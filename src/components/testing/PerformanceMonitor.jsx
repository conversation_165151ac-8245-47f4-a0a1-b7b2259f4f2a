import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import {
  BoltIcon,
  CpuChipIcon,
  ClockIcon,
  SignalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { testingAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function PerformanceMonitor({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isAdmin } = useRoleAccess();
  
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds
  const [performanceHistory, setPerformanceHistory] = useState([]);
  
  const intervalRef = useRef(null);

  useEffect(() => {
    if (hasPermission('monitoring.view')) {
      fetchPerformanceMetrics();
      
      if (autoRefresh) {
        intervalRef.current = setInterval(fetchPerformanceMetrics, refreshInterval);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [hasPermission, autoRefresh, refreshInterval]);

  const fetchPerformanceMetrics = async () => {
    try {
      setLoading(true);
      const response = await testingAPI.getPerformanceMetrics();
      
      if (response.data.success) {
        const newMetrics = response.data.metrics;
        setMetrics(newMetrics);
        
        // Add to history for trending
        setPerformanceHistory(prev => {
          const newHistory = [...prev, {
            timestamp: Date.now(),
            ...newMetrics
          }].slice(-20); // Keep last 20 data points
          return newHistory;
        });
      }
    } catch (error) {
      logger.error('Failed to fetch performance metrics:', error);
      if (!metrics) { // Only show error if we don't have any data
        toast.error('Failed to load performance metrics');
      }
    } finally {
      setLoading(false);
    }
  };

  const runPerformanceTest = async (testType) => {
    if (!hasPermission('testing.run')) {
      toast.error('You do not have permission to run performance tests');
      return;
    }

    try {
      setLoading(true);
      const response = await testingAPI.runPerformanceTest({ type: testType });
      
      if (response.data.success) {
        toast.success(`${testType} performance test completed`);
        fetchPerformanceMetrics(); // Refresh metrics
      }
    } catch (error) {
      logger.error('Performance test failed:', error);
      toast.error('Performance test failed');
    } finally {
      setLoading(false);
    }
  };

  const getMetricStatus = (value, thresholds) => {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.warning) return 'warning';
    return 'critical';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'good':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'good':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const renderMetricCard = (title, value, unit, icon: Icon, thresholds, description) => {
    const status = getMetricStatus(value, thresholds);
    const statusColor = getStatusColor(status);
    
    return (
      <div className={`p-4 rounded-lg border ${statusColor}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Icon className="h-5 w-5" />
            <span className="text-sm font-medium">{title}</span>
          </div>
          {getStatusIcon(status)}
        </div>
        
        <div className="mb-2">
          <span className="text-2xl font-bold">{value}</span>
          <span className="text-sm text-gray-500 ml-1">{unit}</span>
        </div>
        
        <p className="text-xs text-gray-600">{description}</p>
      </div>
    );
  };

  const renderTrendChart = (data, key, label) => {
    if (!data || data.length < 2) return null;
    
    const values = data.map(d => d[key]).filter(v => v !== undefined);
    const max = Math.max(...values);
    const min = Math.min(...values);
    const range = max - min;
    
    return (
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">{label} Trend</h4>
        <div className="h-20 flex items-end space-x-1">
          {values.map((value, index) => {
            const height = range === 0 ? 50 : ((value - min) / range) * 100;
            return (
              <div
                key={index}
                className="flex-1 bg-blue-500 rounded-t"
                style={{ height: `${Math.max(height, 5)}%` }}
                title={`${value}${key.includes('time') ? 'ms' : key.includes('memory') ? 'MB' : ''}`}
              />
            );
          })}
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-2">
          <span>Min: {min}</span>
          <span>Max: {max}</span>
        </div>
      </div>
    );
  };

  if (!hasPermission('monitoring.view')) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <BoltIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">You don't have permission to view performance metrics.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BoltIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Performance Monitor</h1>
            <p className="text-gray-600 mt-1">
              Real-time system performance monitoring
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">Auto-refresh</span>
          </label>

          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            disabled={!autoRefresh}
            className="block px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
          >
            <option value={1000}>1s</option>
            <option value={5000}>5s</option>
            <option value={10000}>10s</option>
            <option value={30000}>30s</option>
          </select>

          <button
            onClick={fetchPerformanceMetrics}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {renderMetricCard(
            'Response Time',
            metrics.response_time,
            'ms',
            ClockIcon,
            { good: 200, warning: 500 },
            'Average API response time'
          )}
          
          {renderMetricCard(
            'Memory Usage',
            metrics.memory_usage,
            'MB',
            CpuChipIcon,
            { good: 512, warning: 1024 },
            'Current memory consumption'
          )}
          
          {renderMetricCard(
            'CPU Usage',
            metrics.cpu_usage,
            '%',
            CpuChipIcon,
            { good: 50, warning: 80 },
            'Current CPU utilization'
          )}
          
          {renderMetricCard(
            'Active Connections',
            metrics.active_connections,
            '',
            SignalIcon,
            { good: 100, warning: 500 },
            'Current active connections'
          )}
        </div>
      )}

      {/* Performance Tests */}
      {hasPermission('testing.run') && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Performance Tests</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => runPerformanceTest('load')}
              disabled={loading}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
            >
              <div className="text-center">
                <BoltIcon className="mx-auto h-8 w-8 text-blue-600 mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Load Test</h3>
                <p className="text-xs text-gray-500 mt-1">Test system under load</p>
              </div>
            </button>
            
            <button
              onClick={() => runPerformanceTest('stress')}
              disabled={loading}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
            >
              <div className="text-center">
                <ExclamationTriangleIcon className="mx-auto h-8 w-8 text-yellow-600 mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Stress Test</h3>
                <p className="text-xs text-gray-500 mt-1">Test system limits</p>
              </div>
            </button>
            
            <button
              onClick={() => runPerformanceTest('endurance')}
              disabled={loading}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
            >
              <div className="text-center">
                <ClockIcon className="mx-auto h-8 w-8 text-green-600 mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Endurance Test</h3>
                <p className="text-xs text-gray-500 mt-1">Long-running stability test</p>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Performance Trends */}
      {performanceHistory.length > 1 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderTrendChart(performanceHistory, 'response_time', 'Response Time')}
          {renderTrendChart(performanceHistory, 'memory_usage', 'Memory Usage')}
          {renderTrendChart(performanceHistory, 'cpu_usage', 'CPU Usage')}
          {renderTrendChart(performanceHistory, 'active_connections', 'Active Connections')}
        </div>
      )}

      {/* System Health */}
      {metrics && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <ChartBarIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">System Health</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {metrics.uptime ? formatDuration(metrics.uptime) : 'N/A'}
              </div>
              <div className="text-sm text-gray-500">Uptime</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {metrics.requests_per_minute || 0}
              </div>
              <div className="text-sm text-gray-500">Requests/min</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {metrics.error_rate ? `${metrics.error_rate}%` : '0%'}
              </div>
              <div className="text-sm text-gray-500">Error Rate</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PerformanceMonitor;
