import React from 'react';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  MinusIcon
} from '@heroicons/react/24/outline';

function MetricCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  color = 'blue',
  subtitle,
  loading = false,
  className = '' 
}) {
  const getColorClasses = (color) => {
    const colors = {
      blue: {
        bg: 'bg-blue-50',
        text: 'text-blue-600',
        border: 'border-blue-200'
      },
      green: {
        bg: 'bg-green-50',
        text: 'text-green-600',
        border: 'border-green-200'
      },
      yellow: {
        bg: 'bg-yellow-50',
        text: 'text-yellow-600',
        border: 'border-yellow-200'
      },
      purple: {
        bg: 'bg-purple-50',
        text: 'text-purple-600',
        border: 'border-purple-200'
      },
      red: {
        bg: 'bg-red-50',
        text: 'text-red-600',
        border: 'border-red-200'
      },
      gray: {
        bg: 'bg-gray-50',
        text: 'text-gray-600',
        border: 'border-gray-200'
      }
    };
    return colors[color] || colors.blue;
  };

  const getChangeIcon = (change) => {
    const changeNum = parseFloat(change);
    if (changeNum > 0) {
      return <TrendingUpIcon className="h-4 w-4 text-green-500" />;
    } else if (changeNum < 0) {
      return <TrendingDownIcon className="h-4 w-4 text-red-500" />;
    } else {
      return <MinusIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getChangeColor = (change) => {
    const changeNum = parseFloat(change);
    if (changeNum > 0) {
      return 'text-green-600';
    } else if (changeNum < 0) {
      return 'text-red-600';
    } else {
      return 'text-gray-500';
    }
  };

  const colorClasses = getColorClasses(color);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
            <div className="ml-4 flex-1">
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="h-3 bg-gray-200 rounded w-12"></div>
            <div className="h-3 bg-gray-200 rounded w-16 ml-2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 ${className}`}>
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses.bg} ${colorClasses.border} border`}>
          <Icon className={`h-6 w-6 ${colorClasses.text}`} />
        </div>
        
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
      </div>

      {change !== undefined && change !== null && (
        <div className="mt-4 flex items-center">
          {getChangeIcon(change)}
          <span className={`ml-1 text-sm font-medium ${getChangeColor(change)}`}>
            {Math.abs(parseFloat(change))}%
          </span>
          <span className="text-sm text-gray-500 ml-1">
            vs previous period
          </span>
        </div>
      )}
    </div>
  );
}

export default MetricCard;
