{"env": {"browser": true, "es6": true, "jest": true}, "parser": "@babel/eslint-parser", "parserOptions": {"requireConfigFile": false, "ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module"}, "extends": ["airbnb", "plugin:react/recommended", "plugin:react-hooks/recommended"], "plugins": ["react", "react-refresh"], "rules": {"react/jsx-filename-extension": ["warn", {"extensions": [".js", ".jsx"]}], "react/react-in-jsx-scope": "off", "import/no-unresolved": "off", "no-shadow": "off", "react-refresh/only-export-components": ["warn", {"allowConstantExport": true}]}, "overrides": [{"files": ["src/**/*Slice.js"], "rules": {"no-param-reassign": ["error", {"props": false}]}}], "ignorePatterns": ["dist/", "build/"]}