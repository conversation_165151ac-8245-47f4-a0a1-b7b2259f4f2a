import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  WrenchScrewdriverIcon,
  CameraIcon,
  CalculatorIcon,
  ChartBarIcon,
  BellIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ClockIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { toolsAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import ImageSearchTool from './ImageSearchTool';
import PriceCalculator from './PriceCalculator';
import MarketAnalyzer from './MarketAnalyzer';
import AutomationTools from './AutomationTools';
import SecurityTools from './SecurityTools';
import logger from '../helpers/logger';

function AdvancedTools({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isSeller, isAdmin } = useRoleAccess();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [tools, setTools] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchAvailableTools();
  }, []);

  const fetchAvailableTools = async () => {
    try {
      setLoading(true);
      const response = await toolsAPI.getAvailableTools();
      
      if (response.data.success) {
        setTools(response.data.tools);
      }
    } catch (error) {
      logger.error('Failed to fetch tools:', error);
      toast.error('Failed to load tools');
    } finally {
      setLoading(false);
    }
  };

  const getAvailableTools = () => {
    const allTools = [
      {
        id: 'image_search',
        name: 'Image Search',
        description: 'Search for products using images',
        icon: CameraIcon,
        color: 'blue',
        category: 'search',
        permissions: ['search.image'],
        component: ImageSearchTool
      },
      {
        id: 'price_calculator',
        name: 'Price Calculator',
        description: 'Calculate optimal pricing with fees',
        icon: CalculatorIcon,
        color: 'green',
        category: 'pricing',
        permissions: ['pricing.calculate'],
        component: PriceCalculator
      },
      {
        id: 'market_analyzer',
        name: 'Market Analyzer',
        description: 'Analyze market trends and competition',
        icon: ChartBarIcon,
        color: 'purple',
        category: 'analytics',
        permissions: ['analytics.market'],
        component: MarketAnalyzer
      },
      {
        id: 'automation_tools',
        name: 'Automation Tools',
        description: 'Automate repetitive tasks',
        icon: ClockIcon,
        color: 'yellow',
        category: 'automation',
        permissions: ['automation.manage'],
        component: AutomationTools
      },
      {
        id: 'security_tools',
        name: 'Security Tools',
        description: 'Security and fraud prevention',
        icon: ShieldCheckIcon,
        color: 'red',
        category: 'security',
        permissions: ['security.manage'],
        component: SecurityTools
      }
    ];

    // Filter tools based on user permissions
    return allTools.filter(tool => {
      if (!tool.permissions || tool.permissions.length === 0) return true;
      return tool.permissions.some(permission => hasPermission(permission));
    });
  };

  const getToolsByCategory = () => {
    const availableTools = getAvailableTools();
    const categories = {};
    
    availableTools.forEach(tool => {
      const category = tool.category || 'other';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(tool);
    });
    
    return categories;
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'search':
        return MagnifyingGlassIcon;
      case 'pricing':
        return CurrencyDollarIcon;
      case 'analytics':
        return ChartBarIcon;
      case 'automation':
        return ClockIcon;
      case 'security':
        return ShieldCheckIcon;
      default:
        return WrenchScrewdriverIcon;
    }
  };

  const getCategoryName = (category) => {
    switch (category) {
      case 'search':
        return 'Search Tools';
      case 'pricing':
        return 'Pricing Tools';
      case 'analytics':
        return 'Analytics Tools';
      case 'automation':
        return 'Automation';
      case 'security':
        return 'Security';
      default:
        return 'Other Tools';
    }
  };

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      red: 'bg-red-50 text-red-600 border-red-200',
      gray: 'bg-gray-50 text-gray-600 border-gray-200'
    };
    return colors[color] || colors.gray;
  };

  const renderToolCard = (tool) => {
    const Icon = tool.icon;
    const colorClasses = getColorClasses(tool.color);
    
    return (
      <div
        key={tool.id}
        onClick={() => setActiveTab(tool.id)}
        className={`p-6 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
          activeTab === tool.id 
            ? `${colorClasses} border-2` 
            : 'bg-white border-gray-200 hover:border-gray-300'
        }`}
      >
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${colorClasses}`}>
            <Icon className="h-6 w-6" />
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {tool.name}
            </h3>
            <p className="text-sm text-gray-600">
              {tool.description}
            </p>
          </div>
        </div>
      </div>
    );
  };

  const renderActiveToolComponent = () => {
    const availableTools = getAvailableTools();
    const activeTool = availableTools.find(tool => tool.id === activeTab);
    
    if (!activeTool || !activeTool.component) {
      return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <WrenchScrewdriverIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Tool</h3>
          <p className="text-gray-500">Choose a tool from the sidebar to get started</p>
        </div>
      );
    }
    
    const ToolComponent = activeTool.component;
    return <ToolComponent />;
  };

  const toolsByCategory = getToolsByCategory();
  const availableTools = getAvailableTools();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <WrenchScrewdriverIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Advanced Tools</h1>
            <p className="text-gray-600 mt-1">
              Powerful tools to enhance your PayHold experience
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Available Tools</h2>
            
            {/* Overview Tab */}
            <button
              onClick={() => setActiveTab('overview')}
              className={`w-full text-left p-3 rounded-lg mb-2 transition-colors duration-200 ${
                activeTab === 'overview'
                  ? 'bg-blue-50 text-blue-600 border border-blue-200'
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <DocumentTextIcon className="h-5 w-5" />
                <span className="font-medium">Overview</span>
              </div>
            </button>

            {/* Tool Categories */}
            {Object.entries(toolsByCategory).map(([category, categoryTools]) => {
              const CategoryIcon = getCategoryIcon(category);
              
              return (
                <div key={category} className="mb-4">
                  <div className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700">
                    <CategoryIcon className="h-4 w-4" />
                    <span>{getCategoryName(category)}</span>
                  </div>
                  
                  <div className="space-y-1 ml-2">
                    {categoryTools.map(tool => {
                      const Icon = tool.icon;
                      return (
                        <button
                          key={tool.id}
                          onClick={() => setActiveTab(tool.id)}
                          className={`w-full text-left p-2 rounded-md transition-colors duration-200 ${
                            activeTab === tool.id
                              ? `${getColorClasses(tool.color)}`
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className="h-4 w-4" />
                            <span className="text-sm">{tool.name}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {activeTab === 'overview' ? (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Tools Overview</h2>
                <p className="text-gray-600 mb-6">
                  Access powerful tools designed to enhance your PayHold experience. 
                  These tools help you search more effectively, price competitively, 
                  analyze markets, automate tasks, and maintain security.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableTools.map(tool => renderToolCard(tool))}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Tool Usage</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {availableTools.length}
                    </div>
                    <div className="text-sm text-gray-500">Available Tools</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Object.keys(toolsByCategory).length}
                    </div>
                    <div className="text-sm text-gray-500">Categories</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {user?.tool_usage_count || 0}
                    </div>
                    <div className="text-sm text-gray-500">Times Used</div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            renderActiveToolComponent()
          )}
        </div>
      </div>
    </div>
  );
}

export default AdvancedTools;
