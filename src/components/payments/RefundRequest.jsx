import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { paymentAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';

function RefundRequest({ payment, onClose, onRefundRequested }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      refund_amount: payment?.amount || 0,
      reason: '',
      description: ''
    }
  });

  const watchedAmount = watch('refund_amount');
  const maxRefundAmount = payment?.amount || 0;

  const refundReasons = [
    { value: 'duplicate_payment', label: 'Duplicate Payment' },
    { value: 'service_not_received', label: 'Service Not Received' },
    { value: 'product_not_delivered', label: 'Product Not Delivered' },
    { value: 'product_defective', label: 'Product Defective/Damaged' },
    { value: 'unauthorized_payment', label: 'Unauthorized Payment' },
    { value: 'billing_error', label: 'Billing Error' },
    { value: 'cancelled_order', label: 'Order Cancelled' },
    { value: 'other', label: 'Other (Please specify)' }
  ];

  const onSubmit = async (formData) => {
    if (!showConfirmation) {
      setShowConfirmation(true);
      return;
    }

    try {
      setIsSubmitting(true);

      const refundData = {
        amount: parseFloat(formData.refund_amount),
        reason: formData.reason,
        description: formData.description,
        currency: payment.currency
      };

      const response = await paymentAPI.requestRefund(payment.id, refundData);

      if (response.data.success) {
        toast.success('Refund request submitted successfully!');
        onRefundRequested?.(response.data.refund);
        onClose?.();
      } else {
        throw new Error(response.data.error || 'Failed to submit refund request');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to submit refund request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (showConfirmation) {
      setShowConfirmation(false);
    } else {
      onClose?.();
    }
  };

  const formatCurrency = (amount, currency = 'GHS') => {
    const symbol = currency === 'GHS' ? 'GH₵' : currency === 'USD' ? '$' : currency;
    return `${symbol} ${amount.toFixed(2)}`;
  };

  if (!payment) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <ArrowPathIcon className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Request Refund
              </h3>
              <p className="text-sm text-gray-600">
                Payment Reference: {payment.reference}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {!showConfirmation ? (
          /* Refund Form */
          <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
            {/* Payment Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Payment Information</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Original Amount:</span>
                  <span className="ml-2 font-medium text-gray-900">
                    {formatCurrency(payment.amount, payment.currency)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Payment Date:</span>
                  <span className="ml-2 font-medium text-gray-900">
                    {new Date(payment.paid_at || payment.created_at).toLocaleDateString()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Payment Method:</span>
                  <span className="ml-2 font-medium text-gray-900 capitalize">
                    {payment.payment_method?.replace('_', ' ')}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className="ml-2 font-medium text-green-600 capitalize">
                    {payment.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Refund Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Refund Amount *
              </label>
              <div className="relative">
                <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  max={maxRefundAmount}
                  {...register('refund_amount', {
                    required: 'Refund amount is required',
                    min: { value: 0.01, message: 'Amount must be greater than 0' },
                    max: { value: maxRefundAmount, message: `Amount cannot exceed ${formatCurrency(maxRefundAmount, payment.currency)}` }
                  })}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="0.00"
                />
              </div>
              {errors.refund_amount && (
                <p className="mt-1 text-sm text-red-600">{errors.refund_amount.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Maximum refundable amount: {formatCurrency(maxRefundAmount, payment.currency)}
              </p>
            </div>

            {/* Refund Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Refund *
              </label>
              <select
                {...register('reason', { required: 'Please select a reason' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="">Select a reason...</option>
                {refundReasons.map((reason) => (
                  <option key={reason.value} value={reason.value}>
                    {reason.label}
                  </option>
                ))}
              </select>
              {errors.reason && (
                <p className="mt-1 text-sm text-red-600">{errors.reason.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Details *
              </label>
              <textarea
                {...register('description', { 
                  required: 'Please provide additional details',
                  minLength: { value: 10, message: 'Please provide at least 10 characters' }
                })}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="Please explain why you are requesting this refund..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Warning */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3 mt-0.5" />
                <div className="text-sm">
                  <h4 className="font-medium text-yellow-800 mb-1">Important Information</h4>
                  <ul className="text-yellow-700 space-y-1">
                    <li>• Refund requests are subject to review and approval</li>
                    <li>• Processing time is typically 3-5 business days</li>
                    <li>• Refunds will be credited to your original payment method</li>
                    <li>• Some payment methods may take longer to process</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Review Request
              </button>
            </div>
          </form>
        ) : (
          /* Confirmation */
          <div className="mt-6 space-y-6">
            <div className="text-center">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                Confirm Refund Request
              </h4>
              <p className="text-sm text-gray-600">
                Please review your refund request details before submitting.
              </p>
            </div>

            {/* Confirmation Details */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Refund Amount:</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(parseFloat(watchedAmount), payment.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Reason:</span>
                <span className="font-medium text-gray-900">
                  {refundReasons.find(r => r.value === watch('reason'))?.label}
                </span>
              </div>
              <div className="border-t border-gray-300 pt-3">
                <span className="text-gray-600 block mb-1">Description:</span>
                <p className="text-sm text-gray-900">{watch('description')}</p>
              </div>
            </div>

            {/* Final Warning */}
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-3 mt-0.5" />
                <div className="text-sm text-red-800">
                  <p className="font-medium mb-1">This action cannot be undone</p>
                  <p>Once submitted, your refund request will be reviewed by our team. You will receive updates via email and notifications.</p>
                </div>
              </div>
            </div>

            {/* Confirmation Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
              >
                Back to Edit
              </button>
              <button
                onClick={handleSubmit(onSubmit)}
                disabled={isSubmitting}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="small" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Submit Refund Request
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default RefundRequest;
