import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ShoppingCartIcon, UserIcon, BellIcon } from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';

function Header() {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <header>
      <div>
        <div>
          {/* Logo */}
          <Link to="/">
            <img src="/logo.png" alt="PayHold" />
            <span>PayHold</span>
          </Link>

          {/* Navigation */}
          <nav>
            <Link to="/products">
              Products
            </Link>
            <Link to="/how-it-works">
              How It Works
            </Link>
            <Link to="/support">
              Support
            </Link>
          </nav>

          {/* User Actions */}
          <div>
            {isAuthenticated ? (
              <>
                <Link to="/cart">
                  <ShoppingCartIcon />
                  {/* Cart count badge */}
                </Link>

                <Link to="/notifications">
                  <BellIcon />
                </Link>

                <div>
                  <button
                    type="button"
                    onClick={() => setShowProfileMenu(!showProfileMenu)}
                  >
                    <UserIcon />
                    <span>{user?.first_name}</span>
                  </button>

                  {showProfileMenu && (
                    <div>
                      <Link
                        to="/dashboard"
                        onClick={() => setShowProfileMenu(false)}
                      >
                        Dashboard
                      </Link>
                      <Link
                        to="/profile"
                        onClick={() => setShowProfileMenu(false)}
                      >
                        Profile
                      </Link>
                      <button
                        type="button"
                        onClick={handleLogout}
                      >
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="space-x-2">
                <Link
                  to="/login"
                >
                  Login
                </Link>
                <Link
                  to="/register"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
