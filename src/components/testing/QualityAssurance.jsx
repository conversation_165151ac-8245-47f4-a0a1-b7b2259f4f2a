import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  ShieldCheckIcon,
  CodeBracketIcon,
  BugAntIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { testingAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function QualityAssurance({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isAdmin } = useRoleAccess();
  
  const [qualityReports, setQualityReports] = useState({});
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [runningScans, setRunningScans] = useState(new Set());

  useEffect(() => {
    if (hasPermission('qa.view')) {
      fetchQualityReports();
    }
  }, [hasPermission]);

  const fetchQualityReports = async () => {
    try {
      setLoading(true);
      const response = await testingAPI.getQualityReports();
      
      if (response.data.success) {
        setQualityReports(response.data.reports);
      }
    } catch (error) {
      logger.error('Failed to fetch quality reports:', error);
      toast.error('Failed to load quality reports');
    } finally {
      setLoading(false);
    }
  };

  const runQualityCheck = async (checkType) => {
    if (!hasPermission('qa.run')) {
      toast.error('You do not have permission to run quality checks');
      return;
    }

    try {
      setRunningScans(prev => new Set([...prev, checkType]));
      
      const response = await testingAPI.runQualityCheck({ type: checkType });
      
      if (response.data.success) {
        setQualityReports(prev => ({
          ...prev,
          [checkType]: response.data.report
        }));
        
        const { issues, warnings } = response.data.report.summary;
        if (issues === 0 && warnings === 0) {
          toast.success(`${checkType} check passed with no issues`);
        } else {
          toast.warning(`${checkType} check found ${issues} issues and ${warnings} warnings`);
        }
      }
    } catch (error) {
      logger.error('Quality check failed:', error);
      toast.error('Quality check failed');
    } finally {
      setRunningScans(prev => {
        const newSet = new Set(prev);
        newSet.delete(checkType);
        return newSet;
      });
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'info':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <ExclamationTriangleIcon className="h-4 w-4 text-blue-500" />;
      case 'info':
        return <EyeIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
    }
  };

  const renderQualityCard = (title, report, icon: Icon, checkType) => {
    const isRunning = runningScans.has(checkType);
    const hasIssues = report && (report.summary.issues > 0 || report.summary.warnings > 0);
    
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Icon className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              {report && (
                <p className="text-sm text-gray-500">
                  Last scan: {new Date(report.timestamp).toLocaleString()}
                </p>
              )}
            </div>
          </div>

          {hasPermission('qa.run') && (
            <button
              onClick={() => runQualityCheck(checkType)}
              disabled={isRunning}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              {isRunning ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Scanning...
                </>
              ) : (
                <>
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Scan
                </>
              )}
            </button>
          )}
        </div>

        {report ? (
          <div className="space-y-4">
            {/* Summary */}
            <div className={`p-3 rounded-lg border ${
              hasIssues ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {hasIssues ? (
                    <XCircleIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    hasIssues ? 'text-red-900' : 'text-green-900'
                  }`}>
                    {hasIssues ? 'Issues Found' : 'All Clear'}
                  </span>
                </div>
                
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    hasIssues ? 'text-red-900' : 'text-green-900'
                  }`}>
                    {report.summary.issues} issues, {report.summary.warnings} warnings
                  </div>
                  <div className="text-xs text-gray-500">
                    {report.summary.files_scanned} files scanned
                  </div>
                </div>
              </div>
            </div>

            {/* Issues List */}
            {report.issues && report.issues.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Issues</h4>
                <div className="max-h-48 overflow-y-auto space-y-2">
                  {report.issues.slice(0, 10).map((issue, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${getSeverityColor(issue.severity)}`}
                    >
                      <div className="flex items-start space-x-2">
                        {getSeverityIcon(issue.severity)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {issue.title}
                          </p>
                          <p className="text-xs text-gray-600 mt-1">
                            {issue.description}
                          </p>
                          {issue.file && (
                            <p className="text-xs text-gray-500 mt-1">
                              {issue.file}:{issue.line}
                            </p>
                          )}
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          getSeverityColor(issue.severity)
                        }`}>
                          {issue.severity}
                        </span>
                      </div>
                    </div>
                  ))}
                  
                  {report.issues.length > 10 && (
                    <div className="text-center">
                      <button
                        onClick={() => setActiveTab(checkType)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        View all {report.issues.length} issues
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metrics */}
            {report.metrics && (
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                {Object.entries(report.metrics).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-lg font-semibold text-gray-900">{value}</div>
                    <div className="text-xs text-gray-500 capitalize">
                      {key.replace('_', ' ')}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Report Available</h4>
            <p className="text-gray-500">Run a scan to generate a quality report</p>
          </div>
        )}
      </div>
    );
  };

  const renderOverview = () => {
    const totalIssues = Object.values(qualityReports).reduce((sum, report) => 
      sum + (report?.summary?.issues || 0), 0);
    const totalWarnings = Object.values(qualityReports).reduce((sum, report) => 
      sum + (report?.summary?.warnings || 0), 0);
    const reportsCount = Object.keys(qualityReports).length;

    return (
      <div className="space-y-6">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-2xl font-bold text-gray-900">{reportsCount}</div>
            <div className="text-sm text-gray-500">Quality Checks</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-2xl font-bold text-red-600">{totalIssues}</div>
            <div className="text-sm text-gray-500">Total Issues</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-2xl font-bold text-yellow-600">{totalWarnings}</div>
            <div className="text-sm text-gray-500">Total Warnings</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-2xl font-bold text-green-600">
              {totalIssues === 0 ? '100%' : Math.round((1 - totalIssues / 100) * 100) + '%'}
            </div>
            <div className="text-sm text-gray-500">Quality Score</div>
          </div>
        </div>

        {/* Quality Checks */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderQualityCard(
            'Code Quality',
            qualityReports.code_quality,
            CodeBracketIcon,
            'code_quality'
          )}
          
          {renderQualityCard(
            'Security Scan',
            qualityReports.security,
            ShieldCheckIcon,
            'security'
          )}
          
          {renderQualityCard(
            'Bug Detection',
            qualityReports.bugs,
            BugAntIcon,
            'bugs'
          )}
          
          {renderQualityCard(
            'Documentation',
            qualityReports.documentation,
            DocumentTextIcon,
            'documentation'
          )}
        </div>
      </div>
    );
  };

  if (!hasPermission('qa.view')) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">You don't have permission to view quality assurance reports.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Quality Assurance</h1>
            <p className="text-gray-600 mt-1">
              Code quality, security, and documentation checks
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchQualityReports}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>

          {hasPermission('qa.run') && (
            <button
              onClick={() => {
                runQualityCheck('code_quality');
                runQualityCheck('security');
                runQualityCheck('bugs');
                runQualityCheck('documentation');
              }}
              disabled={runningScans.size > 0}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {runningScans.size > 0 ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Running Scans...
                </>
              ) : (
                <>
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Run All Checks
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      {renderOverview()}
    </div>
  );
}

export default QualityAssurance;
