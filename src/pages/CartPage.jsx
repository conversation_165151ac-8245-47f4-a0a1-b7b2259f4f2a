import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  ShoppingCartIcon,
  BookmarkIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';
import { updateCartItem, removeFromCart, clearCart } from '../redux/slice/cartSlice';
import useAuth from '../hooks/useAuth';
import CartItem from '../components/cart/CartItem';
import CartSummary from '../components/cart/CartSummary';
import SavedItems from '../components/cart/SavedItems';
import LoadingSpinner from '../components/helpers/LoadingSpinner';
import logger from '../components/helpers/logger';

function CartPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    items, total, itemCount, loading,
  } = useSelector((state) => state.cart);

  const [activeTab, setActiveTab] = useState('cart');
  const [savedItems, setSavedItems] = useState([]);
  const [appliedCoupon, setAppliedCoupon] = useState(null);
  const [updatingItems, setUpdatingItems] = useState(new Set());

  useEffect(() => {
    // Fetch saved items when component mounts
    if (user && activeTab === 'saved') {
      fetchSavedItems();
    }
  }, [user, activeTab]);

  const fetchSavedItems = async () => {
    try {
      // TODO: Implement API call to fetch saved items
      // const response = await cartAPI.getSavedItems();
      // setSavedItems(response.data.items);
      setSavedItems([]); // Placeholder
    } catch (error) {
      logger.error('Failed to fetch saved items:', error);
    }
  };

  const handleUpdateQuantity = async (itemId, quantity) => {
    setUpdatingItems((prev) => new Set(prev).add(itemId));

    try {
      if (quantity <= 0) {
        dispatch(removeFromCart(itemId));
        toast.success('Item removed from cart');
      } else {
        dispatch(updateCartItem({ id: itemId, quantity }));
        toast.success('Cart updated');
      }
    } catch (error) {
      toast.error('Failed to update cart');
    } finally {
      setUpdatingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleRemoveItem = async (itemId) => {
    setUpdatingItems((prev) => new Set(prev).add(itemId));

    try {
      dispatch(removeFromCart(itemId));
      toast.success('Item removed from cart');
    } catch (error) {
      toast.error('Failed to remove item');
    } finally {
      setUpdatingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleSaveForLater = async (itemId) => {
    setUpdatingItems((prev) => new Set(prev).add(itemId));

    try {
      // TODO: Implement API call to save item for later
      // await cartAPI.saveForLater(itemId);

      // For now, just remove from cart
      dispatch(removeFromCart(itemId));
      toast.success('Item saved for later');
    } catch (error) {
      toast.error('Failed to save item for later');
    } finally {
      setUpdatingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleApplyCoupon = async (couponCode) => {
    if (!couponCode) {
      setAppliedCoupon(null);
      return;
    }

    try {
      // TODO: Implement API call to apply coupon
      // const response = await cartAPI.applyCoupon(couponCode);
      // setAppliedCoupon(response.data.coupon);

      // Placeholder for demo
      if (couponCode === 'SAVE10') {
        setAppliedCoupon({
          code: 'SAVE10',
          discount_percentage: 10,
        });
        toast.success('Coupon applied successfully!');
      } else {
        throw new Error('Invalid coupon code');
      }
    } catch (error) {
      throw error;
    }
  };

  const handleProceedToCheckout = () => {
    if (!user) {
      toast.error('Please login to proceed to checkout');
      navigate('/login', { state: { from: '/cart' } });
      return;
    }

    navigate('/checkout');
  };

  const handleMoveToCart = async (itemId) => {
    try {
      // TODO: Implement API call to move saved item to cart
      // await cartAPI.moveToCart(itemId);

      // Remove from saved items
      setSavedItems((prev) => prev.filter((item) => item.id !== itemId));
      toast.success('Item moved to cart');
    } catch (error) {
      throw error;
    }
  };

  const handleRemoveSavedItem = async (itemId) => {
    try {
      // TODO: Implement API call to remove saved item
      // await cartAPI.removeSavedItem(itemId);

      setSavedItems((prev) => prev.filter((item) => item.id !== itemId));
    } catch (error) {
      throw error;
    }
  };

  const handleToggleWishlist = async (itemId, isWishlisted) => {
    try {
      // TODO: Implement API call to toggle wishlist
      // await wishlistAPI.toggle(itemId, isWishlisted);

      setSavedItems((prev) => prev.map((item) => (item.id === itemId ? { ...item, is_wishlisted: isWishlisted } : item)));
    } catch (error) {
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
              <p className="text-sm text-gray-600 mt-1">
                {activeTab === 'cart'
                  ? `${itemCount} item${itemCount !== 1 ? 's' : ''} in your cart`
                  : `${savedItems.length} item${savedItems.length !== 1 ? 's' : ''} saved for later`}
              </p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('cart')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'cart'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <ShoppingCartIcon className="h-4 w-4 inline mr-2" />
              Cart (
              {itemCount}
              )
            </button>
            <button
              onClick={() => setActiveTab('saved')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'saved'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <BookmarkIcon className="h-4 w-4 inline mr-2" />
              Saved (
              {savedItems.length}
              )
            </button>
          </div>
        </div>

        {/* Cart Tab */}
        {activeTab === 'cart' && (
          <>
            {items.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCartIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h2 className="mt-2 text-lg font-medium text-gray-900">Your cart is empty</h2>
                <p className="mt-1 text-sm text-gray-500">
                  Start adding some items to your cart!
                </p>
                <div className="mt-6">
                  <Link
                    to="/products"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Continue Shopping
                  </Link>
                </div>
              </div>
            ) : (
              <div className="lg:grid lg:grid-cols-12 lg:gap-x-8">
                {/* Cart Items */}
                <div className="lg:col-span-8">
                  <div className="space-y-4">
                    {items.map((item) => (
                      <CartItem
                        key={item.id}
                        item={item}
                        onUpdateQuantity={handleUpdateQuantity}
                        onRemoveItem={handleRemoveItem}
                        onSaveForLater={handleSaveForLater}
                        isUpdating={updatingItems.has(item.id)}
                      />
                    ))}
                  </div>

                  {/* Clear Cart */}
                  <div className="mt-6 flex justify-between items-center">
                    <Link
                      to="/products"
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      ← Continue Shopping
                    </Link>
                    <button
                      onClick={() => {
                        dispatch(clearCart());
                        toast.success('Cart cleared');
                      }}
                      className="text-sm text-red-600 hover:text-red-800"
                    >
                      Clear Cart
                    </button>
                  </div>
                </div>

                {/* Cart Summary */}
                <div className="lg:col-span-4 mt-8 lg:mt-0">
                  <CartSummary
                    items={items}
                    onApplyCoupon={handleApplyCoupon}
                    onProceedToCheckout={handleProceedToCheckout}
                    appliedCoupon={appliedCoupon}
                    isLoading={loading}
                  />
                </div>
              </div>
            )}
          </>
        )}

        {/* Saved Items Tab */}
        {activeTab === 'saved' && (
          <SavedItems
            savedItems={savedItems}
            onMoveToCart={handleMoveToCart}
            onRemoveItem={handleRemoveSavedItem}
            onToggleWishlist={handleToggleWishlist}
            isLoading={loading}
          />
        )}
      </div>
    </div>
  );
}

export default CartPage;
