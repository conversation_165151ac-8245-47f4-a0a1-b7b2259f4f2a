import React from 'react';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  UserIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

function StatusTimeline({ transaction }) {
  if (!transaction) {
    return null;
  }

  // Define timeline events based on transaction data
  const getTimelineEvents = () => {
    const events = [];

    // Transaction Created
    events.push({
      id: 'created',
      title: 'Transaction Created',
      description: `${transaction.buyer?.first_name} created a transaction for ${transaction.product_name}`,
      timestamp: transaction.created_at,
      icon: DocumentTextIcon,
      status: 'completed',
      color: 'blue'
    });

    // Seller Acceptance
    if (transaction.accepted_at) {
      events.push({
        id: 'accepted',
        title: 'Transaction Accepted',
        description: `${transaction.seller?.first_name} accepted the transaction`,
        timestamp: transaction.accepted_at,
        icon: CheckCircleIcon,
        status: 'completed',
        color: 'green'
      });
    } else if (transaction.status === 'pending') {
      events.push({
        id: 'pending_acceptance',
        title: 'Waiting for Seller',
        description: '<PERSON><PERSON> needs to accept the transaction',
        timestamp: null,
        icon: ClockIcon,
        status: 'pending',
        color: 'yellow'
      });
    }

    // Escrow Funding
    if (transaction.funded_at) {
      events.push({
        id: 'funded',
        title: 'Escrow Funded',
        description: `Payment of GH₵ ${transaction.total_amount?.toFixed(2)} secured in escrow`,
        timestamp: transaction.funded_at,
        icon: ShieldCheckIcon,
        status: 'completed',
        color: 'green'
      });
    } else if (['accepted', 'funded', 'shipped', 'delivered', 'completed'].includes(transaction.status)) {
      events.push({
        id: 'pending_funding',
        title: 'Waiting for Payment',
        description: 'Buyer needs to fund the escrow account',
        timestamp: null,
        icon: CurrencyDollarIcon,
        status: transaction.status === 'accepted' ? 'pending' : 'completed',
        color: transaction.status === 'accepted' ? 'yellow' : 'green'
      });
    }

    // Shipping
    if (transaction.shipped_at) {
      events.push({
        id: 'shipped',
        title: 'Item Shipped',
        description: transaction.tracking_number 
          ? `Item shipped with tracking number: ${transaction.tracking_number}`
          : 'Item has been shipped by the seller',
        timestamp: transaction.shipped_at,
        icon: TruckIcon,
        status: 'completed',
        color: 'purple'
      });
    } else if (['funded', 'shipped', 'delivered', 'completed'].includes(transaction.status)) {
      events.push({
        id: 'pending_shipping',
        title: 'Preparing for Shipment',
        description: 'Seller is preparing the item for shipment',
        timestamp: null,
        icon: TruckIcon,
        status: transaction.status === 'funded' ? 'pending' : 'completed',
        color: transaction.status === 'funded' ? 'yellow' : 'purple'
      });
    }

    // Delivery
    if (transaction.delivered_at) {
      events.push({
        id: 'delivered',
        title: 'Item Delivered',
        description: 'Item has been delivered to the buyer',
        timestamp: transaction.delivered_at,
        icon: CheckCircleIcon,
        status: 'completed',
        color: 'green'
      });
    } else if (['shipped', 'delivered', 'completed'].includes(transaction.status)) {
      events.push({
        id: 'pending_delivery',
        title: 'In Transit',
        description: 'Item is on its way to the buyer',
        timestamp: null,
        icon: TruckIcon,
        status: transaction.status === 'shipped' ? 'pending' : 'completed',
        color: transaction.status === 'shipped' ? 'yellow' : 'green'
      });
    }

    // Completion
    if (transaction.completed_at) {
      events.push({
        id: 'completed',
        title: 'Transaction Completed',
        description: 'Buyer confirmed delivery and payment has been released to seller',
        timestamp: transaction.completed_at,
        icon: CheckCircleIcon,
        status: 'completed',
        color: 'green'
      });
    } else if (['delivered', 'completed'].includes(transaction.status)) {
      events.push({
        id: 'pending_completion',
        title: 'Awaiting Confirmation',
        description: 'Waiting for buyer to confirm delivery',
        timestamp: null,
        icon: ClockIcon,
        status: transaction.status === 'delivered' ? 'pending' : 'completed',
        color: transaction.status === 'delivered' ? 'yellow' : 'green'
      });
    }

    // Handle special statuses
    if (transaction.status === 'cancelled') {
      events.push({
        id: 'cancelled',
        title: 'Transaction Cancelled',
        description: transaction.cancellation_reason || 'Transaction was cancelled',
        timestamp: transaction.cancelled_at || transaction.updated_at,
        icon: XCircleIcon,
        status: 'completed',
        color: 'red'
      });
    }

    if (transaction.status === 'disputed') {
      events.push({
        id: 'disputed',
        title: 'Dispute Opened',
        description: 'A dispute has been opened for this transaction',
        timestamp: transaction.disputed_at || transaction.updated_at,
        icon: ExclamationTriangleIcon,
        status: 'completed',
        color: 'red'
      });
    }

    // Add chat messages if available
    if (transaction.recent_messages && transaction.recent_messages.length > 0) {
      transaction.recent_messages.slice(0, 3).forEach((message, index) => {
        events.push({
          id: `message_${index}`,
          title: 'Message',
          description: `${message.sender_name}: ${message.content.substring(0, 50)}${message.content.length > 50 ? '...' : ''}`,
          timestamp: message.created_at,
          icon: ChatBubbleLeftRightIcon,
          status: 'completed',
          color: 'gray',
          isMessage: true
        });
      });
    }

    // Sort events by timestamp (null timestamps go to end)
    return events.sort((a, b) => {
      if (!a.timestamp && !b.timestamp) return 0;
      if (!a.timestamp) return 1;
      if (!b.timestamp) return -1;
      return new Date(a.timestamp) - new Date(b.timestamp);
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusStyles = (status, color) => {
    const baseStyles = {
      completed: {
        iconBg: `bg-${color}-100`,
        iconColor: `text-${color}-600`,
        lineColor: `bg-${color}-200`
      },
      pending: {
        iconBg: 'bg-yellow-100',
        iconColor: 'text-yellow-600',
        lineColor: 'bg-gray-200'
      },
      failed: {
        iconBg: 'bg-red-100',
        iconColor: 'text-red-600',
        lineColor: 'bg-red-200'
      }
    };

    return baseStyles[status] || baseStyles.pending;
  };

  const events = getTimelineEvents();

  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {events.map((event, eventIdx) => {
          const styles = getStatusStyles(event.status, event.color);
          const EventIcon = event.icon;
          const isLast = eventIdx === events.length - 1;

          return (
            <li key={event.id}>
              <div className="relative pb-8">
                {!isLast && (
                  <span
                    className={`absolute top-4 left-4 -ml-px h-full w-0.5 ${styles.lineColor}`}
                    aria-hidden="true"
                  />
                )}
                <div className="relative flex space-x-3">
                  <div>
                    <span
                      className={`h-8 w-8 rounded-full ${styles.iconBg} flex items-center justify-center ring-8 ring-white`}
                    >
                      <EventIcon
                        className={`h-5 w-5 ${styles.iconColor}`}
                        aria-hidden="true"
                      />
                    </span>
                  </div>
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p className={`text-sm font-medium ${
                        event.status === 'pending' ? 'text-gray-500' : 'text-gray-900'
                      } ${event.isMessage ? 'text-gray-600' : ''}`}>
                        {event.title}
                      </p>
                      <p className={`mt-0.5 text-sm ${
                        event.status === 'pending' ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {event.description}
                      </p>
                      
                      {/* Additional details for specific events */}
                      {event.id === 'shipped' && transaction.shipping_carrier && (
                        <p className="mt-1 text-xs text-gray-400">
                          Carrier: {transaction.shipping_carrier}
                        </p>
                      )}
                      
                      {event.id === 'delivered' && transaction.delivery_notes && (
                        <p className="mt-1 text-xs text-gray-400">
                          Notes: {transaction.delivery_notes}
                        </p>
                      )}
                    </div>
                    <div className="whitespace-nowrap text-right text-sm text-gray-500">
                      {event.timestamp ? (
                        <div>
                          <p>{formatDate(event.timestamp)}</p>
                          {event.status === 'pending' && (
                            <div className="flex items-center justify-end mt-1">
                              <div className="animate-pulse h-2 w-2 bg-yellow-400 rounded-full mr-1"></div>
                              <span className="text-xs text-yellow-600">Pending</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center justify-end">
                          <div className="animate-pulse h-2 w-2 bg-yellow-400 rounded-full mr-1"></div>
                          <span className="text-xs text-yellow-600">Pending</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>

      {/* Timeline Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Completed</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span className="text-gray-600">Pending</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 bg-gray-300 rounded-full"></div>
              <span className="text-gray-600">Upcoming</span>
            </div>
          </div>
          
          <div className="text-gray-500">
            Last updated: {formatDate(transaction.updated_at)}
          </div>
        </div>
      </div>
    </div>
  );
}

export default StatusTimeline;
