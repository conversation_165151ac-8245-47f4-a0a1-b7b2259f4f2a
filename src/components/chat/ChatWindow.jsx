import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import {
  PaperAirplaneIcon,
  PhotoIcon,
  PaperClipIcon,
  EllipsisVerticalIcon,
  XMarkIcon,
  MinusIcon,
  ArrowsPointingOutIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { CheckIcon as CheckIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';
import { useSocket } from '../../hooks/useSocket';
import { chatAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function ChatWindow({ 
  conversation, 
  onClose, 
  onMinimize, 
  isMinimized = false,
  className = '' 
}) {
  const { user } = useSelector((state) => state.auth);
  const socket = useSocket();
  
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  useEffect(() => {
    if (conversation?.id) {
      fetchMessages();
      joinConversation();
    }

    return () => {
      if (conversation?.id) {
        leaveConversation();
      }
    };
  }, [conversation?.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (socket) {
      socket.on('message_received', handleMessageReceived);
      socket.on('user_typing', handleUserTyping);
      socket.on('user_stopped_typing', handleUserStoppedTyping);
      socket.on('message_read', handleMessageRead);

      return () => {
        socket.off('message_received', handleMessageReceived);
        socket.off('user_typing', handleUserTyping);
        socket.off('user_stopped_typing', handleUserStoppedTyping);
        socket.off('message_read', handleMessageRead);
      };
    }
  }, [socket]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getMessages(conversation.id);
      
      if (response.data.success) {
        setMessages(response.data.messages);
        // Mark messages as read
        markMessagesAsRead();
      }
    } catch (error) {
      logger.error('Failed to fetch messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const joinConversation = () => {
    if (socket && conversation?.id) {
      socket.emit('join_conversation', { conversation_id: conversation.id });
    }
  };

  const leaveConversation = () => {
    if (socket && conversation?.id) {
      socket.emit('leave_conversation', { conversation_id: conversation.id });
    }
  };

  const markMessagesAsRead = async () => {
    try {
      await chatAPI.markAsRead(conversation.id);
    } catch (error) {
      logger.error('Failed to mark messages as read:', error);
    }
  };

  const handleMessageReceived = (data) => {
    if (data.conversation_id === conversation.id) {
      setMessages(prev => [...prev, data.message]);
      markMessagesAsRead();
    }
  };

  const handleUserTyping = (data) => {
    if (data.conversation_id === conversation.id && data.user_id !== user.id) {
      setOtherUserTyping(true);
    }
  };

  const handleUserStoppedTyping = (data) => {
    if (data.conversation_id === conversation.id && data.user_id !== user.id) {
      setOtherUserTyping(false);
    }
  };

  const handleMessageRead = (data) => {
    if (data.conversation_id === conversation.id) {
      setMessages(prev => prev.map(msg => 
        msg.id === data.message_id ? { ...msg, read_at: data.read_at } : msg
      ));
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() && selectedFiles.length === 0) return;

    try {
      setSending(true);
      
      const messageData = {
        conversation_id: conversation.id,
        content: newMessage.trim(),
        type: selectedFiles.length > 0 ? 'media' : 'text',
        attachments: selectedFiles
      };

      const response = await chatAPI.sendMessage(messageData);
      
      if (response.data.success) {
        const message = response.data.message;
        setMessages(prev => [...prev, message]);
        
        // Emit via socket for real-time delivery
        if (socket) {
          socket.emit('send_message', {
            conversation_id: conversation.id,
            message: message
          });
        }
        
        setNewMessage('');
        setSelectedFiles([]);
        setShowAttachments(false);
      }
    } catch (error) {
      logger.error('Failed to send message:', error);
      toast.error('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (e) => {
    setNewMessage(e.target.value);
    
    if (!isTyping) {
      setIsTyping(true);
      if (socket) {
        socket.emit('typing', { conversation_id: conversation.id });
      }
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      if (socket) {
        socket.emit('stop_typing', { conversation_id: conversation.id });
      }
    }, 1000);
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/') || file.type.startsWith('video/') || 
                         file.type === 'application/pdf' || file.type.startsWith('text/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit
      
      if (!isValidType) {
        toast.error(`${file.name} is not a supported file type`);
        return false;
      }
      
      if (!isValidSize) {
        toast.error(`${file.name} is too large (max 10MB)`);
        return false;
      }
      
      return true;
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);
    e.target.value = '';
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const getOtherUser = () => {
    return conversation?.participants?.find(p => p.id !== user.id);
  };

  const otherUser = getOtherUser();

  if (isMinimized) {
    return (
      <div className={`fixed bottom-0 right-4 w-80 bg-white border border-gray-200 rounded-t-lg shadow-lg ${className}`}>
        <div className="flex items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg cursor-pointer" onClick={onMinimize}>
          <div className="flex items-center space-x-2">
            <img
              src={otherUser?.avatar || '/default-avatar.png'}
              alt={otherUser?.name}
              className="h-6 w-6 rounded-full"
            />
            <span className="text-sm font-medium">{otherUser?.name}</span>
          </div>
          <button onClick={onClose} className="text-white hover:text-gray-200">
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-0 right-4 w-96 h-96 bg-white border border-gray-200 rounded-t-lg shadow-xl flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-blue-600 text-white rounded-t-lg">
        <div className="flex items-center space-x-3">
          <img
            src={otherUser?.avatar || '/default-avatar.png'}
            alt={otherUser?.name}
            className="h-8 w-8 rounded-full"
          />
          <div>
            <h3 className="text-sm font-medium">{otherUser?.name}</h3>
            <p className="text-xs opacity-75">
              {otherUser?.is_online ? 'Online' : `Last seen ${formatTime(otherUser?.last_seen)}`}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={onMinimize}
            className="text-white hover:text-gray-200"
            title="Minimize"
          >
            <MinusIcon className="h-4 w-4" />
          </button>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200"
            title="Close"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwn = message.sender_id === user.id;
              const showDate = index === 0 || 
                formatDate(messages[index - 1].created_at) !== formatDate(message.created_at);
              
              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="text-center text-xs text-gray-500 my-2">
                      {formatDate(message.created_at)}
                    </div>
                  )}
                  
                  <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs px-3 py-2 rounded-lg ${
                      isOwn 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <p className="text-sm">{message.content}</p>
                      
                      <div className={`flex items-center justify-between mt-1 text-xs ${
                        isOwn ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        <span>{formatTime(message.created_at)}</span>
                        {isOwn && (
                          <div className="ml-2">
                            {message.read_at ? (
                              <CheckIconSolid className="h-3 w-3" />
                            ) : (
                              <CheckIcon className="h-3 w-3" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            
            {otherUserTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 px-3 py-2 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* File Attachments Preview */}
      {selectedFiles.length > 0 && (
        <div className="px-4 py-2 border-t border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-2">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center space-x-2 bg-white px-2 py-1 rounded border">
                <span className="text-xs text-gray-600 truncate max-w-20">{file.name}</span>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="text-gray-400 hover:text-gray-600"
                title="Attach file"
              >
                <PaperClipIcon className="h-4 w-4" />
              </button>
              
              <button
                type="button"
                onClick={() => {
                  fileInputRef.current?.click();
                  fileInputRef.current?.setAttribute('accept', 'image/*');
                }}
                className="text-gray-400 hover:text-gray-600"
                title="Attach image"
              >
                <PhotoIcon className="h-4 w-4" />
              </button>
            </div>
            
            <textarea
              value={newMessage}
              onChange={handleTyping}
              placeholder="Type a message..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage(e);
                }
              }}
            />
          </div>
          
          <button
            type="submit"
            disabled={(!newMessage.trim() && selectedFiles.length === 0) || sending}
            className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {sending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <PaperAirplaneIcon className="h-4 w-4" />
            )}
          </button>
        </form>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,.pdf,.txt,.doc,.docx"
        />
      </div>
    </div>
  );
}

export default ChatWindow;
