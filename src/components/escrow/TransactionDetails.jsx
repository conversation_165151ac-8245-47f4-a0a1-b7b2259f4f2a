import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  UserIcon,
  LinkIcon,
  CalendarIcon,
  MapPinIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  TruckIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import LoadingSpinner from '../helpers/LoadingSpinner';

// Mock external transaction API
const externalTransactionAPI = {
  getTransaction: (id) => fetch(`/api/v1/external_transactions/${id}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
  }).then((res) => res.json()),

  acceptTransaction: (id) => fetch(`/api/v1/external_transactions/${id}/accept_transaction`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
  }).then((res) => res.json()),

  fundEscrow: (id, paymentData) => fetch(`/api/v1/external_transactions/${id}/fund_escrow`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
    body: JSON.stringify(paymentData),
  }).then((res) => res.json()),

  confirmDelivery: (id, confirmationData) => fetch(`/api/v1/external_transactions/${id}/confirm_delivery`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
    body: JSON.stringify(confirmationData),
  }).then((res) => res.json()),
};

function TransactionDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchTransaction();
  }, [id]);

  const fetchTransaction = async () => {
    try {
      setLoading(true);
      const response = await externalTransactionAPI.getTransaction(id);

      if (response.success) {
        setTransaction(response.transaction);
        setError(null);
      } else {
        throw new Error(response.error || 'Transaction not found');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptTransaction = async () => {
    try {
      setActionLoading(true);
      const response = await externalTransactionAPI.acceptTransaction(id);

      if (response.success) {
        toast.success('Transaction accepted successfully!');
        setTransaction(response.transaction);
      } else {
        throw new Error(response.error || 'Failed to accept transaction');
      }
    } catch (err) {
      toast.error(err.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleFundEscrow = async () => {
    try {
      setActionLoading(true);
      const paymentData = {
        payment_method: 'card',
        callback_url: `${window.location.origin}/payment/callback`,
      };

      const response = await externalTransactionAPI.fundEscrow(id, paymentData);

      if (response.success) {
        // Redirect to payment gateway
        window.location.href = response.payment.authorization_url;
      } else {
        throw new Error(response.error || 'Failed to initialize payment');
      }
    } catch (err) {
      toast.error(err.message);
      setActionLoading(false);
    }
  };

  const handleConfirmDelivery = async () => {
    try {
      setActionLoading(true);
      const confirmationData = {
        delivery_confirmed: true,
        confirmation_notes: 'Product received in good condition',
      };

      const response = await externalTransactionAPI.confirmDelivery(id, confirmationData);

      if (response.success) {
        toast.success('Delivery confirmed! Payment has been released to the seller.');
        setTransaction(response.transaction);
      } else {
        throw new Error(response.error || 'Failed to confirm delivery');
      }
    } catch (err) {
      toast.error(err.message);
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading transaction</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return null;
  }

  // Status configurations
  const statusConfig = {
    pending: {
      icon: ClockIcon,
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200',
      label: 'Pending Acceptance',
    },
    accepted: {
      icon: CheckCircleIcon,
      color: 'blue',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200',
      label: 'Accepted - Awaiting Payment',
    },
    funded: {
      icon: CurrencyDollarIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Funded - In Progress',
    },
    shipped: {
      icon: TruckIcon,
      color: 'purple',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200',
      label: 'Shipped - Awaiting Delivery',
    },
    delivered: {
      icon: CheckCircleIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Delivered - Awaiting Confirmation',
    },
    completed: {
      icon: CheckCircleIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Completed',
    },
    cancelled: {
      icon: XCircleIcon,
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-800',
      borderColor: 'border-red-200',
      label: 'Cancelled',
    },
    disputed: {
      icon: ExclamationTriangleIcon,
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-800',
      borderColor: 'border-red-200',
      label: 'Disputed',
    },
  };

  const status = statusConfig[transaction.status] || statusConfig.pending;
  const StatusIcon = status.icon;

  // Determine user role in this transaction
  const userRole = transaction.buyer_id === user?.id ? 'buyer' : 'seller';

  // Format date
  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  // Get platform display
  const getPlatformDisplay = (storeName) => {
    const platforms = {
      Facebook: { name: 'Facebook Marketplace', color: 'text-blue-600' },
      TikTok: { name: 'TikTok Shop', color: 'text-black' },
      Instagram: { name: 'Instagram Shop', color: 'text-pink-600' },
      WhatsApp: { name: 'WhatsApp Business', color: 'text-green-600' },
      Telegram: { name: 'Telegram', color: 'text-blue-500' },
      Twitter: { name: 'Twitter/X', color: 'text-black' },
      LinkedIn: { name: 'LinkedIn', color: 'text-blue-700' },
      Snapchat: { name: 'Snapchat', color: 'text-yellow-500' },
      YouTube: { name: 'YouTube', color: 'text-red-600' },
      Discord: { name: 'Discord', color: 'text-indigo-600' },
      Reddit: { name: 'Reddit', color: 'text-orange-600' },
      Pinterest: { name: 'Pinterest', color: 'text-red-500' },
      Offline: { name: 'Offline Deal', color: 'text-gray-700' },
      Phone: { name: 'Phone Call', color: 'text-green-700' },
      InPerson: { name: 'In-Person Meeting', color: 'text-purple-600' },
      Other: { name: 'External Platform', color: 'text-gray-600' },
    };

    // If it's not a predefined platform, treat it as a custom platform name
    if (!platforms[storeName]) {
      return { name: storeName, color: 'text-gray-600' };
    }

    return platforms[storeName];
  };

  const platform = getPlatformDisplay(transaction.external_store_name);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Transaction Details
            </h1>
            <p className="text-sm text-gray-600">
              Transaction ID:
              {' '}
              {transaction.id}
            </p>
          </div>
        </div>

        {/* Status Badge */}
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${status.bgColor} ${status.textColor} ${status.borderColor} border`}>
          <StatusIcon className="h-4 w-4 mr-2" />
          {status.label}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Transaction Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Product Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <LinkIcon className="h-5 w-5 mr-2 text-gray-500" />
              Product Information
            </h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {transaction.product_name}
                </h3>
                <div className="flex items-center space-x-2 mt-1">
                  <span className={`text-sm font-medium ${platform.color}`}>
                    {platform.name}
                  </span>
                </div>
              </div>

              {transaction.product_description && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Description</h4>
                  <p className="text-sm text-gray-600">{transaction.product_description}</p>
                </div>
              )}

              {transaction.external_product_url && (
                <div>
                  <a
                    href={transaction.external_product_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                  >
                    <LinkIcon className="h-4 w-4 mr-1" />
                    View Original Product
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <CurrencyDollarIcon className="h-5 w-5 mr-2 text-gray-500" />
              Payment Information
            </h2>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Product Amount:</span>
                <span className="text-sm font-medium">
                  GH₵
                  {transaction.base_amount?.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Escrow Fee (5%):</span>
                <span className="text-sm font-medium">
                  GH₵
                  {transaction.escrow_fee?.toFixed(2)}
                </span>
              </div>
              <div className="border-t border-gray-200 pt-3 flex justify-between">
                <span className="font-medium text-gray-900">Total Amount:</span>
                <span className="font-bold text-blue-600">
                  GH₵
                  {transaction.total_amount?.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Shipping Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <MapPinIcon className="h-5 w-5 mr-2 text-gray-500" />
              Shipping Information
            </h2>

            <div className="space-y-2">
              <p className="text-sm text-gray-900">
                {transaction.shipping_address?.street}
              </p>
              <p className="text-sm text-gray-900">
                {transaction.shipping_address?.city}
                ,
                {transaction.shipping_address?.region}
              </p>
              <p className="text-sm text-gray-900">
                {transaction.shipping_address?.country}
              </p>
            </div>

            {transaction.tracking_number && (
              <div className="mt-4 p-3 bg-blue-50 rounded-md">
                <div className="flex items-center">
                  <TruckIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      Tracking Number:
                      {' '}
                      {transaction.tracking_number}
                    </p>
                    <p className="text-xs text-blue-700">
                      Use this number to track your package
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Notes */}
          {transaction.notes && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2 text-gray-500" />
                Additional Notes
              </h2>
              <p className="text-sm text-gray-700">{transaction.notes}</p>
            </div>
          )}
        </div>

        {/* Right Column - Actions & Timeline */}
        <div className="space-y-6">
          {/* Action Buttons */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Actions</h2>

            <div className="space-y-3">
              {userRole === 'seller' && transaction.status === 'pending' && (
                <button
                  onClick={handleAcceptTransaction}
                  disabled={actionLoading}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="small" className="mr-2" />
                      Accepting...
                    </>
                  ) : (
                    <>
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Accept Transaction
                    </>
                  )}
                </button>
              )}

              {userRole === 'buyer' && transaction.status === 'accepted' && (
                <button
                  onClick={handleFundEscrow}
                  disabled={actionLoading}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="small" className="mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <ShieldCheckIcon className="h-4 w-4 mr-2" />
                      Fund Escrow
                    </>
                  )}
                </button>
              )}

              {userRole === 'seller' && transaction.status === 'funded' && (
                <Link
                  to={`/transactions/${transaction.id}/order-form`}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  <TruckIcon className="h-4 w-4 mr-2" />
                  Submit Shipping Details
                </Link>
              )}

              {userRole === 'buyer' && transaction.status === 'delivered' && (
                <button
                  onClick={handleConfirmDelivery}
                  disabled={actionLoading}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="small" className="mr-2" />
                      Confirming...
                    </>
                  ) : (
                    <>
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Confirm Delivery
                    </>
                  )}
                </button>
              )}

              {/* Chat Button */}
              <Link
                to={`/chat/${userRole === 'buyer' ? transaction.seller_id : transaction.buyer_id}`}
                className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                Message
                {' '}
                {userRole === 'buyer' ? 'Seller' : 'Buyer'}
              </Link>

              {/* Dispute Button (if applicable) */}
              {['funded', 'shipped', 'delivered'].includes(transaction.status) && (
                <Link
                  to={`/disputes/new?transaction_id=${transaction.id}`}
                  className="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                  Report Issue
                </Link>
              )}
            </div>
          </div>

          {/* Participant Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Participants</h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Buyer</h3>
                <div className="flex items-center space-x-3">
                  <img
                    src={transaction.buyer?.avatar || '/default-avatar.jpg'}
                    alt={transaction.buyer?.first_name}
                    className="h-8 w-8 rounded-full"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.buyer?.first_name}
                      {' '}
                      {transaction.buyer?.last_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      @
                      {transaction.buyer?.username}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Seller</h3>
                <div className="flex items-center space-x-3">
                  <img
                    src={transaction.seller?.avatar || '/default-avatar.jpg'}
                    alt={transaction.seller?.first_name}
                    className="h-8 w-8 rounded-full"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.seller?.first_name}
                      {' '}
                      {transaction.seller?.last_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      @
                      {transaction.seller?.username}
                      {' '}
                      • Rating:
                      {' '}
                      {transaction.seller?.rating || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transaction Timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2 text-gray-500" />
              Timeline
            </h2>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 h-2 w-2 bg-blue-500 rounded-full mt-2" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Transaction Created</p>
                  <p className="text-xs text-gray-500">{formatDate(transaction.created_at)}</p>
                </div>
              </div>

              {transaction.accepted_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 h-2 w-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Transaction Accepted</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.accepted_at)}</p>
                  </div>
                </div>
              )}

              {transaction.funded_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 h-2 w-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Escrow Funded</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.funded_at)}</p>
                  </div>
                </div>
              )}

              {transaction.shipped_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 h-2 w-2 bg-purple-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Item Shipped</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.shipped_at)}</p>
                  </div>
                </div>
              )}

              {transaction.delivered_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 h-2 w-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Item Delivered</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.delivered_at)}</p>
                  </div>
                </div>
              )}

              {transaction.completed_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 h-2 w-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Transaction Completed</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.completed_at)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionDetails;
