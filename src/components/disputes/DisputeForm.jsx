import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import {
  ExclamationTriangleIcon,
  DocumentArrowUpIcon,
  XMarkIcon,
  InformationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { disputeAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function DisputeForm({ transactionId, onSuccess, onCancel, className = '' }) {
  const { user } = useSelector((state) => state.auth);
  
  const [loading, setLoading] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [transaction, setTransaction] = useState(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue
  } = useForm({
    defaultValues: {
      reason: '',
      description: '',
      requested_action: 'refund',
      evidence_description: ''
    }
  });

  const selectedReason = watch('reason');

  useEffect(() => {
    if (transactionId) {
      fetchTransaction();
    }
  }, [transactionId]);

  const fetchTransaction = async () => {
    try {
      const response = await disputeAPI.getTransaction(transactionId);
      if (response.data.success) {
        setTransaction(response.data.transaction);
      }
    } catch (error) {
      logger.error('Failed to fetch transaction:', error);
      toast.error('Failed to load transaction details');
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    
    if (files.length === 0) return;

    // Validate files
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
    
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File ${file.name} has an unsupported format.`);
        return false;
      }
      
      return true;
    });

    if (validFiles.length === 0) return;

    try {
      setUploadingFiles(true);
      
      const formData = new FormData();
      validFiles.forEach(file => {
        formData.append('files', file);
      });

      const response = await disputeAPI.uploadEvidence(formData);
      
      if (response.data.success) {
        setAttachments(prev => [...prev, ...response.data.files]);
        toast.success(`${validFiles.length} file(s) uploaded successfully`);
      }
    } catch (error) {
      logger.error('Failed to upload files:', error);
      toast.error('Failed to upload files');
    } finally {
      setUploadingFiles(false);
    }
  };

  const removeAttachment = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      const disputeData = {
        transaction_id: transactionId,
        reason: data.reason,
        description: data.description,
        requested_action: data.requested_action,
        evidence_description: data.evidence_description,
        attachments: attachments.map(file => file.id)
      };

      const response = await disputeAPI.createDispute(disputeData);
      
      if (response.data.success) {
        toast.success('Dispute created successfully');
        onSuccess?.(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to create dispute:', error);
      toast.error('Failed to create dispute');
    } finally {
      setLoading(false);
    }
  };

  const disputeReasons = [
    {
      value: 'item_not_received',
      label: 'Item Not Received',
      description: 'The item was never delivered or received'
    },
    {
      value: 'item_not_as_described',
      label: 'Item Not as Described',
      description: 'The item received differs significantly from the description'
    },
    {
      value: 'item_damaged',
      label: 'Item Damaged',
      description: 'The item was damaged during shipping or was already damaged'
    },
    {
      value: 'wrong_item',
      label: 'Wrong Item Sent',
      description: 'A different item was sent instead of what was ordered'
    },
    {
      value: 'seller_unresponsive',
      label: 'Seller Unresponsive',
      description: 'The seller is not responding to messages or concerns'
    },
    {
      value: 'unauthorized_charge',
      label: 'Unauthorized Charge',
      description: 'This charge was not authorized or recognized'
    },
    {
      value: 'other',
      label: 'Other',
      description: 'Other reason not listed above'
    }
  ];

  const requestedActions = [
    { value: 'refund', label: 'Full Refund' },
    { value: 'partial_refund', label: 'Partial Refund' },
    { value: 'replacement', label: 'Replacement Item' },
    { value: 'repair', label: 'Repair/Fix Item' },
    { value: 'store_credit', label: 'Store Credit' }
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Create Dispute</h2>
              <p className="text-gray-600 mt-1">Report an issue with your transaction</p>
            </div>
          </div>
          
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          )}
        </div>
      </div>

      {/* Transaction Info */}
      {transaction && (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Transaction Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Transaction ID:</span>
              <span className="ml-2 font-medium">{transaction.id}</span>
            </div>
            <div>
              <span className="text-gray-500">Amount:</span>
              <span className="ml-2 font-medium">GH₵{transaction.amount}</span>
            </div>
            <div>
              <span className="text-gray-500">Date:</span>
              <span className="ml-2 font-medium">
                {new Date(transaction.created_at).toLocaleDateString()}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Status:</span>
              <span className="ml-2 font-medium capitalize">{transaction.status}</span>
            </div>
          </div>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Dispute Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            What is the issue? <span className="text-red-500">*</span>
          </label>
          
          <div className="space-y-3">
            {disputeReasons.map((reason) => (
              <label key={reason.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  value={reason.value}
                  {...register('reason', { required: 'Please select a reason for the dispute' })}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{reason.label}</div>
                  <div className="text-sm text-gray-500">{reason.description}</div>
                </div>
              </label>
            ))}
          </div>
          
          {errors.reason && (
            <p className="text-red-600 text-sm mt-1">{errors.reason.message}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Detailed Description <span className="text-red-500">*</span>
          </label>
          <textarea
            {...register('description', { 
              required: 'Please provide a detailed description',
              minLength: {
                value: 50,
                message: 'Description must be at least 50 characters'
              }
            })}
            rows={4}
            placeholder="Please provide a detailed explanation of the issue, including what happened, when it occurred, and any steps you've already taken to resolve it..."
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {errors.description && (
            <p className="text-red-600 text-sm mt-1">{errors.description.message}</p>
          )}
        </div>

        {/* Requested Action */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            What would you like to happen? <span className="text-red-500">*</span>
          </label>
          <select
            {...register('requested_action', { required: 'Please select a requested action' })}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {requestedActions.map((action) => (
              <option key={action.value} value={action.value}>
                {action.label}
              </option>
            ))}
          </select>
          {errors.requested_action && (
            <p className="text-red-600 text-sm mt-1">{errors.requested_action.message}</p>
          )}
        </div>

        {/* Evidence Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Supporting Evidence
          </label>
          <p className="text-sm text-gray-500 mb-3">
            Upload photos, screenshots, receipts, or other documents that support your dispute.
          </p>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <DocumentArrowUpIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="text-sm text-gray-600 mb-2">
              <label className="cursor-pointer text-blue-600 hover:text-blue-800">
                Click to upload files
                <input
                  type="file"
                  multiple
                  accept="image/*,.pdf,.txt"
                  onChange={handleFileUpload}
                  className="hidden"
                  disabled={uploadingFiles}
                />
              </label>
              {' '}or drag and drop
            </div>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF, PDF, TXT up to 10MB each
            </p>
          </div>

          {uploadingFiles && (
            <div className="mt-2 text-sm text-blue-600">
              Uploading files...
            </div>
          )}

          {/* Uploaded Files */}
          {attachments.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Uploaded Files:</h4>
              {attachments.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <div className="flex items-center space-x-2">
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <span className="text-xs text-gray-500">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Evidence Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Evidence Description
          </label>
          <textarea
            {...register('evidence_description')}
            rows={3}
            placeholder="Please describe the evidence you've uploaded and how it supports your dispute..."
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Important Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <InformationCircleIcon className="h-5 w-5 text-blue-400 mt-0.5" />
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-800">Important Notice</h4>
              <div className="text-sm text-blue-700 mt-1">
                <ul className="list-disc list-inside space-y-1">
                  <li>All disputes are reviewed by our moderation team</li>
                  <li>You may be contacted for additional information</li>
                  <li>False or fraudulent disputes may result in account suspension</li>
                  <li>The other party will be notified and can respond to your dispute</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
          )}
          
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating Dispute...' : 'Create Dispute'}
          </button>
        </div>
      </form>
    </div>
  );
}

export default DisputeForm;
