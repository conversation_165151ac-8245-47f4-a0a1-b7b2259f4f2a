import { useState } from 'react';
import { Link, Navigate, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import useAuth from '../hooks/useAuth';
import Button from '../components/helpers/Button';
import LoadingSpinner from '../components/helpers/LoadingSpinner';

function RegisterPage() {
  const { isAuthenticated, register: registerUser, isLoading } = useAuth();
  const navigate = useNavigate();
  const [selectedRole, setSelectedRole] = useState('buyer');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  const password = watch('password');

  // Register field configurations
  const firstNameField = register('first_name', { required: 'First name is required' });
  const lastNameField = register('last_name', { required: 'Last name is required' });
  const emailField = register('email', {
    required: 'Email is required',
    pattern: {
      value: /^\S+@\S+$/i,
      message: 'Invalid email address',
    },
  });
  const phoneField = register('phone_number', { required: 'Phone number is required' });
  const passwordField = register('password', {
    required: 'Password is required',
    minLength: {
      value: 6,
      message: 'Password must be at least 6 characters',
    },
  });
  const passwordConfirmationField = register('password_confirmation', {
    required: 'Please confirm your password',
    validate: (value) => value === password || 'Passwords do not match',
  });
  const termsField = register('terms', {
    required: 'You must accept the terms and conditions',
  });

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const onSubmit = async (data) => {
    try {
      const userData = {
        ...data,
        role: selectedRole,
      };

      const result = await registerUser(userData);
      if (result.type === 'auth/register/fulfilled') {
        toast.success('Registration successful! Welcome to PayHold!');
        navigate('/dashboard');
      }
    } catch (error) {
      toast.error(`Registration failed: ${error?.message || error}`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">PayHold</h1>
          <h2 className="mt-6 text-2xl font-bold text-gray-900">
            Create your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Already have an account?
            {' '}
            <Link
              to="/login"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Role Selection */}
            <div>
              <div className="block text-sm font-medium text-gray-700 mb-3">
                I want to:
              </div>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setSelectedRole('buyer')}
                  className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                    selectedRole === 'buyer'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  Buy Products
                </button>
                <button
                  type="button"
                  onClick={() => setSelectedRole('seller')}
                  className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                    selectedRole === 'seller'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  Sell Products
                </button>
              </div>
            </div>

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium text-gray-700">
                  First Name
                  <input
                    id="first_name"
                    name={firstNameField.name}
                    onChange={firstNameField.onChange}
                    onBlur={firstNameField.onBlur}
                    ref={firstNameField.ref}
                    type="text"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </label>
                {errors.first_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.first_name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="last_name" className="block text-sm font-medium text-gray-700">
                  Last Name
                  <input
                    id="last_name"
                    name={lastNameField.name}
                    onChange={lastNameField.onChange}
                    onBlur={lastNameField.onBlur}
                    ref={lastNameField.ref}
                    type="text"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </label>
                {errors.last_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.last_name.message}</p>
                )}
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
                <input
                  id="email"
                  name={emailField.name}
                  onChange={emailField.onChange}
                  onBlur={emailField.onBlur}
                  ref={emailField.ref}
                  type="email"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </label>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700">
                Phone Number
                <input
                  id="phone_number"
                  name={phoneField.name}
                  onChange={phoneField.onChange}
                  onBlur={phoneField.onBlur}
                  ref={phoneField.ref}
                  type="tel"
                  placeholder="+233123456789"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </label>
              {errors.phone_number && (
                <p className="mt-1 text-sm text-red-600">{errors.phone_number.message}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
                <input
                  id="password"
                  name={passwordField.name}
                  onChange={passwordField.onChange}
                  onBlur={passwordField.onBlur}
                  ref={passwordField.ref}
                  type="password"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </label>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <label htmlFor="password_confirmation" className="block text-sm font-medium text-gray-700">
                Confirm Password
                <input
                  id="password_confirmation"
                  name={passwordConfirmationField.name}
                  onChange={passwordConfirmationField.onChange}
                  onBlur={passwordConfirmationField.onBlur}
                  ref={passwordConfirmationField.ref}
                  type="password"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </label>
              {errors.password_confirmation && (
                <p className="mt-1 text-sm text-red-600">{errors.password_confirmation.message}</p>
              )}
            </div>

            {/* Terms and Conditions */}
            <div className="flex items-center">
              <input
                id="terms"
                name={termsField.name}
                onChange={termsField.onChange}
                onBlur={termsField.onBlur}
                ref={termsField.ref}
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
                I agree to the
                {' '}
                <Link to="/terms" className="text-blue-600 hover:text-blue-500">
                  Terms and Conditions
                </Link>
                {' '}
                and
                {' '}
                <Link to="/privacy" className="text-blue-600 hover:text-blue-500">
                  Privacy Policy
                </Link>
              </label>
            </div>
            {errors.terms && (
              <p className="text-sm text-red-600">{errors.terms.message}</p>
            )}

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default RegisterPage;
