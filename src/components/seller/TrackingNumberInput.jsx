import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  LinkIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../helpers/LoadingSpinner';

function TrackingNumberInput({ 
  transaction, 
  onUpdate, 
  onClose, 
  initialData = null 
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationStatus, setValidationStatus] = useState(null);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      tracking_number: initialData?.tracking_number || '',
      shipping_carrier: initialData?.shipping_carrier || '',
      shipping_method: initialData?.shipping_method || 'standard',
      estimated_delivery: initialData?.estimated_delivery || ''
    }
  });

  const watchedTrackingNumber = watch('tracking_number');
  const watchedCarrier = watch('shipping_carrier');

  const shippingCarriers = [
    { 
      value: 'dhl', 
      label: 'DHL Express',
      trackingUrl: 'https://www.dhl.com/en/express/tracking.html?AWB=',
      pattern: /^[0-9]{10,11}$/
    },
    { 
      value: 'fedex', 
      label: 'FedEx',
      trackingUrl: 'https://www.fedex.com/fedextrack/?trknbr=',
      pattern: /^[0-9]{12,14}$/
    },
    { 
      value: 'ups', 
      label: 'UPS',
      trackingUrl: 'https://www.ups.com/track?loc=en_US&tracknum=',
      pattern: /^1Z[0-9A-Z]{16}$/
    },
    { 
      value: 'ghana_post', 
      label: 'Ghana Post',
      trackingUrl: 'https://www.ghanapost.com.gh/track/',
      pattern: /^[A-Z]{2}[0-9]{9}[A-Z]{2}$/
    },
    { 
      value: 'speedaf', 
      label: 'SpeedAF',
      trackingUrl: 'https://www.speedaf.com/track/',
      pattern: /^[A-Z0-9]{10,15}$/
    },
    { 
      value: 'aramex', 
      label: 'Aramex',
      trackingUrl: 'https://www.aramex.com/track/results?ShipmentNumber=',
      pattern: /^[0-9]{11}$/
    },
    { 
      value: 'tnt', 
      label: 'TNT',
      trackingUrl: 'https://www.tnt.com/express/en_us/site/shipping-tools/tracking.html?searchType=con&cons=',
      pattern: /^[0-9]{9}$/
    },
    { 
      value: 'other', 
      label: 'Other',
      trackingUrl: null,
      pattern: null
    }
  ];

  const shippingMethods = [
    { value: 'standard', label: 'Standard Shipping (5-7 days)', estimatedDays: 6 },
    { value: 'express', label: 'Express Shipping (2-3 days)', estimatedDays: 3 },
    { value: 'overnight', label: 'Overnight Shipping (1 day)', estimatedDays: 1 },
    { value: 'economy', label: 'Economy Shipping (7-14 days)', estimatedDays: 10 },
    { value: 'pickup', label: 'Local Pickup', estimatedDays: 0 }
  ];

  // Validate tracking number format
  React.useEffect(() => {
    if (watchedTrackingNumber && watchedCarrier) {
      const carrier = shippingCarriers.find(c => c.value === watchedCarrier);
      if (carrier && carrier.pattern) {
        const isValid = carrier.pattern.test(watchedTrackingNumber);
        setValidationStatus(isValid ? 'valid' : 'invalid');
      } else {
        setValidationStatus('unknown');
      }
    } else {
      setValidationStatus(null);
    }
  }, [watchedTrackingNumber, watchedCarrier]);

  // Auto-calculate estimated delivery date
  React.useEffect(() => {
    const method = watch('shipping_method');
    if (method) {
      const shippingMethod = shippingMethods.find(m => m.value === method);
      if (shippingMethod && shippingMethod.estimatedDays > 0) {
        const estimatedDate = new Date();
        estimatedDate.setDate(estimatedDate.getDate() + shippingMethod.estimatedDays);
        setValue('estimated_delivery', estimatedDate.toISOString().split('T')[0]);
      }
    }
  }, [watch('shipping_method'), setValue]);

  const copyTrackingNumber = () => {
    if (watchedTrackingNumber) {
      navigator.clipboard.writeText(watchedTrackingNumber);
      toast.success('Tracking number copied to clipboard');
    }
  };

  const openTrackingUrl = () => {
    const carrier = shippingCarriers.find(c => c.value === watchedCarrier);
    if (carrier && carrier.trackingUrl && watchedTrackingNumber) {
      window.open(carrier.trackingUrl + watchedTrackingNumber, '_blank');
    }
  };

  const onSubmit = async (formData) => {
    try {
      setIsSubmitting(true);

      // Validate tracking number format if pattern exists
      const carrier = shippingCarriers.find(c => c.value === formData.shipping_carrier);
      if (carrier && carrier.pattern && !carrier.pattern.test(formData.tracking_number)) {
        toast.error(`Invalid tracking number format for ${carrier.label}`);
        return;
      }

      const updateData = {
        tracking_number: formData.tracking_number,
        shipping_carrier: formData.shipping_carrier,
        shipping_method: formData.shipping_method,
        estimated_delivery: formData.estimated_delivery || null
      };

      await onUpdate(updateData);
      toast.success('Tracking information updated successfully!');
      onClose?.();
    } catch (error) {
      toast.error(error.message || 'Failed to update tracking information');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getValidationIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'invalid':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'unknown':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getValidationMessage = () => {
    const carrier = shippingCarriers.find(c => c.value === watchedCarrier);
    
    switch (validationStatus) {
      case 'valid':
        return `Valid ${carrier?.label} tracking number`;
      case 'invalid':
        return `Invalid format for ${carrier?.label}. Please check the tracking number.`;
      case 'unknown':
        return 'Unable to validate tracking number format';
      default:
        return null;
    }
  };

  if (!transaction) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {initialData ? 'Update' : 'Add'} Tracking Information
              </h3>
              <p className="text-sm text-gray-600">
                {transaction.product_name}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
          {/* Shipping Carrier */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shipping Carrier *
            </label>
            <select
              {...register('shipping_carrier', { required: 'Please select a carrier' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select carrier...</option>
              {shippingCarriers.map((carrier) => (
                <option key={carrier.value} value={carrier.value}>
                  {carrier.label}
                </option>
              ))}
            </select>
            {errors.shipping_carrier && (
              <p className="mt-1 text-sm text-red-600">{errors.shipping_carrier.message}</p>
            )}
          </div>

          {/* Tracking Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tracking Number *
            </label>
            <div className="relative">
              <input
                type="text"
                {...register('tracking_number', { required: 'Tracking number is required' })}
                className={`w-full px-3 py-2 pr-20 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent ${
                  validationStatus === 'valid' ? 'border-green-300 focus:ring-green-500' :
                  validationStatus === 'invalid' ? 'border-red-300 focus:ring-red-500' :
                  'border-gray-300 focus:ring-blue-500'
                }`}
                placeholder="Enter tracking number"
              />
              <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                {watchedTrackingNumber && (
                  <button
                    type="button"
                    onClick={copyTrackingNumber}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy tracking number"
                  >
                    <ClipboardDocumentIcon className="h-4 w-4" />
                  </button>
                )}
                {getValidationIcon()}
              </div>
            </div>
            
            {/* Validation Message */}
            {validationStatus && (
              <div className={`mt-1 flex items-center text-sm ${
                validationStatus === 'valid' ? 'text-green-600' :
                validationStatus === 'invalid' ? 'text-red-600' :
                'text-yellow-600'
              }`}>
                {getValidationMessage()}
              </div>
            )}
            
            {errors.tracking_number && (
              <p className="mt-1 text-sm text-red-600">{errors.tracking_number.message}</p>
            )}

            {/* External Tracking Link */}
            {watchedCarrier && watchedTrackingNumber && validationStatus === 'valid' && (
              <div className="mt-2">
                <button
                  type="button"
                  onClick={openTrackingUrl}
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <LinkIcon className="h-4 w-4 mr-1" />
                  Track with {shippingCarriers.find(c => c.value === watchedCarrier)?.label}
                </button>
              </div>
            )}
          </div>

          {/* Shipping Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shipping Method
            </label>
            <select
              {...register('shipping_method')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {shippingMethods.map((method) => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </select>
          </div>

          {/* Estimated Delivery */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Delivery Date
            </label>
            <input
              type="date"
              {...register('estimated_delivery')}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="mt-1 text-xs text-gray-500">
              This will be automatically calculated based on shipping method
            </p>
          </div>

          {/* Shipping Address Reminder */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="font-medium text-blue-900 mb-2">Shipping To:</h4>
            <div className="text-sm text-blue-800">
              <p>{transaction.shipping_address?.street}</p>
              <p>{transaction.shipping_address?.city}, {transaction.shipping_address?.region}</p>
              <p>{transaction.shipping_address?.country}</p>
            </div>
          </div>

          {/* Important Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3 mt-0.5" />
              <div className="text-sm">
                <h4 className="font-medium text-yellow-800 mb-1">Important</h4>
                <ul className="text-yellow-700 space-y-1">
                  <li>• Double-check the tracking number for accuracy</li>
                  <li>• The buyer will be notified with this tracking information</li>
                  <li>• Make sure the package is properly labeled and secured</li>
                  <li>• Keep your shipping receipt for records</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || validationStatus === 'invalid'}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  {initialData ? 'Updating...' : 'Adding...'}
                </>
              ) : (
                <>
                  <CheckCircleIcon className="h-4 w-4 mr-2" />
                  {initialData ? 'Update' : 'Add'} Tracking Info
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default TrackingNumberInput;
