import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';

// Mock API calls - replace with actual API when available
const chatAPI = {
  getConversations: (params) => Promise.resolve({ data: { success: true, conversations: [] } }),
  getMessages: (conversationId) => Promise.resolve({ data: { success: true, messages: [] } }),
  sendMessage: (messageData) => Promise.resolve({ data: { success: true, message: messageData } }),
  createConversation: (conversationData) => Promise.resolve({ data: { success: true, conversation: conversationData } })
};

// Async thunks
export const fetchConversations = createAsyncThunk(
  'chat/fetchConversations',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await chatAPI.getConversations(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch conversations');
    }
  }
);

export const fetchMessages = createAsyncThunk(
  'chat/fetchMessages',
  async (conversationId, { rejectWithValue }) => {
    try {
      const response = await chatAPI.getMessages(conversationId);
      return { conversationId, ...response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch messages');
    }
  }
);

export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async (messageData, { rejectWithValue }) => {
    try {
      const response = await chatAPI.sendMessage(messageData);
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to send message';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const createConversation = createAsyncThunk(
  'chat/createConversation',
  async (conversationData, { rejectWithValue }) => {
    try {
      const response = await chatAPI.createConversation(conversationData);
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create conversation';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

const initialState = {
  // Conversations
  conversations: [],
  currentConversation: null,
  
  // Messages
  messages: {}, // { conversationId: [messages] }
  
  // UI state
  activeConversationId: null,
  isTyping: {},
  onlineUsers: [],
  
  // Loading states
  isLoadingConversations: false,
  isLoadingMessages: false,
  isSendingMessage: false,
  isCreatingConversation: false,
  
  // Error states
  error: null,
  sendError: null,
  
  // Unread counts
  unreadCounts: {}, // { conversationId: count }
  totalUnreadCount: 0,
  
  // Connection status
  isConnected: false,
  connectionError: null
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.sendError = null;
      state.connectionError = null;
    },
    
    setActiveConversation: (state, action) => {
      state.activeConversationId = action.payload;
      
      // Mark conversation as read
      if (action.payload && state.unreadCounts[action.payload]) {
        state.totalUnreadCount -= state.unreadCounts[action.payload];
        state.unreadCounts[action.payload] = 0;
      }
    },
    
    clearActiveConversation: (state) => {
      state.activeConversationId = null;
      state.currentConversation = null;
    },
    
    // Real-time message handling
    messageReceived: (state, action) => {
      const message = action.payload;
      const conversationId = message.conversation_id;
      
      // Add message to messages array
      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }
      state.messages[conversationId].push(message);
      
      // Update conversation last message
      const conversationIndex = state.conversations.findIndex(
        conv => conv.id === conversationId
      );
      if (conversationIndex !== -1) {
        state.conversations[conversationIndex].last_message = message;
        state.conversations[conversationIndex].updated_at = message.created_at;
        
        // Move conversation to top
        const conversation = state.conversations.splice(conversationIndex, 1)[0];
        state.conversations.unshift(conversation);
      }
      
      // Update unread count if not active conversation
      if (state.activeConversationId !== conversationId) {
        state.unreadCounts[conversationId] = (state.unreadCounts[conversationId] || 0) + 1;
        state.totalUnreadCount += 1;
      }
    },
    
    messageSent: (state, action) => {
      const message = action.payload;
      const conversationId = message.conversation_id;
      
      // Add message to messages array
      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }
      
      // Update or add the message (in case of optimistic updates)
      const existingIndex = state.messages[conversationId].findIndex(
        msg => msg.temp_id === message.temp_id
      );
      
      if (existingIndex !== -1) {
        state.messages[conversationId][existingIndex] = message;
      } else {
        state.messages[conversationId].push(message);
      }
      
      // Update conversation last message
      const conversationIndex = state.conversations.findIndex(
        conv => conv.id === conversationId
      );
      if (conversationIndex !== -1) {
        state.conversations[conversationIndex].last_message = message;
        state.conversations[conversationIndex].updated_at = message.created_at;
      }
    },
    
    // Optimistic message update
    addOptimisticMessage: (state, action) => {
      const { conversationId, message } = action.payload;
      
      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }
      
      // Add temporary message with temp_id
      const tempMessage = {
        ...message,
        temp_id: Date.now(),
        status: 'sending',
        created_at: new Date().toISOString()
      };
      
      state.messages[conversationId].push(tempMessage);
    },
    
    updateMessageStatus: (state, action) => {
      const { conversationId, tempId, status, messageId } = action.payload;
      
      if (state.messages[conversationId]) {
        const messageIndex = state.messages[conversationId].findIndex(
          msg => msg.temp_id === tempId
        );
        
        if (messageIndex !== -1) {
          state.messages[conversationId][messageIndex].status = status;
          if (messageId) {
            state.messages[conversationId][messageIndex].id = messageId;
          }
        }
      }
    },
    
    // Typing indicators
    setTyping: (state, action) => {
      const { conversationId, userId, isTyping } = action.payload;
      
      if (!state.isTyping[conversationId]) {
        state.isTyping[conversationId] = {};
      }
      
      if (isTyping) {
        state.isTyping[conversationId][userId] = true;
      } else {
        delete state.isTyping[conversationId][userId];
      }
    },
    
    // Online status
    updateOnlineUsers: (state, action) => {
      state.onlineUsers = action.payload;
    },
    
    userOnline: (state, action) => {
      const userId = action.payload;
      if (!state.onlineUsers.includes(userId)) {
        state.onlineUsers.push(userId);
      }
    },
    
    userOffline: (state, action) => {
      const userId = action.payload;
      state.onlineUsers = state.onlineUsers.filter(id => id !== userId);
    },
    
    // Connection status
    setConnectionStatus: (state, action) => {
      state.isConnected = action.payload;
      if (action.payload) {
        state.connectionError = null;
      }
    },
    
    setConnectionError: (state, action) => {
      state.connectionError = action.payload;
      state.isConnected = false;
    },
    
    // Unread counts
    updateUnreadCount: (state, action) => {
      const { conversationId, count } = action.payload;
      const oldCount = state.unreadCounts[conversationId] || 0;
      state.unreadCounts[conversationId] = count;
      state.totalUnreadCount = state.totalUnreadCount - oldCount + count;
    },
    
    markConversationAsRead: (state, action) => {
      const conversationId = action.payload;
      const unreadCount = state.unreadCounts[conversationId] || 0;
      state.totalUnreadCount -= unreadCount;
      state.unreadCounts[conversationId] = 0;
    },
    
    // Clear messages for a conversation
    clearMessages: (state, action) => {
      const conversationId = action.payload;
      delete state.messages[conversationId];
    }
  },
  
  extraReducers: (builder) => {
    // Fetch Conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.isLoadingConversations = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.isLoadingConversations = false;
        state.conversations = action.payload.conversations || [];
        
        // Calculate unread counts
        state.conversations.forEach(conv => {
          if (conv.unread_count > 0) {
            state.unreadCounts[conv.id] = conv.unread_count;
            state.totalUnreadCount += conv.unread_count;
          }
        });
        
        state.error = null;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.isLoadingConversations = false;
        state.error = action.payload;
      });

    // Fetch Messages
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.isLoadingMessages = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.isLoadingMessages = false;
        const { conversationId, messages } = action.payload;
        state.messages[conversationId] = messages || [];
        state.error = null;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.isLoadingMessages = false;
        state.error = action.payload;
      });

    // Send Message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isSendingMessage = true;
        state.sendError = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isSendingMessage = false;
        // Message will be handled by messageSent reducer
        state.sendError = null;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isSendingMessage = false;
        state.sendError = action.payload;
      });

    // Create Conversation
    builder
      .addCase(createConversation.pending, (state) => {
        state.isCreatingConversation = true;
        state.error = null;
      })
      .addCase(createConversation.fulfilled, (state, action) => {
        state.isCreatingConversation = false;
        const newConversation = action.payload.conversation;
        state.conversations.unshift(newConversation);
        state.currentConversation = newConversation;
        state.activeConversationId = newConversation.id;
        state.error = null;
      })
      .addCase(createConversation.rejected, (state, action) => {
        state.isCreatingConversation = false;
        state.error = action.payload;
      });
  }
});

export const {
  clearError,
  setActiveConversation,
  clearActiveConversation,
  messageReceived,
  messageSent,
  addOptimisticMessage,
  updateMessageStatus,
  setTyping,
  updateOnlineUsers,
  userOnline,
  userOffline,
  setConnectionStatus,
  setConnectionError,
  updateUnreadCount,
  markConversationAsRead,
  clearMessages
} = chatSlice.actions;

export default chatSlice.reducer;
