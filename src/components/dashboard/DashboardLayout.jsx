import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  HomeIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  UserIcon,
  CogIcon,
  ChartBarIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';

function DashboardLayout() {
  const { user, isAuthenticated } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Navigation items based on user role
  const getNavigationItems = () => {
    const baseItems = [
      {
        name: 'Overview', href: '/dashboard', icon: HomeIcon, current: location.pathname === '/dashboard',
      },
      {
        name: 'Orders', href: '/dashboard/orders', icon: ShoppingBagIcon, current: location.pathname.startsWith('/dashboard/orders'),
      },
      {
        name: 'Messages', href: '/dashboard/messages', icon: ChatBubbleLeftRightIcon, current: location.pathname.startsWith('/dashboard/messages'),
      },
      {
        name: 'Notifications', href: '/dashboard/notifications', icon: BellIcon, current: location.pathname.startsWith('/dashboard/notifications'),
      },
      {
        name: 'Profile', href: '/dashboard/profile', icon: UserIcon, current: location.pathname.startsWith('/dashboard/profile'),
      },
      {
        name: 'Settings', href: '/dashboard/settings', icon: CogIcon, current: location.pathname.startsWith('/dashboard/settings'),
      },
    ];

    // Add role-specific items
    if (user?.role === 'seller') {
      baseItems.splice(
        2,
        0,
        {
          name: 'Products', href: '/dashboard/products', icon: ShoppingCartIcon, current: location.pathname.startsWith('/dashboard/products'),
        },
        {
          name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon, current: location.pathname.startsWith('/dashboard/analytics'),
        },
      );
    }

    if (user?.role === 'admin') {
      baseItems.splice(
        1,
        0,
        {
          name: 'Users', href: '/dashboard/users', icon: UsersIcon, current: location.pathname.startsWith('/dashboard/users'),
        },
        {
          name: 'Disputes', href: '/dashboard/disputes', icon: ExclamationTriangleIcon, current: location.pathname.startsWith('/dashboard/disputes'),
        },
        {
          name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon, current: location.pathname.startsWith('/dashboard/analytics'),
        },
      );
    }

    return baseItems;
  };

  const navigation = getNavigationItems();

  // Close sidebar on route change (mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        </div>
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-gray-900">PayHold</h1>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* User info */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <img
              src={user?.avatar || '/default-avatar.jpg'}
              alt={user?.first_name}
              className="h-10 w-10 rounded-full"
            />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {user?.first_name}
                {' '}
                {user?.last_name}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {user?.role}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {navigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  item.current
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <item.icon
                  className={`mr-3 h-5 w-5 ${
                    item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
              </a>
            ))}
          </div>
        </nav>

        {/* Subscription status (if applicable) */}
        {user?.role !== 'admin' && (
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div className="bg-yellow-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-xs font-medium text-yellow-800">
                    Free Plan
                  </p>
                  <p className="text-xs text-yellow-700">
                    5% fee applies
                  </p>
                </div>
              </div>
              <div className="mt-2">
                <a
                  href="/dashboard/subscription"
                  className="text-xs font-medium text-yellow-800 hover:text-yellow-900"
                >
                  Upgrade to Premium →
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>

            <div className="flex items-center space-x-4">
              {/* Search bar */}
              <div className="hidden md:block">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search..."
                    className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Notifications */}
              <button className="p-2 text-gray-400 hover:text-gray-600 relative">
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white" />
              </button>

              {/* Profile dropdown */}
              <div className="relative">
                <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50">
                  <img
                    src={user?.avatar || '/default-avatar.jpg'}
                    alt={user?.first_name}
                    className="h-8 w-8 rounded-full"
                  />
                  <span className="hidden md:block text-sm font-medium text-gray-700">
                    {user?.first_name}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-4 sm:p-6 lg:p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default DashboardLayout;
