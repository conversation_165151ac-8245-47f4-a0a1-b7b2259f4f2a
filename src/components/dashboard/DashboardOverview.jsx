import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import { dashboardAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';

function DashboardOverview() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, [user?.role]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      let response;

      switch (user?.role) {
        case 'admin':
          response = await dashboardAPI.getAdminDashboard();
          break;
        case 'seller':
          response = await dashboardAPI.getSellerDashboard();
          break;
        default:
          response = await dashboardAPI.getBuyerDashboard();
      }

      if (response.data.success) {
        setDashboardData(response.data);
      }
    } catch (err) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchDashboardData}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  // Get role-specific stats and content
  const getStatsCards = () => {
    const baseStats = dashboardData?.stats || {};

    switch (user?.role) {
      case 'admin':
        return [
          {
            name: 'Total Users',
            value: baseStats.total_users || 0,
            change: baseStats.users_change || 0,
            icon: ShoppingBagIcon,
            color: 'blue',
          },
          {
            name: 'Total Transactions',
            value: baseStats.total_transactions || 0,
            change: baseStats.transactions_change || 0,
            icon: ChartBarIcon,
            color: 'green',
          },
          {
            name: 'Platform Revenue',
            value: `GH₵ ${(baseStats.platform_revenue || 0).toLocaleString()}`,
            change: baseStats.revenue_change || 0,
            icon: CurrencyDollarIcon,
            color: 'purple',
          },
          {
            name: 'Active Disputes',
            value: baseStats.active_disputes || 0,
            change: baseStats.disputes_change || 0,
            icon: ClockIcon,
            color: 'red',
          },
        ];

      case 'seller':
        return [
          {
            name: 'Total Sales',
            value: baseStats.total_sales || 0,
            change: baseStats.sales_change || 0,
            icon: ShoppingBagIcon,
            color: 'blue',
          },
          {
            name: 'Revenue',
            value: `GH₵ ${(baseStats.total_revenue || 0).toLocaleString()}`,
            change: baseStats.revenue_change || 0,
            icon: CurrencyDollarIcon,
            color: 'green',
          },
          {
            name: 'Products',
            value: baseStats.total_products || 0,
            change: baseStats.products_change || 0,
            icon: ChartBarIcon,
            color: 'purple',
          },
          {
            name: 'Pending Orders',
            value: baseStats.pending_orders || 0,
            change: baseStats.pending_change || 0,
            icon: ClockIcon,
            color: 'yellow',
          },
        ];

      default: // buyer
        return [
          {
            name: 'Total Orders',
            value: baseStats.total_orders || 0,
            change: baseStats.orders_change || 0,
            icon: ShoppingBagIcon,
            color: 'blue',
          },
          {
            name: 'Total Spent',
            value: `GH₵ ${(baseStats.total_spent || 0).toLocaleString()}`,
            change: baseStats.spent_change || 0,
            icon: CurrencyDollarIcon,
            color: 'green',
          },
          {
            name: 'Active Orders',
            value: baseStats.active_orders || 0,
            change: baseStats.active_change || 0,
            icon: ClockIcon,
            color: 'purple',
          },
          {
            name: 'Completed Orders',
            value: baseStats.completed_orders || 0,
            change: baseStats.completed_change || 0,
            icon: ChartBarIcon,
            color: 'green',
          },
        ];
    }
  };

  const statsCards = getStatsCards();
  const recentActivity = dashboardData?.recent_activity || [];
  const quickActions = getQuickActions();

  function getQuickActions() {
    switch (user?.role) {
      case 'admin':
        return [
          { name: 'Manage Users', href: '/dashboard/users', icon: ShoppingBagIcon },
          { name: 'Review Disputes', href: '/dashboard/disputes', icon: ClockIcon },
          { name: 'View Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },
          { name: 'System Settings', href: '/dashboard/settings', icon: CurrencyDollarIcon },
        ];
      case 'seller':
        return [
          { name: 'Add Product', href: '/dashboard/products/new', icon: ShoppingBagIcon },
          { name: 'View Orders', href: '/dashboard/orders', icon: ClockIcon },
          { name: 'Sales Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },
          { name: 'Manage Profile', href: '/dashboard/profile', icon: CurrencyDollarIcon },
        ];
      default:
        return [
          { name: 'Browse Products', href: '/products', icon: ShoppingBagIcon },
          { name: 'View Orders', href: '/dashboard/orders', icon: ClockIcon },
          { name: 'Start Transaction', href: '/dashboard/transactions/new', icon: ChartBarIcon },
          { name: 'Update Profile', href: '/dashboard/profile', icon: CurrencyDollarIcon },
        ];
    }
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back,
          {' '}
          {user?.first_name}
          !
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your
          {' '}
          {user?.role === 'admin' ? 'platform' : 'account'}
          {' '}
          today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full bg-${stat.color}-100`}>
                <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
              </div>
            </div>
            {stat.change !== 0 && (
              <div className="mt-4 flex items-center">
                {stat.change > 0 ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  stat.change > 0 ? 'text-green-600' : 'text-red-600'
                }`}
                >
                  {Math.abs(stat.change)}
                  %
                </span>
                <span className="text-sm text-gray-500 ml-1">from last month</span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 gap-4">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                to={action.href}
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <action.icon className="h-8 w-8 text-blue-600 mr-3" />
                <span className="font-medium text-gray-900">{action.name}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            <Link
              to="/dashboard/activity"
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
            >
              View all
              <EyeIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>

          {recentActivity.length > 0 ? (
            <div className="space-y-4">
              {recentActivity.slice(0, 5).map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <div className="h-2 w-2 bg-blue-600 rounded-full" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No recent activity</p>
          )}
        </div>
      </div>

      {/* Additional role-specific content */}
      {user?.role === 'seller' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Tips</h2>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                <div className="h-2 w-2 bg-green-600 rounded-full" />
              </div>
              <p className="text-sm text-gray-700">
                Add high-quality images to your products to increase sales by up to 40%
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 h-5 w-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                <div className="h-2 w-2 bg-blue-600 rounded-full" />
              </div>
              <p className="text-sm text-gray-700">
                Respond to customer messages within 2 hours to maintain high ratings
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 h-5 w-5 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                <div className="h-2 w-2 bg-purple-600 rounded-full" />
              </div>
              <p className="text-sm text-gray-700">
                Consider upgrading to Premium to waive the 5% transaction fee
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DashboardOverview;
