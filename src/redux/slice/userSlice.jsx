import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { userAPI } from '../../services/apiServices';

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await userAPI.getProfile();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch profile');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateProfile(userData);
      toast.success('Profile updated successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update profile';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const changePassword = createAsyncThunk(
  'user/changePassword',
  async (passwordData, { rejectWithValue }) => {
    try {
      const response = await userAPI.changePassword(passwordData);
      toast.success('Password changed successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to change password';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const uploadAvatar = createAsyncThunk(
  'user/uploadAvatar',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await userAPI.uploadAvatar(formData);
      toast.success('Avatar updated successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to upload avatar';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

const initialState = {
  // User profile data
  profile: null,
  
  // User preferences
  preferences: {
    notifications: {
      email: true,
      push: true,
      sms: false,
      order_updates: true,
      marketing: false,
      chat_messages: true
    },
    privacy: {
      show_online_status: true,
      show_last_seen: true,
      allow_contact_from_strangers: false
    },
    display: {
      theme: 'light',
      language: 'en',
      currency: 'GHS',
      timezone: 'Africa/Accra'
    }
  },
  
  // User statistics
  stats: {
    total_orders: 0,
    total_spent: 0,
    total_earned: 0,
    successful_transactions: 0,
    rating: 0,
    reviews_count: 0,
    join_date: null,
    last_active: null
  },
  
  // Subscription info
  subscription: {
    is_subscribed: false,
    plan: null,
    expires_at: null,
    features: []
  },
  
  // Loading states
  isLoading: false,
  isUpdating: false,
  isChangingPassword: false,
  isUploadingAvatar: false,
  
  // Error states
  error: null,
  updateError: null,
  passwordError: null,
  avatarError: null,
  
  // UI state
  showProfileModal: false,
  showSettingsModal: false,
  activeSettingsTab: 'profile'
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.updateError = null;
      state.passwordError = null;
      state.avatarError = null;
    },
    
    updatePreferences: (state, action) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload
      };
    },
    
    updateNotificationPreferences: (state, action) => {
      state.preferences.notifications = {
        ...state.preferences.notifications,
        ...action.payload
      };
    },
    
    updatePrivacyPreferences: (state, action) => {
      state.preferences.privacy = {
        ...state.preferences.privacy,
        ...action.payload
      };
    },
    
    updateDisplayPreferences: (state, action) => {
      state.preferences.display = {
        ...state.preferences.display,
        ...action.payload
      };
    },
    
    updateUserStats: (state, action) => {
      state.stats = {
        ...state.stats,
        ...action.payload
      };
    },
    
    updateSubscription: (state, action) => {
      state.subscription = {
        ...state.subscription,
        ...action.payload
      };
    },
    
    // UI state management
    showProfileModal: (state) => {
      state.showProfileModal = true;
    },
    
    hideProfileModal: (state) => {
      state.showProfileModal = false;
    },
    
    showSettingsModal: (state, action) => {
      state.showSettingsModal = true;
      if (action.payload) {
        state.activeSettingsTab = action.payload;
      }
    },
    
    hideSettingsModal: (state) => {
      state.showSettingsModal = false;
    },
    
    setActiveSettingsTab: (state, action) => {
      state.activeSettingsTab = action.payload;
    },
    
    // Real-time updates
    profileUpdated: (state, action) => {
      state.profile = {
        ...state.profile,
        ...action.payload
      };
    },
    
    avatarUpdated: (state, action) => {
      if (state.profile) {
        state.profile.avatar = action.payload;
      }
    },
    
    subscriptionUpdated: (state, action) => {
      state.subscription = {
        ...state.subscription,
        ...action.payload
      };
    },
    
    // Activity tracking
    updateLastActive: (state) => {
      state.stats.last_active = new Date().toISOString();
    },
    
    incrementTransactionCount: (state) => {
      state.stats.successful_transactions += 1;
    },
    
    updateSpentAmount: (state, action) => {
      state.stats.total_spent += action.payload;
    },
    
    updateEarnedAmount: (state, action) => {
      state.stats.total_earned += action.payload;
    },
    
    updateRating: (state, action) => {
      const { rating, reviews_count } = action.payload;
      state.stats.rating = rating;
      state.stats.reviews_count = reviews_count;
    }
  },
  
  extraReducers: (builder) => {
    // Fetch User Profile
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        const { user, stats, subscription, preferences } = action.payload;
        
        state.profile = user;
        state.stats = { ...state.stats, ...stats };
        state.subscription = { ...state.subscription, ...subscription };
        state.preferences = { ...state.preferences, ...preferences };
        state.error = null;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Update User Profile
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isUpdating = true;
        state.updateError = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.profile = { ...state.profile, ...action.payload.user };
        state.updateError = null;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.updateError = action.payload;
      });

    // Change Password
    builder
      .addCase(changePassword.pending, (state) => {
        state.isChangingPassword = true;
        state.passwordError = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.isChangingPassword = false;
        state.passwordError = null;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isChangingPassword = false;
        state.passwordError = action.payload;
      });

    // Upload Avatar
    builder
      .addCase(uploadAvatar.pending, (state) => {
        state.isUploadingAvatar = true;
        state.avatarError = null;
      })
      .addCase(uploadAvatar.fulfilled, (state, action) => {
        state.isUploadingAvatar = false;
        if (state.profile) {
          state.profile.avatar = action.payload.avatar_url;
        }
        state.avatarError = null;
      })
      .addCase(uploadAvatar.rejected, (state, action) => {
        state.isUploadingAvatar = false;
        state.avatarError = action.payload;
      });
  }
});

export const {
  clearError,
  updatePreferences,
  updateNotificationPreferences,
  updatePrivacyPreferences,
  updateDisplayPreferences,
  updateUserStats,
  updateSubscription,
  showProfileModal,
  hideProfileModal,
  showSettingsModal,
  hideSettingsModal,
  setActiveSettingsTab,
  profileUpdated,
  avatarUpdated,
  subscriptionUpdated,
  updateLastActive,
  incrementTransactionCount,
  updateSpentAmount,
  updateEarnedAmount,
  updateRating
} = userSlice.actions;

export default userSlice.reducer;
