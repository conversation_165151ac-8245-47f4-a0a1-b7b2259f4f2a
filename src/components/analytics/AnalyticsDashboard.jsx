import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  UsersIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ArrowPathIcon,
  CalendarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { analyticsAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import MetricCard from './MetricCard';
import ChartContainer from './ChartContainer';
import RevenueChart from './RevenueChart';
import TransactionChart from './TransactionChart';
import UserGrowthChart from './UserGrowthChart';
import logger from '../helpers/logger';

function AnalyticsDashboard({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isSeller, isAdmin } = useRoleAccess();
  
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState('30d');
  const [selectedMetrics, setSelectedMetrics] = useState('overview');

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Determine which analytics to fetch based on user role
      let response;
      if (isAdmin()) {
        response = await analyticsAPI.getAdminAnalytics({ period: dateRange });
      } else if (isSeller()) {
        response = await analyticsAPI.getSellerAnalytics({ period: dateRange });
      } else {
        response = await analyticsAPI.getUserAnalytics({ period: dateRange });
      }
      
      if (response.data.success) {
        setAnalytics(response.data.analytics);
      }
    } catch (error) {
      logger.error('Failed to fetch analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  const calculatePercentageChange = (current, previous) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const getMetricCards = () => {
    if (!analytics) return [];

    const cards = [];

    if (isAdmin()) {
      cards.push(
        {
          title: 'Total Revenue',
          value: formatCurrency(analytics.revenue?.total),
          change: calculatePercentageChange(analytics.revenue?.current, analytics.revenue?.previous),
          icon: CurrencyDollarIcon,
          color: 'green'
        },
        {
          title: 'Total Transactions',
          value: formatNumber(analytics.transactions?.total),
          change: calculatePercentageChange(analytics.transactions?.current, analytics.transactions?.previous),
          icon: ShoppingCartIcon,
          color: 'blue'
        },
        {
          title: 'Active Users',
          value: formatNumber(analytics.users?.active),
          change: calculatePercentageChange(analytics.users?.current, analytics.users?.previous),
          icon: UsersIcon,
          color: 'purple'
        },
        {
          title: 'Conversion Rate',
          value: `${analytics.conversion?.rate || 0}%`,
          change: calculatePercentageChange(analytics.conversion?.current, analytics.conversion?.previous),
          icon: TrendingUpIcon,
          color: 'yellow'
        }
      );
    } else if (isSeller()) {
      cards.push(
        {
          title: 'Sales Revenue',
          value: formatCurrency(analytics.sales?.revenue),
          change: calculatePercentageChange(analytics.sales?.current, analytics.sales?.previous),
          icon: CurrencyDollarIcon,
          color: 'green'
        },
        {
          title: 'Orders',
          value: formatNumber(analytics.orders?.total),
          change: calculatePercentageChange(analytics.orders?.current, analytics.orders?.previous),
          icon: ShoppingCartIcon,
          color: 'blue'
        },
        {
          title: 'Products Sold',
          value: formatNumber(analytics.products?.sold),
          change: calculatePercentageChange(analytics.products?.current, analytics.products?.previous),
          icon: ChartBarIcon,
          color: 'purple'
        },
        {
          title: 'Avg Order Value',
          value: formatCurrency(analytics.orders?.average_value),
          change: calculatePercentageChange(analytics.orders?.avg_current, analytics.orders?.avg_previous),
          icon: TrendingUpIcon,
          color: 'yellow'
        }
      );
    } else {
      cards.push(
        {
          title: 'Total Spent',
          value: formatCurrency(analytics.spending?.total),
          change: calculatePercentageChange(analytics.spending?.current, analytics.spending?.previous),
          icon: CurrencyDollarIcon,
          color: 'green'
        },
        {
          title: 'Orders Placed',
          value: formatNumber(analytics.orders?.total),
          change: calculatePercentageChange(analytics.orders?.current, analytics.orders?.previous),
          icon: ShoppingCartIcon,
          color: 'blue'
        },
        {
          title: 'Items Purchased',
          value: formatNumber(analytics.items?.total),
          change: calculatePercentageChange(analytics.items?.current, analytics.items?.previous),
          icon: ChartBarIcon,
          color: 'purple'
        },
        {
          title: 'Savings from Escrow',
          value: formatCurrency(analytics.savings?.total),
          change: calculatePercentageChange(analytics.savings?.current, analytics.savings?.previous),
          icon: TrendingUpIcon,
          color: 'yellow'
        }
      );
    }

    return cards;
  };

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ];

  const metricOptions = [
    { value: 'overview', label: 'Overview' },
    { value: 'revenue', label: 'Revenue' },
    { value: 'transactions', label: 'Transactions' },
    { value: 'users', label: 'Users' },
    { value: 'products', label: 'Products' }
  ];

  if (loading && !analytics) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, index) => (
              <div key={index} className="h-80 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ChartBarIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isAdmin() ? 'Platform Analytics' : isSeller() ? 'Sales Analytics' : 'My Analytics'}
            </h1>
            <p className="text-gray-600 mt-1">
              {isAdmin() 
                ? 'Monitor platform performance and user activity'
                : isSeller()
                ? 'Track your sales performance and growth'
                : 'View your activity and spending insights'
              }
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <select
            value={selectedMetrics}
            onChange={(e) => setSelectedMetrics(e.target.value)}
            className="block px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {metricOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="block px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {dateRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <button
            onClick={fetchAnalytics}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {getMetricCards().map((card, index) => (
          <MetricCard
            key={index}
            title={card.title}
            value={card.value}
            change={card.change}
            icon={card.icon}
            color={card.color}
          />
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        {analytics?.revenue_chart && (
          <ChartContainer title="Revenue Trend" loading={loading}>
            <RevenueChart data={analytics.revenue_chart} />
          </ChartContainer>
        )}

        {/* Transaction Chart */}
        {analytics?.transaction_chart && (
          <ChartContainer title="Transaction Volume" loading={loading}>
            <TransactionChart data={analytics.transaction_chart} />
          </ChartContainer>
        )}
      </div>

      {/* Additional Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart (Admin only) */}
        {isAdmin() && analytics?.user_growth_chart && (
          <ChartContainer title="User Growth" loading={loading}>
            <UserGrowthChart data={analytics.user_growth_chart} />
          </ChartContainer>
        )}

        {/* Top Products/Categories */}
        {analytics?.top_items && (
          <ChartContainer title={isSeller() ? "Top Products" : "Popular Categories"} loading={loading}>
            <div className="space-y-4">
              {analytics.top_items.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {index + 1}
                        </span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{item.name}</p>
                      <p className="text-xs text-gray-500">{item.category}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(item.revenue)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatNumber(item.sales)} sales
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ChartContainer>
        )}
      </div>

      {/* Recent Activity */}
      {analytics?.recent_activity && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          
          <div className="space-y-4">
            {analytics.recent_activity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <ChartBarIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <div className="flex items-center space-x-4 mt-1">
                    <p className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                    {activity.amount && (
                      <p className="text-xs font-medium text-green-600">
                        {formatCurrency(activity.amount)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Insights */}
      {analytics?.insights && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <TrendingUpIcon className="h-6 w-6 text-blue-600 mt-0.5" />
            <div>
              <h3 className="text-lg font-medium text-blue-900">Performance Insights</h3>
              <div className="text-sm text-blue-800 mt-2 space-y-2">
                {analytics.insights.map((insight, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-blue-600">•</span>
                    <p>{insight}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AnalyticsDashboard;
