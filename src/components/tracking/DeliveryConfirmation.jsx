import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  StarIcon,
  CameraIcon,
  DocumentTextIcon,
  TruckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { externalTransactionAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';

function DeliveryConfirmation({ transaction, onConfirmed, onClose }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showRating, setShowRating] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [uploadedImages, setUploadedImages] = useState([]);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      delivery_status: '',
      condition: '',
      notes: '',
      rating_comment: ''
    }
  });

  const watchedDeliveryStatus = watch('delivery_status');

  const deliveryStatuses = [
    {
      value: 'received_good_condition',
      label: 'Received in Good Condition',
      description: 'Item received as expected, no issues',
      icon: CheckCircleIcon,
      color: 'green'
    },
    {
      value: 'received_minor_issues',
      label: 'Received with Minor Issues',
      description: 'Item received but with some minor problems',
      icon: ExclamationTriangleIcon,
      color: 'yellow'
    },
    {
      value: 'received_major_issues',
      label: 'Received with Major Issues',
      description: 'Item received but significantly different from description',
      icon: XCircleIcon,
      color: 'red'
    },
    {
      value: 'not_received',
      label: 'Not Received',
      description: 'Item has not been delivered yet',
      icon: ClockIcon,
      color: 'gray'
    }
  ];

  const conditionOptions = [
    { value: 'excellent', label: 'Excellent - Perfect condition' },
    { value: 'good', label: 'Good - Minor wear/imperfections' },
    { value: 'fair', label: 'Fair - Noticeable issues but functional' },
    { value: 'poor', label: 'Poor - Significant damage or defects' },
    { value: 'damaged', label: 'Damaged - Item is broken/unusable' }
  ];

  const handleImageUpload = (event) => {
    const files = Array.from(event.target.files);
    const maxFiles = 5;
    
    if (uploadedImages.length + files.length > maxFiles) {
      toast.error(`You can only upload up to ${maxFiles} images`);
      return;
    }

    files.forEach(file => {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImages(prev => [...prev, {
          file,
          preview: e.target.result,
          name: file.name
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (formData) => {
    try {
      setIsSubmitting(true);

      // Prepare form data for submission
      const submitData = new FormData();
      submitData.append('delivery_status', formData.delivery_status);
      submitData.append('condition', formData.condition);
      submitData.append('notes', formData.notes);
      
      if (showRating) {
        submitData.append('seller_rating', rating);
        submitData.append('rating_comment', formData.rating_comment);
      }

      // Add images
      uploadedImages.forEach((image, index) => {
        submitData.append(`delivery_images[${index}]`, image.file);
      });

      const response = await externalTransactionAPI.confirmDelivery(transaction.id, submitData);

      if (response.data.success) {
        toast.success('Delivery confirmed successfully!');
        onConfirmed?.(response.data.transaction);
        onClose?.();
      } else {
        throw new Error(response.data.error || 'Failed to confirm delivery');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to confirm delivery');
    } finally {
      setIsSubmitting(false);
    }
  };

  const shouldShowRating = watchedDeliveryStatus === 'received_good_condition' || 
                          watchedDeliveryStatus === 'received_minor_issues';

  React.useEffect(() => {
    setShowRating(shouldShowRating);
    if (!shouldShowRating) {
      setRating(0);
    }
  }, [shouldShowRating]);

  if (!transaction) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Confirm Delivery
              </h3>
              <p className="text-sm text-gray-600">
                {transaction.product_name}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-6">
          {/* Delivery Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Delivery Status *
            </label>
            <div className="space-y-3">
              {deliveryStatuses.map((status) => {
                const StatusIcon = status.icon;
                return (
                  <label
                    key={status.value}
                    className={`relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      watchedDeliveryStatus === status.value
                        ? `border-${status.color}-500 bg-${status.color}-50`
                        : 'border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      value={status.value}
                      {...register('delivery_status', { required: 'Please select delivery status' })}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${
                        watchedDeliveryStatus === status.value 
                          ? `bg-${status.color}-100` 
                          : 'bg-gray-100'
                      }`}>
                        <StatusIcon className={`h-5 w-5 ${
                          watchedDeliveryStatus === status.value 
                            ? `text-${status.color}-600` 
                            : 'text-gray-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{status.label}</div>
                        <p className="text-sm text-gray-600">{status.description}</p>
                      </div>
                    </div>
                    {watchedDeliveryStatus === status.value && (
                      <CheckCircleIcon className={`h-5 w-5 text-${status.color}-600`} />
                    )}
                  </label>
                );
              })}
            </div>
            {errors.delivery_status && (
              <p className="mt-1 text-sm text-red-600">{errors.delivery_status.message}</p>
            )}
          </div>

          {/* Item Condition */}
          {watchedDeliveryStatus && watchedDeliveryStatus !== 'not_received' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Item Condition *
              </label>
              <select
                {...register('condition', { required: 'Please select item condition' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Select condition...</option>
                {conditionOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.condition && (
                <p className="mt-1 text-sm text-red-600">{errors.condition.message}</p>
              )}
            </div>
          )}

          {/* Seller Rating */}
          {showRating && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Rate the Seller
              </label>
              <div className="flex items-center space-x-1 mb-3">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setRating(star)}
                    onMouseEnter={() => setHoverRating(star)}
                    onMouseLeave={() => setHoverRating(0)}
                    className="focus:outline-none"
                  >
                    {star <= (hoverRating || rating) ? (
                      <StarIconSolid className="h-8 w-8 text-yellow-400" />
                    ) : (
                      <StarIcon className="h-8 w-8 text-gray-300" />
                    )}
                  </button>
                ))}
                <span className="ml-2 text-sm text-gray-600">
                  {rating > 0 ? `${rating} star${rating > 1 ? 's' : ''}` : 'No rating'}
                </span>
              </div>
              
              <textarea
                {...register('rating_comment')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Share your experience with this seller (optional)..."
              />
            </div>
          )}

          {/* Upload Images */}
          {watchedDeliveryStatus && watchedDeliveryStatus !== 'not_received' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Photos (Optional)
              </label>
              <p className="text-xs text-gray-500 mb-3">
                Upload photos of the received item. Maximum 5 images, 5MB each.
              </p>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="cursor-pointer flex flex-col items-center justify-center"
                >
                  <CameraIcon className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-600">Click to upload images</span>
                </label>
              </div>

              {/* Image Previews */}
              {uploadedImages.length > 0 && (
                <div className="mt-3 grid grid-cols-3 gap-3">
                  {uploadedImages.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.preview}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-20 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <XCircleIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              {...register('notes')}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Any additional comments about the delivery or item condition..."
            />
          </div>

          {/* Warning for problematic deliveries */}
          {(watchedDeliveryStatus === 'received_major_issues' || watchedDeliveryStatus === 'not_received') && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3 mt-0.5" />
                <div className="text-sm">
                  <h4 className="font-medium text-yellow-800 mb-1">Need Help?</h4>
                  <p className="text-yellow-700">
                    If you're experiencing issues with your delivery, consider opening a dispute 
                    instead of confirming delivery. This will help protect your payment.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Confirming...
                </>
              ) : (
                <>
                  <CheckCircleIcon className="h-4 w-4 mr-2" />
                  Confirm Delivery
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default DeliveryConfirmation;
