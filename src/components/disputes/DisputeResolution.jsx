import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import {
  ShieldCheckIcon,
  ScaleIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { disputeAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function DisputeResolution({ dispute, onResolution, className = '' }) {
  const { hasPermission } = useRoleAccess();
  
  const [loading, setLoading] = useState(false);
  const [showResolutionForm, setShowResolutionForm] = useState(false);
  const [resolutionType, setResolutionType] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      resolution_type: '',
      resolution_notes: '',
      refund_amount: '',
      refund_percentage: 100,
      action_required: '',
      follow_up_required: false
    }
  });

  const watchedResolutionType = watch('resolution_type');
  const watchedRefundPercentage = watch('refund_percentage');

  useEffect(() => {
    if (watchedResolutionType) {
      setResolutionType(watchedResolutionType);
    }
  }, [watchedResolutionType]);

  const handleResolveDispute = async (data) => {
    try {
      setLoading(true);
      
      const resolutionData = {
        dispute_id: dispute.id,
        resolution_type: data.resolution_type,
        resolution_notes: data.resolution_notes,
        refund_amount: data.refund_amount || null,
        refund_percentage: data.refund_percentage || null,
        action_required: data.action_required || null,
        follow_up_required: data.follow_up_required || false
      };

      const response = await disputeAPI.resolveDispute(dispute.id, resolutionData);
      
      if (response.data.success) {
        toast.success('Dispute resolved successfully');
        setShowResolutionForm(false);
        reset();
        onResolution?.(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to resolve dispute:', error);
      toast.error('Failed to resolve dispute');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickResolution = async (type) => {
    try {
      setLoading(true);
      
      let resolutionData = {
        dispute_id: dispute.id,
        resolution_type: type
      };

      // Set default values based on resolution type
      switch (type) {
        case 'favor_buyer':
          resolutionData.resolution_notes = 'Dispute resolved in favor of the buyer based on evidence provided.';
          resolutionData.refund_percentage = 100;
          break;
        case 'favor_seller':
          resolutionData.resolution_notes = 'Dispute resolved in favor of the seller. No refund required.';
          break;
        case 'partial_refund':
          resolutionData.resolution_notes = 'Partial refund approved based on dispute circumstances.';
          resolutionData.refund_percentage = 50;
          break;
        case 'dismiss':
          resolutionData.resolution_notes = 'Dispute dismissed due to insufficient evidence or invalid claim.';
          break;
      }

      const response = await disputeAPI.resolveDispute(dispute.id, resolutionData);
      
      if (response.data.success) {
        toast.success('Dispute resolved successfully');
        onResolution?.(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to resolve dispute:', error);
      toast.error('Failed to resolve dispute');
    } finally {
      setLoading(false);
    }
  };

  const calculateRefundAmount = () => {
    if (!dispute.transaction?.amount || !watchedRefundPercentage) return 0;
    return (dispute.transaction.amount * watchedRefundPercentage / 100).toFixed(2);
  };

  if (!hasPermission('dispute.resolve')) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">You don't have permission to resolve disputes.</p>
      </div>
    );
  }

  if (dispute.status === 'resolved' || dispute.status === 'closed') {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="text-center">
          <CheckCircleIcon className="mx-auto h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Dispute Already Resolved</h3>
          <p className="text-gray-500">This dispute has already been resolved.</p>
          
          {dispute.resolution && (
            <div className="mt-6 bg-gray-50 rounded-lg p-4 text-left">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Resolution Details</h4>
              <div className="space-y-2 text-sm text-gray-700">
                <div>
                  <span className="font-medium">Type:</span> {dispute.resolution.type}
                </div>
                {dispute.resolution.notes && (
                  <div>
                    <span className="font-medium">Notes:</span> {dispute.resolution.notes}
                  </div>
                )}
                {dispute.resolution.refund_amount && (
                  <div>
                    <span className="font-medium">Refund Amount:</span> GH₵{dispute.resolution.refund_amount}
                  </div>
                )}
                <div>
                  <span className="font-medium">Resolved On:</span> {new Date(dispute.resolution.created_at).toLocaleString()}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <ScaleIcon className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">Dispute Resolution</h2>
            <p className="text-gray-600 mt-1">Resolve dispute #{dispute.id}</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Resolution</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <button
            onClick={() => handleQuickResolution('favor_buyer')}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            Favor Buyer
          </button>
          
          <button
            onClick={() => handleQuickResolution('favor_seller')}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            Favor Seller
          </button>
          
          <button
            onClick={() => handleQuickResolution('partial_refund')}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <BanknotesIcon className="h-5 w-5 mr-2" />
            Partial Refund
          </button>
          
          <button
            onClick={() => handleQuickResolution('dismiss')}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <XCircleIcon className="h-5 w-5 mr-2" />
            Dismiss
          </button>
        </div>
      </div>

      {/* Custom Resolution */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Custom Resolution</h3>
          
          {!showResolutionForm && (
            <button
              onClick={() => setShowResolutionForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <DocumentTextIcon className="h-4 w-4 mr-2" />
              Create Custom Resolution
            </button>
          )}
        </div>

        {showResolutionForm && (
          <form onSubmit={handleSubmit(handleResolveDispute)} className="space-y-6">
            {/* Resolution Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Resolution Type <span className="text-red-500">*</span>
              </label>
              <select
                {...register('resolution_type', { required: 'Please select a resolution type' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select resolution type</option>
                <option value="favor_buyer">Favor Buyer (Full Refund)</option>
                <option value="favor_seller">Favor Seller (No Refund)</option>
                <option value="partial_refund">Partial Refund</option>
                <option value="replacement">Replacement Required</option>
                <option value="store_credit">Store Credit</option>
                <option value="dismiss">Dismiss Dispute</option>
                <option value="escalate">Escalate to Senior Moderator</option>
              </select>
              {errors.resolution_type && (
                <p className="text-red-600 text-sm mt-1">{errors.resolution_type.message}</p>
              )}
            </div>

            {/* Refund Details */}
            {(resolutionType === 'favor_buyer' || resolutionType === 'partial_refund') && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Refund Details</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Refund Percentage
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        step="5"
                        {...register('refund_percentage')}
                        className="flex-1"
                      />
                      <span className="text-sm font-medium text-gray-900 w-12">
                        {watchedRefundPercentage}%
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Refund Amount
                    </label>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">GH₵</span>
                      <input
                        type="number"
                        step="0.01"
                        value={calculateRefundAmount()}
                        {...register('refund_amount')}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Resolution Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Resolution Notes <span className="text-red-500">*</span>
              </label>
              <textarea
                {...register('resolution_notes', { 
                  required: 'Please provide resolution notes',
                  minLength: {
                    value: 20,
                    message: 'Resolution notes must be at least 20 characters'
                  }
                })}
                rows={4}
                placeholder="Explain the reasoning behind this resolution decision..."
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.resolution_notes && (
                <p className="text-red-600 text-sm mt-1">{errors.resolution_notes.message}</p>
              )}
            </div>

            {/* Action Required */}
            {(resolutionType === 'replacement' || resolutionType === 'escalate') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Action Required
                </label>
                <textarea
                  {...register('action_required')}
                  rows={3}
                  placeholder="Describe what action needs to be taken..."
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}

            {/* Follow-up Required */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('follow_up_required')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Follow-up required to ensure resolution is implemented
                </span>
              </label>
            </div>

            {/* Warning */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-yellow-800">Important</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This action cannot be undone. Both parties will be notified of the resolution decision.
                    Any refunds will be processed automatically.
                  </p>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={() => {
                  setShowResolutionForm(false);
                  reset();
                }}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Resolving...
                  </>
                ) : (
                  'Resolve Dispute'
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default DisputeResolution;
