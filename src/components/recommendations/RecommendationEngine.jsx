import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  SparklesIcon,
  HeartIcon,
  EyeIcon,
  ShoppingCartIcon,
  UserGroupIcon,
  TrendingUpIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';
import { recommendationAPI } from '../../services/apiServices';
import ProductCard from '../products/ProductCard';
import logger from '../helpers/logger';

function RecommendationEngine({ 
  type = 'personalized', // 'personalized', 'trending', 'similar', 'category'
  productId = null,
  categoryId = null,
  limit = 8,
  title,
  showTitle = true,
  className = '' 
}) {
  const { user } = useSelector((state) => state.auth);
  
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRecommendations();
  }, [type, productId, categoryId, limit, user?.id]);

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      const params = { limit, user_id: user?.id };

      switch (type) {
        case 'personalized':
          response = await recommendationAPI.getPersonalizedRecommendations(params);
          break;
        case 'trending':
          response = await recommendationAPI.getTrendingProducts(params);
          break;
        case 'similar':
          if (!productId) {
            setError('Product ID required for similar recommendations');
            return;
          }
          response = await recommendationAPI.getSimilarProducts(productId, params);
          break;
        case 'category':
          if (!categoryId) {
            setError('Category ID required for category recommendations');
            return;
          }
          response = await recommendationAPI.getCategoryRecommendations(categoryId, params);
          break;
        case 'recently_viewed':
          response = await recommendationAPI.getRecentlyViewed(params);
          break;
        case 'wishlist_based':
          response = await recommendationAPI.getWishlistBasedRecommendations(params);
          break;
        case 'collaborative':
          response = await recommendationAPI.getCollaborativeRecommendations(params);
          break;
        default:
          response = await recommendationAPI.getPersonalizedRecommendations(params);
      }
      
      if (response.data.success) {
        setRecommendations(response.data.recommendations || []);
      }
    } catch (error) {
      logger.error('Failed to fetch recommendations:', error);
      setError('Failed to load recommendations');
    } finally {
      setLoading(false);
    }
  };

  const handleProductClick = async (product) => {
    try {
      // Track product view for better recommendations
      await recommendationAPI.trackInteraction({
        type: 'view',
        product_id: product.id,
        user_id: user?.id,
        recommendation_type: type
      });
    } catch (error) {
      logger.error('Failed to track interaction:', error);
    }
  };

  const handleAddToCart = async (product) => {
    try {
      // Track add to cart interaction
      await recommendationAPI.trackInteraction({
        type: 'add_to_cart',
        product_id: product.id,
        user_id: user?.id,
        recommendation_type: type
      });
      
      toast.success('Added to cart');
    } catch (error) {
      logger.error('Failed to track interaction:', error);
    }
  };

  const handleToggleWishlist = async (product) => {
    try {
      // Track wishlist interaction
      await recommendationAPI.trackInteraction({
        type: 'wishlist',
        product_id: product.id,
        user_id: user?.id,
        recommendation_type: type
      });
      
      toast.success(product.in_wishlist ? 'Removed from wishlist' : 'Added to wishlist');
    } catch (error) {
      logger.error('Failed to track interaction:', error);
    }
  };

  const getRecommendationTitle = () => {
    if (title) return title;
    
    switch (type) {
      case 'personalized':
        return 'Recommended for You';
      case 'trending':
        return 'Trending Now';
      case 'similar':
        return 'Similar Products';
      case 'category':
        return 'More in This Category';
      case 'recently_viewed':
        return 'Recently Viewed';
      case 'wishlist_based':
        return 'Based on Your Wishlist';
      case 'collaborative':
        return 'Others Also Liked';
      default:
        return 'Recommendations';
    }
  };

  const getRecommendationIcon = () => {
    switch (type) {
      case 'personalized':
        return <SparklesIcon className="h-5 w-5 text-blue-600" />;
      case 'trending':
        return <TrendingUpIcon className="h-5 w-5 text-red-600" />;
      case 'similar':
        return <EyeIcon className="h-5 w-5 text-green-600" />;
      case 'category':
        return <ShoppingCartIcon className="h-5 w-5 text-purple-600" />;
      case 'recently_viewed':
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
      case 'wishlist_based':
        return <HeartIcon className="h-5 w-5 text-pink-600" />;
      case 'collaborative':
        return <UserGroupIcon className="h-5 w-5 text-indigo-600" />;
      default:
        return <SparklesIcon className="h-5 w-5 text-blue-600" />;
    }
  };

  const getRecommendationDescription = () => {
    switch (type) {
      case 'personalized':
        return 'Curated based on your browsing history and preferences';
      case 'trending':
        return 'Popular products that are trending right now';
      case 'similar':
        return 'Products similar to what you\'re viewing';
      case 'category':
        return 'Explore more products in this category';
      case 'recently_viewed':
        return 'Products you\'ve recently looked at';
      case 'wishlist_based':
        return 'Recommendations based on your saved items';
      case 'collaborative':
        return 'Products liked by users with similar interests';
      default:
        return 'Personalized product recommendations';
    }
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        {showTitle && (
          <div className="flex items-center space-x-3 mb-6">
            <div className="h-6 w-6 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
        )}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(limit)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 text-center ${className}`}>
        <SparklesIcon className="mx-auto h-12 w-12 text-red-400 mb-4" />
        <h3 className="text-lg font-medium text-red-900 mb-2">Failed to Load Recommendations</h3>
        <p className="text-red-700 mb-4">{error}</p>
        <button
          onClick={fetchRecommendations}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!recommendations || recommendations.length === 0) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-6 text-center ${className}`}>
        <SparklesIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Recommendations Available</h3>
        <p className="text-gray-500">
          {type === 'personalized' 
            ? 'Browse more products to get personalized recommendations'
            : 'Check back later for new recommendations'
          }
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      {showTitle && (
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            {getRecommendationIcon()}
            <h2 className="text-2xl font-bold text-gray-900">
              {getRecommendationTitle()}
            </h2>
          </div>
          <p className="text-gray-600">{getRecommendationDescription()}</p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product, index) => (
          <div key={`${product.id}-${index}`} className="relative group">
            <ProductCard
              product={product}
              onClick={() => handleProductClick(product)}
              onAddToCart={() => handleAddToCart(product)}
              onToggleWishlist={() => handleToggleWishlist(product)}
              showQuickActions={true}
            />
            
            {/* Recommendation Badge */}
            {product.recommendation_score && (
              <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                {Math.round(product.recommendation_score * 100)}% match
              </div>
            )}
            
            {/* Trending Badge */}
            {type === 'trending' && product.trending_score && (
              <div className="absolute top-2 right-2 bg-red-600 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center space-x-1">
                <TrendingUpIcon className="h-3 w-3" />
                <span>Hot</span>
              </div>
            )}
            
            {/* Recently Viewed Badge */}
            {type === 'recently_viewed' && product.last_viewed && (
              <div className="absolute bottom-2 left-2 bg-gray-800 text-white text-xs px-2 py-1 rounded">
                Viewed {new Date(product.last_viewed).toLocaleDateString()}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {recommendations.length >= limit && (
        <div className="text-center mt-8">
          <button
            onClick={() => fetchRecommendations()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            Load More Recommendations
          </button>
        </div>
      )}

      {/* Feedback Section */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">How are these recommendations?</h4>
            <p className="text-xs text-gray-500 mt-1">Your feedback helps us improve</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => recommendationAPI.provideFeedback({ type, rating: 'good' })}
              className="p-2 text-green-600 hover:bg-green-100 rounded-full"
              title="Good recommendations"
            >
              👍
            </button>
            <button
              onClick={() => recommendationAPI.provideFeedback({ type, rating: 'bad' })}
              className="p-2 text-red-600 hover:bg-red-100 rounded-full"
              title="Poor recommendations"
            >
              👎
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RecommendationEngine;
