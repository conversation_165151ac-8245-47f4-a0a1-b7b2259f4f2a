import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
import logger from '../helpers/logger';
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  TrendingUpIcon,
  HashtagIcon
} from '@heroicons/react/24/outline';

function ProductSearch({ 
  onSearch, 
  initialQuery = '', 
  placeholder = 'Search products...', 
  showSuggestions = true,
  className = '' 
}) {
  const navigate = useNavigate();
  const [query, setQuery] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [trendingSearches, setTrendingSearches] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchRef = useRef(null);
  const dropdownRef = useRef(null);
  const debounceRef = useRef(null);

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('payhold_recent_searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }

    // Load trending searches (placeholder data)
    setTrendingSearches([
      'iPhone 14',
      'Samsung Galaxy',
      'MacBook Pro',
      'Nike Air Force',
      'PlayStation 5',
      'AirPods Pro'
    ]);
  }, []);

  useEffect(() => {
    if (query.length > 2 && showSuggestions) {
      // Debounce search suggestions
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
      
      debounceRef.current = setTimeout(() => {
        fetchSuggestions(query);
      }, 300);
    } else {
      setSuggestions([]);
    }

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, showSuggestions]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchSuggestions = async (searchQuery) => {
    try {
      setIsLoading(true);
      // TODO: Implement API call for search suggestions
      // const response = await productAPI.getSearchSuggestions(searchQuery);
      // setSuggestions(response.data.suggestions);
      
      // Placeholder suggestions for demo
      const mockSuggestions = [
        { type: 'product', text: `${searchQuery} Pro`, count: 45 },
        { type: 'product', text: `${searchQuery} Max`, count: 32 },
        { type: 'product', text: `${searchQuery} Mini`, count: 28 },
        { type: 'category', text: `${searchQuery} Accessories`, count: 156 },
        { type: 'brand', text: `${searchQuery} Brand`, count: 89 }
      ].filter(item => item.text.toLowerCase().includes(searchQuery.toLowerCase()));
      
      setSuggestions(mockSuggestions);
    } catch (error) {
      logger.error('Failed to fetch suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    // Save to recent searches
    const updatedRecent = [
      searchQuery,
      ...recentSearches.filter(item => item !== searchQuery)
    ].slice(0, 10);
    
    setRecentSearches(updatedRecent);
    localStorage.setItem('payhold_recent_searches', JSON.stringify(updatedRecent));

    // Close dropdown
    setShowDropdown(false);
    setSelectedIndex(-1);

    // Trigger search
    if (onSearch) {
      onSearch(searchQuery);
    } else {
      navigate(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleKeyDown = (e) => {
    const allItems = [
      ...suggestions,
      ...recentSearches.map(item => ({ type: 'recent', text: item })),
      ...trendingSearches.map(item => ({ type: 'trending', text: item }))
    ];

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, allItems.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && allItems[selectedIndex]) {
          handleSearch(allItems[selectedIndex].text);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowDropdown(false);
        setSelectedIndex(-1);
        searchRef.current?.blur();
        break;
    }
  };

  const clearSearch = () => {
    setQuery('');
    setShowDropdown(false);
    setSelectedIndex(-1);
    searchRef.current?.focus();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('payhold_recent_searches');
  };

  const removeRecentSearch = (searchToRemove) => {
    const updated = recentSearches.filter(item => item !== searchToRemove);
    setRecentSearches(updated);
    localStorage.setItem('payhold_recent_searches', JSON.stringify(updated));
  };

  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'recent':
        return <ClockIcon className="h-4 w-4 text-gray-400" />;
      case 'trending':
        return <TrendingUpIcon className="h-4 w-4 text-red-400" />;
      case 'category':
        return <HashtagIcon className="h-4 w-4 text-blue-400" />;
      default:
        return <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSuggestionLabel = (type) => {
    switch (type) {
      case 'recent':
        return 'Recent';
      case 'trending':
        return 'Trending';
      case 'category':
        return 'Category';
      case 'brand':
        return 'Brand';
      default:
        return 'Product';
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={searchRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setShowDropdown(true)}
          onKeyDown={handleKeyDown}
          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm placeholder-gray-500"
          placeholder={placeholder}
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Search Dropdown */}
      {showDropdown && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="px-4 py-3 text-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Searching...</p>
            </div>
          )}

          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <div className="border-b border-gray-100">
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Suggestions
              </div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={`suggestion-${index}`}
                  onClick={() => handleSearch(suggestion.text)}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between ${
                    selectedIndex === index ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {getSuggestionIcon(suggestion.type)}
                    <span className="text-sm text-gray-900">{suggestion.text}</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                      {getSuggestionLabel(suggestion.type)}
                    </span>
                  </div>
                  {suggestion.count && (
                    <span className="text-xs text-gray-400">
                      {suggestion.count} results
                    </span>
                  )}
                </button>
              ))}
            </div>
          )}

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="border-b border-gray-100">
              <div className="px-4 py-2 flex items-center justify-between">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Recent Searches
                </span>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Clear all
                </button>
              </div>
              {recentSearches.slice(0, 5).map((search, index) => (
                <button
                  key={`recent-${index}`}
                  onClick={() => handleSearch(search)}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between group ${
                    selectedIndex === suggestions.length + index ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <ClockIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{search}</span>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeRecentSearch(search);
                    }}
                    className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 rounded"
                  >
                    <XMarkIcon className="h-3 w-3 text-gray-400" />
                  </button>
                </button>
              ))}
            </div>
          )}

          {/* Trending Searches */}
          {trendingSearches.length > 0 && query.length === 0 && (
            <div>
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Trending Searches
              </div>
              {trendingSearches.slice(0, 6).map((search, index) => (
                <button
                  key={`trending-${index}`}
                  onClick={() => handleSearch(search)}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                    selectedIndex === suggestions.length + recentSearches.length + index ? 'bg-blue-50' : ''
                  }`}
                >
                  <TrendingUpIcon className="h-4 w-4 text-red-400" />
                  <span className="text-sm text-gray-900">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && suggestions.length === 0 && recentSearches.length === 0 && query.length > 2 && (
            <div className="px-4 py-8 text-center">
              <MagnifyingGlassIcon className="mx-auto h-8 w-8 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500">No suggestions found</p>
              <button
                onClick={() => handleSearch()}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
              >
                Search for "{query}"
              </button>
            </div>
          )}

          {/* Search Button */}
          {query.length > 0 && (
            <div className="border-t border-gray-100 p-3">
              <button
                onClick={() => handleSearch()}
                className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
              >
                <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Search for "{query}"
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ProductSearch;
