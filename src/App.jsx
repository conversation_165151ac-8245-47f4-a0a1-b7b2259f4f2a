import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ToastContainer } from 'react-toastify';
import store from './redux/store';
import useAuth from './hooks/useAuth';
import { SocketProvider } from './contexts/SocketContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import LoadingSpinner from './components/helpers/LoadingSpinner';

// Import CSS
import 'react-toastify/dist/ReactToastify.css';
import './App.css';

// Lazy load components for better performance
const Header = React.lazy(() => import('./components/layout/Header'));
const Footer = React.lazy(() => import('./components/layout/Footer'));
const HomePage = React.lazy(() => import('./pages/HomePage'));
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const RegisterPage = React.lazy(() => import('./pages/RegisterPage'));
const ProductsPage = React.lazy(() => import('./pages/ProductsPage'));
const ProductDetailPage = React.lazy(() => import('./pages/ProductDetailPage'));
const CartPage = React.lazy(() => import('./pages/CartPage'));
const CheckoutPage = React.lazy(() => import('./pages/CheckoutPage'));
const DashboardLayout = React.lazy(() => import('./components/dashboard/DashboardLayout'));
const DashboardOverview = React.lazy(() => import('./components/dashboard/DashboardOverview'));
const ProtectedRoute = React.lazy(() => import('./components/auth/ProtectedRoute'));
const NotificationCenter = React.lazy(() => import('./components/notifications/NotificationCenter'));
const ChatManager = React.lazy(() => import('./components/chat/ChatManager'));

function AppContent() {
  const { isAuthenticated } = useAuth();

  return (
    <ErrorBoundary>
      <SocketProvider>
        <Router>
          <div>
            <Suspense fallback={<LoadingSpinner size="large" />}>
              <Header />
            </Suspense>

            {/* Notification Center - only show when authenticated */}
            {isAuthenticated && (
              <Suspense fallback={null}>
                <div className="fixed top-4 right-4 z-50">
                  <NotificationCenter />
                </div>
              </Suspense>
            )}

            {/* Chat Manager - only show when authenticated */}
            {isAuthenticated && (
              <Suspense fallback={null}>
                <ChatManager />
              </Suspense>
            )}

            <main>
              <Suspense fallback={(
                <div>
                  <LoadingSpinner size="large" />
                </div>
              )}
              >
                <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<HomePage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/products" element={<ProductsPage />} />
                  <Route path="/products/:id" element={<ProductDetailPage />} />

                  {/* Protected Routes */}
                  <Route
                    path="/cart"
                    element={(
                      <ProtectedRoute>
                        <CartPage />
                      </ProtectedRoute>
                  )}
                  />

                  <Route
                    path="/checkout"
                    element={(
                      <ProtectedRoute>
                        <CheckoutPage />
                      </ProtectedRoute>
                  )}
                  />

                  {/* Dashboard Routes */}
                  <Route
                    path="/dashboard"
                    element={(
                      <ProtectedRoute>
                        <DashboardLayout />
                      </ProtectedRoute>
                  )}
                  >
                    <Route index element={<DashboardOverview />} />
                    {/* Add more dashboard routes here */}
                  </Route>

                  {/* 404 Route */}
                  <Route
                    path="*"
                    element={(
                      <div>
                        <h1>Page Not Found</h1>
                        <p>The page you&apos;re looking for doesn&apos;t exist.</p>
                      </div>
                  )}
                  />
                </Routes>
              </Suspense>
            </main>

            <Suspense fallback={null}>
              <Footer />
            </Suspense>
          </div>

          {/* Toast Notifications */}
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </Router>
      </SocketProvider>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
