import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  BeakerIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { testingAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function TestSuite({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isAdmin } = useRoleAccess();
  
  const [testSuites, setTestSuites] = useState([]);
  const [runningTests, setRunningTests] = useState(new Set());
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedSuite, setSelectedSuite] = useState('all');

  useEffect(() => {
    if (hasPermission('testing.view')) {
      fetchTestSuites();
    }
  }, [hasPermission]);

  const fetchTestSuites = async () => {
    try {
      setLoading(true);
      const response = await testingAPI.getTestSuites();
      
      if (response.data.success) {
        setTestSuites(response.data.test_suites);
        setTestResults(response.data.latest_results || {});
      }
    } catch (error) {
      logger.error('Failed to fetch test suites:', error);
      toast.error('Failed to load test suites');
    } finally {
      setLoading(false);
    }
  };

  const runTestSuite = async (suiteId) => {
    if (!hasPermission('testing.run')) {
      toast.error('You do not have permission to run tests');
      return;
    }

    try {
      setRunningTests(prev => new Set([...prev, suiteId]));
      
      const response = await testingAPI.runTestSuite(suiteId);
      
      if (response.data.success) {
        setTestResults(prev => ({
          ...prev,
          [suiteId]: response.data.results
        }));
        
        const { passed, failed, total } = response.data.results.summary;
        if (failed === 0) {
          toast.success(`All ${total} tests passed!`);
        } else {
          toast.warning(`${passed}/${total} tests passed, ${failed} failed`);
        }
      }
    } catch (error) {
      logger.error('Test execution failed:', error);
      toast.error('Test execution failed');
    } finally {
      setRunningTests(prev => {
        const newSet = new Set(prev);
        newSet.delete(suiteId);
        return newSet;
      });
    }
  };

  const runAllTests = async () => {
    if (!hasPermission('testing.run')) {
      toast.error('You do not have permission to run tests');
      return;
    }

    try {
      setRunningTests(new Set(['all']));
      
      const response = await testingAPI.runAllTests();
      
      if (response.data.success) {
        setTestResults(response.data.results);
        
        const totalPassed = Object.values(response.data.results).reduce((sum, result) => 
          sum + (result.summary?.passed || 0), 0);
        const totalFailed = Object.values(response.data.results).reduce((sum, result) => 
          sum + (result.summary?.failed || 0), 0);
        const totalTests = totalPassed + totalFailed;
        
        if (totalFailed === 0) {
          toast.success(`All ${totalTests} tests passed!`);
        } else {
          toast.warning(`${totalPassed}/${totalTests} tests passed, ${totalFailed} failed`);
        }
      }
    } catch (error) {
      logger.error('Test execution failed:', error);
      toast.error('Test execution failed');
    } finally {
      setRunningTests(prev => {
        const newSet = new Set(prev);
        newSet.delete('all');
        return newSet;
      });
    }
  };

  const getTestStatusIcon = (status) => {
    switch (status) {
      case 'passed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'running':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getTestStatusColor = (status) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'running':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calculateOverallStatus = (results) => {
    if (!results || !results.summary) return 'pending';
    
    const { passed, failed, total } = results.summary;
    if (failed > 0) return 'failed';
    if (passed === total) return 'passed';
    return 'warning';
  };

  const formatDuration = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (!hasPermission('testing.view')) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">You don't have permission to view test results.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BeakerIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Test Suite</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive testing for PayHold platform
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <select
            value={selectedSuite}
            onChange={(e) => setSelectedSuite(e.target.value)}
            className="block px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Test Suites</option>
            {testSuites.map((suite) => (
              <option key={suite.id} value={suite.id}>
                {suite.name}
              </option>
            ))}
          </select>

          <button
            onClick={fetchTestSuites}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>

          {hasPermission('testing.run') && (
            <button
              onClick={runAllTests}
              disabled={runningTests.has('all')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {runningTests.has('all') ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Run All Tests
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Test Suites */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {testSuites
          .filter(suite => selectedSuite === 'all' || suite.id === selectedSuite)
          .map((suite) => {
            const results = testResults[suite.id];
            const isRunning = runningTests.has(suite.id);
            const status = isRunning ? 'running' : calculateOverallStatus(results);

            return (
              <div key={suite.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                {/* Suite Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    {getTestStatusIcon(status)}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {suite.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {suite.description}
                      </p>
                    </div>
                  </div>

                  {hasPermission('testing.run') && (
                    <button
                      onClick={() => runTestSuite(suite.id)}
                      disabled={isRunning}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      {isRunning ? (
                        <>
                          <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                          Running
                        </>
                      ) : (
                        <>
                          <PlayIcon className="h-4 w-4 mr-2" />
                          Run
                        </>
                      )}
                    </button>
                  )}
                </div>

                {/* Test Results */}
                {results && (
                  <div className="space-y-4">
                    {/* Summary */}
                    <div className={`p-3 rounded-lg border ${getTestStatusColor(status)}`}>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">
                            {results.summary.passed}/{results.summary.total} tests passed
                          </p>
                          {results.duration && (
                            <p className="text-xs mt-1">
                              Completed in {formatDuration(results.duration)}
                            </p>
                          )}
                        </div>
                        
                        <div className="text-right">
                          {results.summary.failed > 0 && (
                            <p className="text-sm font-medium text-red-600">
                              {results.summary.failed} failed
                            </p>
                          )}
                          {results.summary.skipped > 0 && (
                            <p className="text-xs text-gray-500">
                              {results.summary.skipped} skipped
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Individual Test Results */}
                    {results.tests && results.tests.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-900">Test Details</h4>
                        <div className="max-h-48 overflow-y-auto space-y-1">
                          {results.tests.map((test, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                            >
                              <div className="flex items-center space-x-2">
                                {getTestStatusIcon(test.status)}
                                <span className="text-gray-900">{test.name}</span>
                              </div>
                              
                              <div className="flex items-center space-x-2">
                                {test.duration && (
                                  <span className="text-xs text-gray-500">
                                    {formatDuration(test.duration)}
                                  </span>
                                )}
                                
                                {test.status === 'failed' && test.error && (
                                  <span
                                    className="text-xs text-red-600 cursor-help"
                                    title={test.error}
                                  >
                                    Error
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Suite Info */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{suite.test_count} tests</span>
                    <span>Category: {suite.category}</span>
                  </div>
                </div>
              </div>
            );
          })}
      </div>

      {/* Overall Statistics */}
      {Object.keys(testResults).length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <ChartBarIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">Test Statistics</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {(() => {
              const totalPassed = Object.values(testResults).reduce((sum, result) => 
                sum + (result.summary?.passed || 0), 0);
              const totalFailed = Object.values(testResults).reduce((sum, result) => 
                sum + (result.summary?.failed || 0), 0);
              const totalSkipped = Object.values(testResults).reduce((sum, result) => 
                sum + (result.summary?.skipped || 0), 0);
              const totalTests = totalPassed + totalFailed + totalSkipped;

              return (
                <>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{totalTests}</div>
                    <div className="text-sm text-gray-500">Total Tests</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{totalPassed}</div>
                    <div className="text-sm text-gray-500">Passed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{totalFailed}</div>
                    <div className="text-sm text-gray-500">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{totalSkipped}</div>
                    <div className="text-sm text-gray-500">Skipped</div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}

export default TestSuite;
