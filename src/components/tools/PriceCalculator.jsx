import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import {
  CalculatorIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  InformationCircleIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { toolsAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function PriceCalculator({ className = '' }) {
  const [calculation, setCalculation] = useState(null);
  const [marketData, setMarketData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      base_price: '',
      category: '',
      condition: 'new',
      shipping_cost: '',
      include_escrow_fee: true,
      profit_margin: '20',
      competitor_analysis: true
    }
  });

  const watchedValues = watch();

  useEffect(() => {
    if (watchedValues.base_price && parseFloat(watchedValues.base_price) > 0) {
      calculatePricing();
    }
  }, [watchedValues]);

  const calculatePricing = async () => {
    try {
      setLoading(true);
      
      const calculationData = {
        base_price: parseFloat(watchedValues.base_price) || 0,
        category: watchedValues.category,
        condition: watchedValues.condition,
        shipping_cost: parseFloat(watchedValues.shipping_cost) || 0,
        include_escrow_fee: watchedValues.include_escrow_fee,
        profit_margin: parseFloat(watchedValues.profit_margin) || 0,
        competitor_analysis: watchedValues.competitor_analysis
      };

      const response = await toolsAPI.calculatePricing(calculationData);
      
      if (response.data.success) {
        setCalculation(response.data.calculation);
        setMarketData(response.data.market_data);
      }
    } catch (error) {
      logger.error('Price calculation failed:', error);
      toast.error('Failed to calculate pricing');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  };

  const getRecommendationColor = (type) => {
    switch (type) {
      case 'increase':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'decrease':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'maintain':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'increase':
        return <TrendingUpIcon className="h-4 w-4" />;
      case 'decrease':
        return <TrendingDownIcon className="h-4 w-4" />;
      default:
        return <InformationCircleIcon className="h-4 w-4" />;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <CalculatorIcon className="h-6 w-6 text-green-600" />
        <div>
          <h2 className="text-xl font-bold text-gray-900">Price Calculator</h2>
          <p className="text-gray-600 mt-1">
            Calculate optimal pricing with fees and market analysis
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Form */}
        <div className="space-y-6">
          <form className="space-y-4">
            {/* Base Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base Price <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  step="0.01"
                  {...register('base_price', { 
                    required: 'Base price is required',
                    min: { value: 0.01, message: 'Price must be greater than 0' }
                  })}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="0.00"
                />
              </div>
              {errors.base_price && (
                <p className="text-red-600 text-sm mt-1">{errors.base_price.message}</p>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                {...register('category')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Select category</option>
                <option value="electronics">Electronics</option>
                <option value="clothing">Clothing</option>
                <option value="home">Home & Garden</option>
                <option value="books">Books</option>
                <option value="sports">Sports</option>
                <option value="automotive">Automotive</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Condition */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Condition
              </label>
              <select
                {...register('condition')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <option value="new">New</option>
                <option value="like_new">Like New</option>
                <option value="good">Good</option>
                <option value="fair">Fair</option>
                <option value="poor">Poor</option>
              </select>
            </div>

            {/* Shipping Cost */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shipping Cost
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  step="0.01"
                  {...register('shipping_cost')}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Profit Margin */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Desired Profit Margin (%)
              </label>
              <input
                type="number"
                step="1"
                {...register('profit_margin')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                placeholder="20"
              />
            </div>

            {/* Options */}
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('include_escrow_fee')}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Include escrow fee in calculation
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('competitor_analysis')}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Include competitor analysis
                </span>
              </label>
            </div>
          </form>
        </div>

        {/* Results */}
        <div className="space-y-6">
          {loading ? (
            <div className="bg-gray-50 rounded-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="space-y-3">
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ) : calculation ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-green-900 mb-4">
                Pricing Breakdown
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Base Price:</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(calculation.base_price)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Shipping Cost:</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(calculation.shipping_cost)}
                  </span>
                </div>
                
                {calculation.escrow_fee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-700">Escrow Fee (3%):</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(calculation.escrow_fee)}
                    </span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Profit Margin:</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(calculation.profit_amount)}
                  </span>
                </div>
                
                <div className="border-t border-green-300 pt-3">
                  <div className="flex justify-between">
                    <span className="text-base font-medium text-green-900">
                      Recommended Price:
                    </span>
                    <span className="text-lg font-bold text-green-900">
                      {formatCurrency(calculation.recommended_price)}
                    </span>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Your Net Profit:</span>
                  <span className="text-sm font-medium text-green-600">
                    {formatCurrency(calculation.net_profit)}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Enter Price Details
              </h3>
              <p className="text-gray-500">
                Fill in the form to calculate optimal pricing
              </p>
            </div>
          )}

          {/* Market Analysis */}
          {marketData && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-blue-900 mb-4">
                Market Analysis
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Average Market Price:</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(marketData.average_price)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Price Range:</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(marketData.min_price)} - {formatCurrency(marketData.max_price)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700">Your Position:</span>
                  <span className={`text-sm font-medium ${
                    marketData.position === 'competitive' ? 'text-green-600' :
                    marketData.position === 'high' ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {marketData.position_label}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {calculation?.recommendations && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Recommendations</h4>
              {calculation.recommendations.map((rec, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${getRecommendationColor(rec.type)}`}
                >
                  <div className="flex items-start space-x-2">
                    {getRecommendationIcon(rec.type)}
                    <div>
                      <p className="text-sm font-medium">{rec.title}</p>
                      <p className="text-xs mt-1">{rec.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default PriceCalculator;
