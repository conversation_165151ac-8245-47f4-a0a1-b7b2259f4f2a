import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  TrendingUpIcon,
  SparklesIcon,
  AdjustmentsHorizontalIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { searchAPI } from '../../services/apiServices';
import SearchFilters from './SearchFilters';
import SearchResults from './SearchResults';
import SearchSuggestions from './SearchSuggestions';
import logger from '../helpers/logger';

function SearchEngine({ 
  placeholder = "Search products, sellers, or transactions...",
  showFilters = true,
  showSuggestions = true,
  onResultSelect,
  className = '' 
}) {
  const { user } = useSelector((state) => state.auth);
  
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [trendingSearches, setTrendingSearches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    price_range: { min: '', max: '' },
    location: '',
    seller_rating: '',
    condition: '',
    sort_by: 'relevance',
    date_range: ''
  });

  const searchInputRef = useRef(null);
  const searchTimeoutRef = useRef(null);
  const resultsRef = useRef(null);

  useEffect(() => {
    fetchRecentSearches();
    fetchTrendingSearches();
  }, []);

  useEffect(() => {
    // Debounced search
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.trim().length > 0) {
      searchTimeoutRef.current = setTimeout(() => {
        performSearch();
        if (showSuggestions && query.length >= 2) {
          fetchSuggestions();
        }
      }, 300);
    } else {
      setResults([]);
      setSuggestions([]);
      setShowResults(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, filters]);

  const fetchRecentSearches = async () => {
    try {
      const response = await searchAPI.getRecentSearches();
      if (response.data.success) {
        setRecentSearches(response.data.searches);
      }
    } catch (error) {
      logger.error('Failed to fetch recent searches:', error);
    }
  };

  const fetchTrendingSearches = async () => {
    try {
      const response = await searchAPI.getTrendingSearches();
      if (response.data.success) {
        setTrendingSearches(response.data.searches);
      }
    } catch (error) {
      logger.error('Failed to fetch trending searches:', error);
    }
  };

  const fetchSuggestions = async () => {
    try {
      const response = await searchAPI.getSuggestions({ query: query.trim() });
      if (response.data.success) {
        setSuggestions(response.data.suggestions);
      }
    } catch (error) {
      logger.error('Failed to fetch suggestions:', error);
    }
  };

  const performSearch = async () => {
    if (!query.trim()) return;

    try {
      setLoading(true);
      
      const searchParams = {
        query: query.trim(),
        ...filters,
        user_id: user?.id
      };

      const response = await searchAPI.search(searchParams);
      
      if (response.data.success) {
        setResults(response.data.results);
        setShowResults(true);
        
        // Save search to recent searches
        await searchAPI.saveSearch({ query: query.trim(), filters });
        fetchRecentSearches();
      }
    } catch (error) {
      logger.error('Search failed:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleQueryChange = (e) => {
    setQuery(e.target.value);
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion.text);
    setSuggestions([]);
    searchInputRef.current?.focus();
  };

  const handleRecentSearchClick = (search) => {
    setQuery(search.query);
    setFilters(search.filters || {});
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleClearSearch = () => {
    setQuery('');
    setResults([]);
    setSuggestions([]);
    setShowResults(false);
    searchInputRef.current?.focus();
  };

  const handleResultSelect = (result) => {
    onResultSelect?.(result);
    setShowResults(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      performSearch();
    } else if (e.key === 'Escape') {
      setShowResults(false);
      setSuggestions([]);
    }
  };

  // Click outside to close results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (resultsRef.current && !resultsRef.current.contains(event.target)) {
        setShowResults(false);
        setSuggestions([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`} ref={resultsRef}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={searchInputRef}
          type="text"
          value={query}
          onChange={handleQueryChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (query.trim()) setShowResults(true);
            if (query.length >= 2) fetchSuggestions();
          }}
          placeholder={placeholder}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-3">
          {loading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
          
          {query && (
            <button
              onClick={handleClearSearch}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}
          
          {showFilters && (
            <button
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={`text-gray-400 hover:text-gray-600 ${
                showFiltersPanel ? 'text-blue-600' : ''
              }`}
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && showFiltersPanel && (
        <div className="absolute top-full left-0 right-0 mt-2 z-50">
          <SearchFilters
            filters={filters}
            onChange={handleFilterChange}
            onClose={() => setShowFiltersPanel(false)}
          />
        </div>
      )}

      {/* Search Suggestions */}
      {showSuggestions && suggestions.length > 0 && !showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 z-40">
          <SearchSuggestions
            suggestions={suggestions}
            onSelect={handleSuggestionClick}
          />
        </div>
      )}

      {/* Search Results */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 z-30">
          <SearchResults
            results={results}
            query={query}
            loading={loading}
            onSelect={handleResultSelect}
          />
        </div>
      )}

      {/* Recent & Trending Searches (when input is focused but empty) */}
      {!query && !showResults && (recentSearches.length > 0 || trendingSearches.length > 0) && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-20">
          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <ClockIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Recent Searches</span>
              </div>
              <div className="space-y-2">
                {recentSearches.slice(0, 5).map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleRecentSearchClick(search)}
                    className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                  >
                    {search.query}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Trending Searches */}
          {trendingSearches.length > 0 && (
            <div>
              <div className="flex items-center space-x-2 mb-3">
                <TrendingUpIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Trending</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {trendingSearches.slice(0, 8).map((search, index) => (
                  <button
                    key={index}
                    onClick={() => setQuery(search.query)}
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200"
                  >
                    <SparklesIcon className="h-3 w-3 mr-1" />
                    {search.query}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Search Stats */}
      {showResults && results.length > 0 && (
        <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
          <span>
            {results.length} result{results.length !== 1 ? 's' : ''} found
            {query && ` for "${query}"`}
          </span>
          
          <div className="flex items-center space-x-4">
            <select
              value={filters.sort_by}
              onChange={(e) => setFilters({ ...filters, sort_by: e.target.value })}
              className="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="relevance">Most Relevant</option>
              <option value="price_low">Price: Low to High</option>
              <option value="price_high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest First</option>
              <option value="popular">Most Popular</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchEngine;
