import React from 'react';
import {
  UsersIcon,
  UserPlusIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';

function UserGrowthChart({ data, className = '' }) {
  if (!data || !data.length) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 rounded-lg ${className}`}>
        <div className="text-center">
          <UsersIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No User Data</h4>
          <p className="text-sm text-gray-500">User growth data will appear here once available</p>
        </div>
      </div>
    );
  }

  // Calculate max values for scaling
  const maxTotal = Math.max(...data.map(item => item.total_users));
  const maxNew = Math.max(...data.map(item => item.new_users));
  const maxValue = Math.max(maxTotal, maxNew);
  const minValue = 0; // Start from 0 for user counts
  const range = maxValue - minValue;

  // Format number
  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate bar height percentage
  const getBarHeight = (value, maxVal) => {
    if (range === 0) return 10;
    return (value / maxVal) * 70 + 5; // 5-75% range
  };

  // Calculate trend for total users
  const getTotalUsersTrend = () => {
    if (data.length < 2) return { direction: 'neutral', percentage: 0 };
    
    const firstValue = data[0].total_users;
    const lastValue = data[data.length - 1].total_users;
    const change = firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100;
    
    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      percentage: Math.abs(change).toFixed(1)
    };
  };

  const trend = getTotalUsersTrend();

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-gray-900">
            {formatNumber(data[data.length - 1]?.total_users || 0)}
          </span>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend.direction === 'up' 
              ? 'bg-green-100 text-green-800' 
              : trend.direction === 'down'
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            <TrendingUpIcon className={`h-3 w-3 ${
              trend.direction === 'down' ? 'rotate-180' : ''
            }`} />
            <span>{trend.percentage}%</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span className="text-gray-600">Total Users</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-gray-600">New Users</span>
          </div>
        </div>
      </div>

      {/* Chart Area */}
      <div className="flex-1 flex items-end space-x-1 px-2 relative">
        {data.map((item, index) => (
          <div key={index} className="flex-1 flex flex-col items-center group">
            {/* Bars Container */}
            <div className="w-full flex justify-center space-x-1">
              {/* Total Users Bar */}
              <div className="flex flex-col items-center flex-1">
                <div
                  className="w-full bg-blue-500 rounded-t-sm hover:bg-blue-600 transition-colors duration-200 relative"
                  style={{ height: `${getBarHeight(item.total_users, maxTotal)}%` }}
                >
                  {/* Tooltip for Total Users */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    Total: {formatNumber(item.total_users)}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
              
              {/* New Users Bar */}
              <div className="flex flex-col items-center flex-1">
                <div
                  className="w-full bg-green-500 rounded-t-sm hover:bg-green-600 transition-colors duration-200 relative"
                  style={{ height: `${getBarHeight(item.new_users, maxNew)}%` }}
                >
                  {/* Tooltip for New Users */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    New: {formatNumber(item.new_users)}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Label */}
            <div className="mt-2 text-xs text-gray-500 text-center">
              {formatDate(item.date)}
            </div>
          </div>
        ))}
        
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-8 -ml-12">
          <span className="text-xs text-gray-500">{formatNumber(maxValue)}</span>
          <span className="text-xs text-gray-500">{formatNumber(Math.round(maxValue * 0.75))}</span>
          <span className="text-xs text-gray-500">{formatNumber(Math.round(maxValue * 0.5))}</span>
          <span className="text-xs text-gray-500">{formatNumber(Math.round(maxValue * 0.25))}</span>
          <span className="text-xs text-gray-500">0</span>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-6">
          {/* Total Users Stats */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <UsersIcon className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium text-gray-900">Total Users</span>
            </div>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-xs text-gray-500">Current</p>
                <p className="text-sm font-medium text-gray-900">
                  {formatNumber(data[data.length - 1]?.total_users || 0)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Growth</p>
                <p className={`text-sm font-medium ${
                  trend.direction === 'up' ? 'text-green-600' : 
                  trend.direction === 'down' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {trend.direction === 'up' ? '+' : trend.direction === 'down' ? '-' : ''}{trend.percentage}%
                </p>
              </div>
            </div>
          </div>

          {/* New Users Stats */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <UserPlusIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium text-gray-900">New Users</span>
            </div>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-xs text-gray-500">This Period</p>
                <p className="text-sm font-medium text-gray-900">
                  {formatNumber(data[data.length - 1]?.new_users || 0)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Total New</p>
                <p className="text-sm font-medium text-gray-900">
                  {formatNumber(data.reduce((sum, item) => sum + item.new_users, 0))}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserGrowthChart;
