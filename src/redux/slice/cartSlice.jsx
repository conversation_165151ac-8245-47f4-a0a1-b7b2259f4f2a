import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';

const loadCartFromStorage = () => {
  try {
    const cart = localStorage.getItem('cart');
    return cart ? JSON.parse(cart) : [];
  } catch (error) {
    return [];
  }
};

const saveCartToStorage = (cart) => {
  localStorage.setItem('cart', JSON.stringify(cart));
};

// Helper function to calculate totals
const calculateCartTotals = (items) => ({
  total: items.reduce((total, item) => total + (item.price * item.quantity), 0),
  totalItems: items.reduce((total, item) => total + item.quantity, 0),
});

const initialState = {
  items: loadCartFromStorage(),
  total: 0,
  totalItems: 0,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action) => {
      const { product, quantity = 1 } = action.payload;
      const existingItem = state.items.find((item) => item.product.id === product.id);

      let newItems;
      if (existingItem) {
        newItems = state.items.map((item) => (item.product.id === product.id
          ? { ...item, quantity: item.quantity + quantity }
          : item));
        toast.success(`Increased ${product.name} quantity`);
      } else {
        newItems = [
          ...state.items,
          {
            id: Date.now(),
            product,
            quantity,
            price: product.price,
          },
        ];
        toast.success(`Added ${product.name} to cart`);
      }

      const totals = calculateCartTotals(newItems);
      saveCartToStorage(newItems);

      return {
        ...state,
        items: newItems,
        ...totals,
      };
    },

    removeFromCart: (state, action) => {
      const itemId = action.payload;
      const item = state.items.find((item) => item.id === itemId);

      if (item) {
        const newItems = state.items.filter((item) => item.id !== itemId);
        const totals = calculateCartTotals(newItems);

        toast.success(`Removed ${item.product.name} from cart`);
        saveCartToStorage(newItems);

        return {
          ...state,
          items: newItems,
          ...totals,
        };
      }

      return state;
    },

    updateQuantity: (state, action) => {
      const { itemId, quantity } = action.payload;
      const item = state.items.find((item) => item.id === itemId);

      if (item && quantity > 0) {
        const newItems = state.items.map((item) => (item.id === itemId
          ? { ...item, quantity }
          : item));

        const totals = calculateCartTotals(newItems);
        saveCartToStorage(state.items);

        return {
          ...state,
          items: newItems,
          ...totals,
        };
      }

      return state;
    },

    clearCart: (state) => {
      saveCartToStorage([]);
      return {
        ...state,
        items: [],
        total: 0,
        totalItems: 0,
      };
    },

    calculateTotals: (state) => {
      const totals = calculateCartTotals(state.items);
      return {
        ...state,
        ...totals,
      };
    },
  },
});

export const {
  addToCart, removeFromCart, updateQuantity, clearCart, calculateTotals,
} = cartSlice.actions;

export default cartSlice.reducer;
