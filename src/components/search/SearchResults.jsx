import React from 'react';
import {
  MagnifyingGlassIcon,
  StarIcon,
  MapPinIcon,
  TagIcon,
  UserIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

function SearchResults({ results, query, loading, onSelect, className = '' }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getResultIcon = (type) => {
    switch (type) {
      case 'product':
        return <ShoppingCartIcon className="h-5 w-5 text-blue-600" />;
      case 'seller':
        return <UserIcon className="h-5 w-5 text-green-600" />;
      case 'transaction':
        return <CreditCardIcon className="h-5 w-5 text-purple-600" />;
      default:
        return <MagnifyingGlassIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getResultTypeLabel = (type) => {
    switch (type) {
      case 'product':
        return 'Product';
      case 'seller':
        return 'Seller';
      case 'transaction':
        return 'Transaction';
      default:
        return 'Result';
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-4 w-4 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarIconSolid className="h-4 w-4 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return stars;
  };

  const highlightText = (text, query) => {
    if (!query || !text) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  const renderProductResult = (result) => (
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0">
        {result.image ? (
          <img
            src={result.image}
            alt={result.title}
            className="h-16 w-16 object-cover rounded-lg border border-gray-200"
          />
        ) : (
          <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <ShoppingCartIcon className="h-8 w-8 text-gray-400" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {highlightText(result.title, query)}
            </h3>
            <p className="text-sm text-gray-500 mt-1 line-clamp-2">
              {highlightText(result.description, query)}
            </p>
          </div>
          <div className="text-right ml-4">
            <p className="text-lg font-semibold text-gray-900">
              {formatCurrency(result.price)}
            </p>
            {result.original_price && result.original_price > result.price && (
              <p className="text-sm text-gray-500 line-through">
                {formatCurrency(result.original_price)}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-4 mt-2">
          <div className="flex items-center space-x-1">
            {renderStars(result.rating || 0)}
            <span className="text-xs text-gray-500">
              ({result.review_count || 0})
            </span>
          </div>
          
          {result.location && (
            <div className="flex items-center space-x-1">
              <MapPinIcon className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-500">{result.location}</span>
            </div>
          )}
          
          {result.condition && (
            <div className="flex items-center space-x-1">
              <TagIcon className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-500">{result.condition}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between mt-2">
          <span className="text-xs text-gray-500">
            by {result.seller_name}
          </span>
          <span className="text-xs text-gray-500">
            Listed {formatDate(result.created_at)}
          </span>
        </div>
      </div>
    </div>
  );

  const renderSellerResult = (result) => (
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0">
        {result.avatar ? (
          <img
            src={result.avatar}
            alt={result.name}
            className="h-12 w-12 object-cover rounded-full border border-gray-200"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
            <UserIcon className="h-6 w-6 text-gray-400" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900">
              {highlightText(result.name, query)}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {result.business_type} • {result.total_products} products
            </p>
          </div>
          
          <div className="flex items-center space-x-1">
            {renderStars(result.rating || 0)}
            <span className="text-xs text-gray-500 ml-1">
              ({result.review_count || 0})
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 mt-2">
          {result.location && (
            <div className="flex items-center space-x-1">
              <MapPinIcon className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-500">{result.location}</span>
            </div>
          )}
          
          <span className="text-xs text-gray-500">
            {result.total_sales} sales
          </span>
          
          <span className="text-xs text-gray-500">
            Joined {formatDate(result.joined_at)}
          </span>
        </div>
      </div>
    </div>
  );

  const renderTransactionResult = (result) => (
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0">
        <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
          <CreditCardIcon className="h-6 w-6 text-purple-600" />
        </div>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900">
              Transaction #{result.id}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {highlightText(result.description, query)}
            </p>
          </div>
          
          <div className="text-right">
            <p className="text-sm font-semibold text-gray-900">
              {formatCurrency(result.amount)}
            </p>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              result.status === 'completed' ? 'bg-green-100 text-green-800' :
              result.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              result.status === 'disputed' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {result.status}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 mt-2">
          <span className="text-xs text-gray-500">
            {result.buyer_name} ↔ {result.seller_name}
          </span>
          
          <div className="flex items-center space-x-1">
            <ClockIcon className="h-3 w-3 text-gray-400" />
            <span className="text-xs text-gray-500">
              {formatDate(result.created_at)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderResult = (result) => {
    switch (result.type) {
      case 'product':
        return renderProductResult(result);
      case 'seller':
        return renderSellerResult(result);
      case 'transaction':
        return renderTransactionResult(result);
      default:
        return renderProductResult(result);
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-gray-200 p-4 ${className}`}>
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="flex items-start space-x-4">
                <div className="h-16 w-16 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-gray-200 p-8 text-center ${className}`}>
        <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
        <p className="text-gray-500">
          {query ? `No results found for "${query}"` : 'Try adjusting your search terms or filters'}
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto ${className}`}>
      <div className="p-4">
        <div className="space-y-4">
          {results.map((result, index) => (
            <div
              key={`${result.type}-${result.id}-${index}`}
              onClick={() => onSelect(result)}
              className="p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200 border border-transparent hover:border-gray-200"
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getResultIcon(result.type)}
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {getResultTypeLabel(result.type)}
                  </span>
                </div>
                
                {result.featured && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Featured
                  </span>
                )}
              </div>
              
              {renderResult(result)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default SearchResults;
