import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  TrashIcon,
  HeartIcon,
  MinusIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

function CartItem({ 
  item, 
  onUpdateQuantity, 
  onRemoveItem, 
  onSaveForLater, 
  isUpdating = false,
  showShipping = true,
  showEscrowFee = true 
}) {
  const [quantity, setQuantity] = useState(item.quantity);
  const [isWishlisted, setIsWishlisted] = useState(item.is_wishlisted || false);

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity < 1) {
      toast.error('Quantity must be at least 1');
      return;
    }
    
    if (newQuantity > item.product.stock_quantity) {
      toast.error(`Only ${item.product.stock_quantity} items available in stock`);
      return;
    }
    
    setQuantity(newQuantity);
    onUpdateQuantity(item.id, newQuantity);
  };

  const handleRemove = () => {
    onRemoveItem(item.id);
    toast.success('Item removed from cart');
  };

  const handleSaveForLater = () => {
    onSaveForLater(item.id);
    toast.success('Item saved for later');
  };

  const toggleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    // TODO: Implement wishlist API call
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  // Calculate prices
  const basePrice = item.product.price * quantity;
  const shippingCost = item.product.shipping_cost || 0;
  const escrowFee = basePrice * 0.05; // 5% escrow fee
  const totalPrice = basePrice + shippingCost + escrowFee;

  // Check stock availability
  const isOutOfStock = item.product.stock_quantity === 0;
  const isLowStock = item.product.stock_quantity > 0 && item.product.stock_quantity <= 5;
  const isQuantityExceeded = quantity > item.product.stock_quantity;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${
      isOutOfStock || isQuantityExceeded ? 'opacity-75' : ''
    }`}>
      <div className="flex items-start space-x-4">
        {/* Product Image */}
        <div className="flex-shrink-0">
          <Link to={`/products/${item.product.id}`}>
            <img
              src={item.product.primary_image?.url || '/placeholder-product.jpg'}
              alt={item.product.name}
              className="h-20 w-20 object-cover rounded-md border border-gray-200 hover:opacity-75 transition-opacity"
            />
          </Link>
        </div>

        {/* Product Details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <Link
                to={`/products/${item.product.id}`}
                className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
              >
                {item.product.name}
              </Link>
              
              <div className="mt-1 flex items-center space-x-2 text-xs text-gray-500">
                <span>By {item.product.seller?.store_name || item.product.seller?.username}</span>
                {item.product.condition && item.product.condition !== 'new' && (
                  <>
                    <span>•</span>
                    <span className="capitalize">{item.product.condition}</span>
                  </>
                )}
              </div>

              {/* Stock Status */}
              <div className="mt-2">
                {isOutOfStock ? (
                  <div className="flex items-center text-red-600">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Out of Stock</span>
                  </div>
                ) : isLowStock ? (
                  <div className="flex items-center text-yellow-600">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">
                      Only {item.product.stock_quantity} left in stock
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center text-green-600">
                    <span className="text-xs">In Stock</span>
                  </div>
                )}
              </div>

              {/* Shipping & Escrow Info */}
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                {showShipping && (
                  <div className="flex items-center">
                    <TruckIcon className="h-3 w-3 mr-1" />
                    <span>
                      {shippingCost > 0 ? `Shipping: GH₵ ${shippingCost.toFixed(2)}` : 'Free Shipping'}
                    </span>
                  </div>
                )}
                
                {showEscrowFee && (
                  <div className="flex items-center">
                    <ShieldCheckIcon className="h-3 w-3 mr-1" />
                    <span>Escrow Protected</span>
                  </div>
                )}
              </div>
            </div>

            {/* Price */}
            <div className="text-right ml-4">
              <div className="text-lg font-semibold text-gray-900">
                GH₵ {basePrice.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">
                GH₵ {item.product.price.toFixed(2)} each
              </div>
              
              {showEscrowFee && escrowFee > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  + GH₵ {escrowFee.toFixed(2)} escrow fee
                </div>
              )}
              
              {showShipping && shippingCost > 0 && (
                <div className="text-xs text-gray-500">
                  + GH₵ {shippingCost.toFixed(2)} shipping
                </div>
              )}
              
              <div className="text-sm font-medium text-blue-600 mt-1 border-t border-gray-200 pt-1">
                Total: GH₵ {totalPrice.toFixed(2)}
              </div>
            </div>
          </div>

          {/* Quantity and Actions */}
          <div className="mt-4 flex items-center justify-between">
            {/* Quantity Controls */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 1 || isUpdating || isOutOfStock}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <MinusIcon className="h-4 w-4" />
                </button>
                
                <input
                  type="number"
                  min="1"
                  max={item.product.stock_quantity}
                  value={quantity}
                  onChange={(e) => {
                    const newQuantity = parseInt(e.target.value) || 1;
                    handleQuantityChange(newQuantity);
                  }}
                  disabled={isUpdating || isOutOfStock}
                  className="w-16 px-2 py-1 text-center text-sm border-0 focus:ring-0 disabled:bg-gray-50"
                />
                
                <button
                  onClick={() => handleQuantityChange(quantity + 1)}
                  disabled={quantity >= item.product.stock_quantity || isUpdating || isOutOfStock}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlusIcon className="h-4 w-4" />
                </button>
              </div>
              
              <span className="text-xs text-gray-500">
                Qty: {quantity}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              {/* Wishlist Button */}
              <button
                onClick={toggleWishlist}
                className={`p-2 rounded-md transition-colors ${
                  isWishlisted 
                    ? 'text-red-500 hover:text-red-600 bg-red-50' 
                    : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
                }`}
                title={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
              >
                {isWishlisted ? (
                  <HeartIconSolid className="h-4 w-4" />
                ) : (
                  <HeartIcon className="h-4 w-4" />
                )}
              </button>

              {/* Save for Later */}
              <button
                onClick={handleSaveForLater}
                disabled={isUpdating}
                className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save for later
              </button>

              {/* Remove Button */}
              <button
                onClick={handleRemove}
                disabled={isUpdating}
                className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Remove from cart"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Error Messages */}
          {isQuantityExceeded && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center text-red-800">
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                <span className="text-xs">
                  Quantity exceeds available stock ({item.product.stock_quantity} available)
                </span>
              </div>
            </div>
          )}

          {/* Product Unavailable */}
          {isOutOfStock && (
            <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center text-gray-600">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                  <span className="text-xs">This item is currently unavailable</span>
                </div>
                <button
                  onClick={handleRemove}
                  className="text-xs text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
          )}

          {/* Estimated Delivery */}
          {!isOutOfStock && (
            <div className="mt-2 text-xs text-gray-500">
              <TruckIcon className="h-3 w-3 inline mr-1" />
              Estimated delivery: 3-7 business days
            </div>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {isUpdating && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span>Updating...</span>
          </div>
        </div>
      )}
    </div>
  );
}

export default CartItem;
