export const formatCurrency = (amount, currency = 'GHS') => new Intl.NumberFormat('en-GH', {
  style: 'currency',
  currency,
  minimumFractionDigits: 2,
}).format(amount);

// Date formatter
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  };

  return new Date(date).toLocaleDateString('en-GH', defaultOptions);
};

// Relative time formatter
export const formatRelativeTime = (date) => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInMs = now - targetDate;
  const diffInHours = diffInMs / (1000 * 60 * 60);
  const diffInDays = diffInHours / 24;

  if (diffInHours < 1) {
    return 'Less than an hour ago';
  } if (diffInHours < 24) {
    return `${Math.floor(diffInHours)} hours ago`;
  } if (diffInDays < 7) {
    return `${Math.floor(diffInDays)} days ago`;
  }
  return formatDate(date);
};

// File size formatter
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// Phone number formatter for Ghana
export const formatPhoneNumber = (phone) => {
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '');

  // Check if it starts with Ghana country code
  if (cleaned.startsWith('233')) {
    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;
  }

  // Assume it's a local number
  if (cleaned.length === 10) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }

  return phone; // Return original if can't format
};

// Order status formatter
export const getOrderStatusColor = (status) => {
  const statusColors = {
    pending_payment: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-blue-100 text-blue-800',
    processing: 'bg-orange-100 text-orange-800',
    shipped: 'bg-purple-100 text-purple-800',
    delivered: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    disputed: 'bg-gray-100 text-gray-800',
  };

  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

// String truncation
export const truncateString = (str, maxLength = 100) => {
  if (str.length <= maxLength) return str;
  return `${str.slice(0, maxLength)}...`;
};
