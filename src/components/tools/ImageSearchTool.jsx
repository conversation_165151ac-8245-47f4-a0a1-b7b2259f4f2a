import React, { useState, useRef } from 'react';
import {
  CameraIcon,
  PhotoIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  ArrowUpTrayIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { searchAPI } from '../../services/apiServices';
import SearchResults from '../search/SearchResults';
import logger from '../helpers/logger';

function ImageSearchTool({ className = '' }) {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  
  const fileInputRef = useRef(null);
  const dropRef = useRef(null);

  const handleImageSelect = (file) => {
    if (!file) return;
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image size must be less than 10MB');
      return;
    }
    
    setSelectedImage(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    handleImageSelect(file);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleImageSelect(files[0]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleSearchByImage = async () => {
    if (!selectedImage) {
      toast.error('Please select an image first');
      return;
    }
    
    try {
      setLoading(true);
      
      const formData = new FormData();
      formData.append('image', selectedImage);
      formData.append('limit', '20');
      
      const response = await searchAPI.searchByImage(formData);
      
      if (response.data.success) {
        setSearchResults(response.data.results);
        if (response.data.results.length === 0) {
          toast.info('No similar products found');
        } else {
          toast.success(`Found ${response.data.results.length} similar products`);
        }
      }
    } catch (error) {
      logger.error('Image search failed:', error);
      toast.error('Image search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClearImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setSearchResults([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleResultSelect = (result) => {
    // Handle result selection (navigate to product page, etc.)
    toast.success(`Selected: ${result.title}`);
    // TODO: Navigate to product page or handle result selection
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <CameraIcon className="h-6 w-6 text-blue-600" />
        <div>
          <h2 className="text-xl font-bold text-gray-900">Image Search</h2>
          <p className="text-gray-600 mt-1">
            Upload an image to find similar products
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Section */}
        <div className="space-y-6">
          {/* Image Upload Area */}
          <div
            ref={dropRef}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200 ${
              dragActive
                ? 'border-blue-500 bg-blue-50'
                : selectedImage
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Selected"
                  className="max-h-64 mx-auto rounded-lg shadow-sm"
                />
                <button
                  onClick={handleClearImage}
                  className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <div>
                <PhotoIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-900">
                    Drop an image here, or click to select
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports JPG, PNG, GIF up to 10MB
                  </p>
                </div>
              </div>
            )}
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </div>

          {/* Upload Button */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
              Choose Image
            </button>
            
            <button
              onClick={handleSearchByImage}
              disabled={!selectedImage || loading}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                  Search
                </>
              )}
            </button>
          </div>

          {/* Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <SparklesIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900">Tips for better results</h4>
                <ul className="text-sm text-blue-800 mt-2 space-y-1">
                  <li>• Use clear, well-lit images</li>
                  <li>• Focus on the main product</li>
                  <li>• Avoid cluttered backgrounds</li>
                  <li>• Higher resolution images work better</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Search Results</h3>
            {searchResults.length > 0 && (
              <span className="text-sm text-gray-500">
                {searchResults.length} results found
              </span>
            )}
          </div>

          {searchResults.length > 0 ? (
            <div className="max-h-96 overflow-y-auto">
              <SearchResults
                results={searchResults}
                loading={loading}
                onSelect={handleResultSelect}
              />
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">No Results Yet</h4>
              <p className="text-gray-500">
                Upload an image and click search to find similar products
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Recent Image Searches */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Recent Image Searches</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          {/* Placeholder for recent searches */}
          {[...Array(6)].map((_, index) => (
            <div
              key={index}
              className="aspect-square bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors duration-200"
            >
              <PhotoIcon className="h-6 w-6 text-gray-400" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default ImageSearchTool;
