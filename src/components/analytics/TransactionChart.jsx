import React from 'react';
import {
  ShoppingCartIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';

function TransactionChart({ data, className = '' }) {
  if (!data || !data.length) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 rounded-lg ${className}`}>
        <div className="text-center">
          <ShoppingCartIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No Transaction Data</h4>
          <p className="text-sm text-gray-500">Transaction data will appear here once available</p>
        </div>
      </div>
    );
  }

  // Calculate max value for scaling
  const maxValue = Math.max(...data.map(item => item.count));
  const minValue = Math.min(...data.map(item => item.count));
  const range = maxValue - minValue;

  // Format number
  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate line points for SVG path
  const getLinePoints = () => {
    const width = 100; // Percentage width
    const height = 80; // Percentage height
    const stepX = width / (data.length - 1);
    
    return data.map((item, index) => {
      const x = index * stepX;
      const y = range === 0 ? 50 : (1 - (item.count - minValue) / range) * height + 10;
      return `${x},${y}`;
    }).join(' ');
  };

  // Calculate trend
  const getTrend = () => {
    if (data.length < 2) return { direction: 'neutral', percentage: 0 };
    
    const firstValue = data[0].count;
    const lastValue = data[data.length - 1].count;
    const change = firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100;
    
    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      percentage: Math.abs(change).toFixed(1)
    };
  };

  const trend = getTrend();
  const linePoints = getLinePoints();

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-gray-900">
            {formatNumber(data[data.length - 1]?.count || 0)}
          </span>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend.direction === 'up' 
              ? 'bg-green-100 text-green-800' 
              : trend.direction === 'down'
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            <TrendingUpIcon className={`h-3 w-3 ${
              trend.direction === 'down' ? 'rotate-180' : ''
            }`} />
            <span>{trend.percentage}%</span>
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          Transactions
        </div>
      </div>

      {/* Chart Area */}
      <div className="flex-1 relative">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
          
          {/* Area under the line */}
          <path
            d={`M 0,90 L ${linePoints} L 100,90 Z`}
            fill="url(#gradient)"
            opacity="0.3"
          />
          
          {/* Gradient definition */}
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1"/>
            </linearGradient>
          </defs>
          
          {/* Line */}
          <polyline
            points={linePoints}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = range === 0 ? 50 : (1 - (item.count - minValue) / range) * 80 + 10;
            
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r="3"
                  fill="#3b82f6"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-4 transition-all duration-200"
                />
                
                {/* Tooltip trigger area */}
                <circle
                  cx={x}
                  cy={y}
                  r="8"
                  fill="transparent"
                  className="hover:fill-blue-100 hover:fill-opacity-50"
                >
                  <title>{`${formatDate(item.date)}: ${formatNumber(item.count)} transactions`}</title>
                </circle>
              </g>
            );
          })}
        </svg>
        
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-2 -ml-12">
          <span className="text-xs text-gray-500">{formatNumber(maxValue)}</span>
          <span className="text-xs text-gray-500">{formatNumber(Math.round((maxValue + minValue) / 2))}</span>
          <span className="text-xs text-gray-500">{formatNumber(minValue)}</span>
        </div>
      </div>

      {/* X-axis labels */}
      <div className="flex justify-between mt-2 px-2">
        {data.map((item, index) => {
          // Show only first, middle, and last labels to avoid crowding
          if (index === 0 || index === Math.floor(data.length / 2) || index === data.length - 1) {
            return (
              <span key={index} className="text-xs text-gray-500">
                {formatDate(item.date)}
              </span>
            );
          }
          return <span key={index}></span>;
        })}
      </div>

      {/* Summary Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-xs text-gray-500">Total</p>
            <p className="text-sm font-medium text-gray-900">
              {formatNumber(data.reduce((sum, item) => sum + item.count, 0))}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Daily Avg</p>
            <p className="text-sm font-medium text-gray-900">
              {formatNumber(Math.round(data.reduce((sum, item) => sum + item.count, 0) / data.length))}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Peak Day</p>
            <p className="text-sm font-medium text-gray-900">
              {formatNumber(maxValue)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionChart;
