import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  MagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  EllipsisVerticalIcon,
  ArchiveBoxIcon,
  TrashIcon,
  CheckIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { ChatBubbleLeftRightIcon as ChatBubbleLeftRightIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';
import { chatAPI } from '../../services/apiServices';
import { useSocket } from '../../hooks/useSocket';
import logger from '../helpers/logger';

function ConversationList({ 
  onConversationSelect, 
  selectedConversationId,
  className = '' 
}) {
  const { user } = useSelector((state) => state.auth);
  const socket = useSocket();
  
  const [conversations, setConversations] = useState([]);
  const [filteredConversations, setFilteredConversations] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'archived'
  const [showActions, setShowActions] = useState(null);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (socket) {
      socket.on('conversation_updated', handleConversationUpdated);
      socket.on('new_message', handleNewMessage);
      socket.on('message_read', handleMessageRead);

      return () => {
        socket.off('conversation_updated', handleConversationUpdated);
        socket.off('new_message', handleNewMessage);
        socket.off('message_read', handleMessageRead);
      };
    }
  }, [socket]);

  useEffect(() => {
    filterConversations();
  }, [conversations, searchQuery, filter]);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getConversations();
      
      if (response.data.success) {
        setConversations(response.data.conversations);
      }
    } catch (error) {
      logger.error('Failed to fetch conversations:', error);
      toast.error('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const handleConversationUpdated = (data) => {
    setConversations(prev => prev.map(conv => 
      conv.id === data.conversation.id ? data.conversation : conv
    ));
  };

  const handleNewMessage = (data) => {
    setConversations(prev => {
      const updated = prev.map(conv => {
        if (conv.id === data.conversation_id) {
          return {
            ...conv,
            last_message: data.message,
            updated_at: data.message.created_at,
            unread_count: conv.id === selectedConversationId ? 0 : (conv.unread_count || 0) + 1
          };
        }
        return conv;
      });
      
      // Sort by updated_at
      return updated.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
    });
  };

  const handleMessageRead = (data) => {
    setConversations(prev => prev.map(conv => 
      conv.id === data.conversation_id 
        ? { ...conv, unread_count: 0 }
        : conv
    ));
  };

  const filterConversations = () => {
    let filtered = conversations;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(conv => {
        const otherUser = conv.participants?.find(p => p.id !== user.id);
        return otherUser?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
               conv.last_message?.content?.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    // Apply status filter
    switch (filter) {
      case 'unread':
        filtered = filtered.filter(conv => conv.unread_count > 0);
        break;
      case 'archived':
        filtered = filtered.filter(conv => conv.is_archived);
        break;
      default:
        filtered = filtered.filter(conv => !conv.is_archived);
    }

    setFilteredConversations(filtered);
  };

  const handleArchiveConversation = async (conversationId) => {
    try {
      await chatAPI.archiveConversation(conversationId);
      setConversations(prev => prev.map(conv => 
        conv.id === conversationId ? { ...conv, is_archived: true } : conv
      ));
      toast.success('Conversation archived');
    } catch (error) {
      logger.error('Failed to archive conversation:', error);
      toast.error('Failed to archive conversation');
    }
  };

  const handleDeleteConversation = async (conversationId) => {
    if (!window.confirm('Are you sure you want to delete this conversation?')) {
      return;
    }

    try {
      await chatAPI.deleteConversation(conversationId);
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      toast.success('Conversation deleted');
    } catch (error) {
      logger.error('Failed to delete conversation:', error);
      toast.error('Failed to delete conversation');
    }
  };

  const handleMarkAsRead = async (conversationId) => {
    try {
      await chatAPI.markAsRead(conversationId);
      setConversations(prev => prev.map(conv => 
        conv.id === conversationId ? { ...conv, unread_count: 0 } : conv
      ));
    } catch (error) {
      logger.error('Failed to mark as read:', error);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const getOtherUser = (conversation) => {
    return conversation.participants?.find(p => p.id !== user.id);
  };

  const truncateMessage = (message, maxLength = 50) => {
    if (!message) return '';
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
        <div className="p-4">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ChatBubbleLeftRightIconSolid className="h-5 w-5 mr-2 text-blue-600" />
            Messages
          </h2>
          
          <div className="flex items-center space-x-2">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="unread">Unread</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search conversations..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-8 text-center">
            <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              {searchQuery ? 'No conversations found' : 'No conversations yet'}
            </h3>
            <p className="text-sm text-gray-500">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Start a conversation with a seller or buyer'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredConversations.map((conversation) => {
              const otherUser = getOtherUser(conversation);
              const isSelected = conversation.id === selectedConversationId;
              const hasUnread = conversation.unread_count > 0;

              return (
                <div
                  key={conversation.id}
                  className={`relative p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    isSelected ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                  }`}
                  onClick={() => onConversationSelect(conversation)}
                >
                  <div className="flex items-start space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      <img
                        src={otherUser?.avatar || '/default-avatar.png'}
                        alt={otherUser?.name}
                        className="h-10 w-10 rounded-full object-cover"
                      />
                      {otherUser?.is_online && (
                        <div className="absolute bottom-0 right-0 h-3 w-3 bg-green-400 border-2 border-white rounded-full"></div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className={`text-sm font-medium truncate ${
                          hasUnread ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {otherUser?.name || 'Unknown User'}
                        </h3>
                        
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {formatTime(conversation.updated_at)}
                          </span>
                          
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowActions(showActions === conversation.id ? null : conversation.id);
                            }}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <EllipsisVerticalIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-1">
                        <p className={`text-sm truncate ${
                          hasUnread ? 'font-medium text-gray-900' : 'text-gray-500'
                        }`}>
                          {conversation.last_message?.content 
                            ? truncateMessage(conversation.last_message.content)
                            : 'No messages yet'
                          }
                        </p>
                        
                        {hasUnread && (
                          <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                            {conversation.unread_count}
                          </span>
                        )}
                      </div>

                      {/* Transaction Context */}
                      {conversation.transaction && (
                        <div className="mt-2 flex items-center space-x-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Transaction #{conversation.transaction.id}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions Dropdown */}
                  {showActions === conversation.id && (
                    <div className="absolute right-4 top-12 z-10 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1">
                      {hasUnread && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsRead(conversation.id);
                            setShowActions(null);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <CheckIcon className="h-4 w-4 mr-2" />
                          Mark as read
                        </button>
                      )}
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleArchiveConversation(conversation.id);
                          setShowActions(null);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <ArchiveBoxIcon className="h-4 w-4 mr-2" />
                        Archive
                      </button>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteConversation(conversation.id);
                          setShowActions(null);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Delete
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default ConversationList;
