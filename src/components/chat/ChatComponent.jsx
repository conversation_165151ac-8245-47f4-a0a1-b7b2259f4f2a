import { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { PaperAirplaneIcon, AttachmentIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import { chatAPI } from '../../services/apiServices';
import webSocketService from '../../services/websocket';

function ChatComponent({ conversationId }) {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef(null);
  const { user } = useSelector((state) => state.auth);

  useEffect(() => () => {
    webSocketService.leaveConversation(conversationId);
    webSocketService.offEvent('new_message');
  }, [conversationId]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getMessages(conversationId);
      if (response.data.success) {
        setMessages(response.data.messages);
      }
    } catch (error) {
      toast.error('Failed to load messages' || error.message);
    } finally {
      setLoading(false);
    }
  };

  if (conversationId) {
    fetchMessages();
    webSocketService.joinConversation(conversationId);

    webSocketService.onNewMessage((message) => {
      setMessages((prev) => [...prev, message]);
    });
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || sending) return;

    try {
      setSending(true);
      const messageData = {
        conversation_id: conversationId,
        content: newMessage,
        message_type: 'text',
      };

      await chatAPI.sendMessage(messageData);
      setNewMessage('');
    } catch (error) {
      toast.error('Failed to load messages' || error.message);
    } finally {
      setSending(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" />
      </div>
    );
  }

  return (
    <div>
      {/* Messages */}
      <div>
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.sender.id === user.id ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender.id === user.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-200 text-gray-900'
              }`}
            >
              {message.sender.id !== user.id && (
                <p>
                  {message.sender.name}
                </p>
              )}
              <p>{message.content}</p>
              <p
                className={`text-xs mt-1 ${
                  message.sender.id === user.id
                    ? 'text-primary-200'
                    : 'text-gray-500'
                }`}
              >
                {new Date(message.created_at).toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <form onSubmit={sendMessage}>
        <div>
          <button
            type="button"
          >
            <AttachmentIcon className="h-5 w-5" />
          </button>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
      </form>
    </div>
  );
}

ChatComponent.propTypes = {
  conversationId: PropTypes.string.isRequired,
};

export default ChatComponent;
