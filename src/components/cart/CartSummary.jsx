import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  ShoppingBagIcon,
  TruckIcon,
  ShieldCheckIcon,
  TagIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  GiftIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';

function CartSummary({ 
  items = [], 
  onApplyCoupon, 
  onProceedToCheckout,
  appliedCoupon = null,
  isLoading = false,
  showPromoCode = true,
  showEscrowInfo = true 
}) {
  const navigate = useNavigate();
  const [promoCode, setPromoCode] = useState('');
  const [applyingCoupon, setApplyingCoupon] = useState(false);

  // Calculate totals
  const subtotal = items.reduce((sum, item) => {
    return sum + (item.product.price * item.quantity);
  }, 0);

  const totalShipping = items.reduce((sum, item) => {
    return sum + (item.product.shipping_cost || 0);
  }, 0);

  const escrowFee = subtotal * 0.05; // 5% escrow fee
  
  const couponDiscount = appliedCoupon ? (subtotal * appliedCoupon.discount_percentage / 100) : 0;
  const discountedSubtotal = subtotal - couponDiscount;
  
  const finalTotal = discountedSubtotal + totalShipping + escrowFee;

  // Check for issues
  const outOfStockItems = items.filter(item => item.product.stock_quantity === 0);
  const hasIssues = outOfStockItems.length > 0;

  const handleApplyCoupon = async () => {
    if (!promoCode.trim()) {
      toast.error('Please enter a promo code');
      return;
    }

    setApplyingCoupon(true);
    try {
      await onApplyCoupon(promoCode.trim());
      setPromoCode('');
    } catch (error) {
      toast.error(error.message || 'Invalid promo code');
    } finally {
      setApplyingCoupon(false);
    }
  };

  const handleProceedToCheckout = () => {
    if (hasIssues) {
      toast.error('Please resolve cart issues before proceeding');
      return;
    }

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    onProceedToCheckout();
  };

  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-6">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-4">
        <ShoppingBagIcon className="h-5 w-5 text-gray-500" />
        <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
      </div>

      {/* Items Count */}
      <div className="flex justify-between text-sm text-gray-600 mb-4">
        <span>Items ({itemCount})</span>
        <span>GH₵ {subtotal.toFixed(2)}</span>
      </div>

      {/* Promo Code */}
      {showPromoCode && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <TagIcon className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Promo Code</span>
          </div>
          
          {appliedCoupon ? (
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center space-x-2">
                <GiftIcon className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium text-green-800">
                  {appliedCoupon.code}
                </span>
                <span className="text-xs text-green-600">
                  ({appliedCoupon.discount_percentage}% off)
                </span>
              </div>
              <button
                onClick={() => onApplyCoupon(null)} // Remove coupon
                className="text-xs text-green-600 hover:text-green-800"
              >
                Remove
              </button>
            </div>
          ) : (
            <div className="flex space-x-2">
              <input
                type="text"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                placeholder="Enter promo code"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleApplyCoupon}
                disabled={applyingCoupon || !promoCode.trim()}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {applyingCoupon ? 'Applying...' : 'Apply'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Cost Breakdown */}
      <div className="space-y-3 py-4 border-t border-gray-200">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal</span>
          <span className="text-gray-900">GH₵ {subtotal.toFixed(2)}</span>
        </div>

        {appliedCoupon && couponDiscount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-green-600">Discount ({appliedCoupon.code})</span>
            <span className="text-green-600">-GH₵ {couponDiscount.toFixed(2)}</span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <div className="flex items-center space-x-1">
            <TruckIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Shipping</span>
          </div>
          <span className="text-gray-900">
            {totalShipping > 0 ? `GH₵ ${totalShipping.toFixed(2)}` : 'Free'}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <div className="flex items-center space-x-1">
            <ShieldCheckIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Escrow Fee (5%)</span>
          </div>
          <span className="text-gray-900">GH₵ {escrowFee.toFixed(2)}</span>
        </div>

        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between">
            <span className="text-base font-medium text-gray-900">Total</span>
            <span className="text-lg font-bold text-blue-600">GH₵ {finalTotal.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Escrow Protection Info */}
      {showEscrowInfo && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start space-x-2">
            <ShieldCheckIcon className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-xs text-blue-800">
              <p className="font-medium mb-1">Escrow Protection</p>
              <p>Your payment is protected until you confirm delivery. Sellers only get paid after you're satisfied.</p>
            </div>
          </div>
        </div>
      )}

      {/* Issues Warning */}
      {hasIssues && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-start space-x-2">
            <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mt-0.5" />
            <div className="text-xs text-red-800">
              <p className="font-medium mb-1">Cart Issues</p>
              <p>{outOfStockItems.length} item(s) are out of stock. Please remove them to continue.</p>
            </div>
          </div>
        </div>
      )}

      {/* Checkout Button */}
      <button
        onClick={handleProceedToCheckout}
        disabled={isLoading || hasIssues || items.length === 0}
        className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Processing...
          </>
        ) : (
          <>
            <CreditCardIcon className="h-5 w-5 mr-2" />
            Proceed to Checkout
          </>
        )}
      </button>

      {/* Payment Methods */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 mb-2">We accept</p>
        <div className="flex items-center justify-center space-x-2">
          <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
            Visa
          </div>
          <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
            Mastercard
          </div>
          <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
            Mobile Money
          </div>
        </div>
      </div>

      {/* Additional Info */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-start space-x-2 text-xs text-gray-500">
          <InformationCircleIcon className="h-4 w-4 mt-0.5" />
          <div>
            <p className="mb-1">• Free returns within 7 days</p>
            <p className="mb-1">• Secure payment processing</p>
            <p>• 24/7 customer support</p>
          </div>
        </div>
      </div>

      {/* Continue Shopping */}
      <div className="mt-4 text-center">
        <button
          onClick={() => navigate('/products')}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Continue Shopping
        </button>
      </div>
    </div>
  );
}

export default CartSummary;
