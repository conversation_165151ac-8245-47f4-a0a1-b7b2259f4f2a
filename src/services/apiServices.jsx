import api from './api';

// Authentication Services
export const authAPI = {
  register: (userData) => api.post('/auth/register', { user: userData }),
  login: (credentials) => api.post('/auth/login', { user: credentials }),
  logout: () => api.delete('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  forgotPassword: (email) => api.post('/auth/forgot_password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset_password', { token, password }),
};

// User Services
export const userAPI = {
  // Profile management
  getProfile: () => api.get('/users/profile'),
  getUserProfile: (userId) => api.get(`/users/${userId}/profile`),
  updateProfile: (userData) => api.patch('/users/profile', { user: userData }),
  uploadAvatar: (formData) => api.post('/users/upload_avatar', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),

  // Account management
  changePassword: (passwords) => api.patch('/users/change_password', passwords),
  deleteAccount: () => api.delete('/users/account'),

  // Preferences
  updatePreferences: (data) => api.patch('/users/preferences', { preferences: data }),
  getPreferences: () => api.get('/users/preferences'),

  // Two-factor authentication
  enable2FA: () => api.post('/users/2fa/enable'),
  disable2FA: () => api.post('/users/2fa/disable'),
  verify2FA: (data) => api.post('/users/2fa/verify', data),

  // Admin user management
  getUsers: (params) => api.get('/admin/users', { params }),
  activateUser: (userId) => api.patch(`/admin/users/${userId}/activate`),
  deactivateUser: (userId) => api.patch(`/admin/users/${userId}/deactivate`),
  verifyUser: (userId) => api.patch(`/admin/users/${userId}/verify`),
  unverifyUser: (userId) => api.patch(`/admin/users/${userId}/unverify`),
  changeUserRole: (userId, role) => api.patch(`/admin/users/${userId}/role`, { role }),
  deleteUser: (userId) => api.delete(`/admin/users/${userId}`),
  bulkUserAction: (data) => api.post('/admin/users/bulk-action', data),

  // User statistics
  getUserStats: (userId) => api.get(`/users/${userId}/stats`),
  getUserTransactions: (userId, params) => api.get(`/users/${userId}/transactions`, { params }),
  getUserReviews: (userId, params) => api.get(`/users/${userId}/reviews`, { params }),

  // Subscription management
  getSubscription: () => api.get('/users/subscription'),
  upgradeSubscription: (planId) => api.post('/users/subscription/upgrade', { plan_id: planId }),
  cancelSubscription: () => api.post('/users/subscription/cancel'),
  getSubscriptionPlans: () => api.get('/subscription/plans'),
  getInvoices: (params) => api.get('/users/invoices', { params }),
  downloadInvoice: (invoiceId) => api.get(`/users/invoices/${invoiceId}/download`, { responseType: 'blob' }),
};

// Order Services
export const orderAPI = {
  getOrders: (params = {}) => api.get('/orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (orderData) => api.post('/orders', { order: orderData }),
  updateOrderStatus: (id, status, notes) => api.patch(`/orders/${id}/status`, { status, notes }),
  cancelOrder: (id, reason) => api.patch(`/orders/${id}/cancel`, { reason }),
  createReview: (id, reviewData) => api.post(`/orders/${id}/review`, reviewData),
};

// External Transaction Services
export const externalTransactionAPI = {
  getTransactions: (params = {}) => api.get('/external_transactions', { params }),
  getTransaction: (id) => api.get(`/external_transactions/${id}`),
  createTransaction: (transactionData) => api.post('/external_transactions', { transaction: transactionData }),
  acceptTransaction: (id) => api.patch(`/external_transactions/${id}/accept_transaction`),
  fundEscrow: (id, paymentData) => api.post(`/external_transactions/${id}/fund_escrow`, paymentData),
  confirmDelivery: (id, confirmationData) => api.patch(`/external_transactions/${id}/confirm_delivery`, confirmationData),
  cancelTransaction: (id, reason) => api.patch(`/external_transactions/${id}/cancel`, { reason }),
  submitShippingEvidence: (id, formData) => api.post(`/external_transactions/${id}/shipping_evidence`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),
};

// Payment Services
export const paymentAPI = {
  initializePayment: (paymentData) => api.post('/payments/initialize', paymentData),
  verifyPayment: (reference) => api.get(`/payments/${reference}/verify`),
  getPayments: (params = {}) => api.get('/payments', { params }),
  getPayment: (id) => api.get(`/payments/${id}`),
  requestRefund: (paymentId, refundData) => api.post(`/payments/${paymentId}/refund`, refundData),
  getRefunds: (params = {}) => api.get('/refunds', { params }),
  getRefund: (id) => api.get(`/refunds/${id}`),
};

// Product Services (Consolidated)
export const productAPI = {
  // Public product endpoints
  getProducts: (params = {}) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  getCategories: () => api.get('/products/categories'),
  getFeatured: () => api.get('/products/featured'),
  searchProducts: (query, params = {}) => api.get('/products/search', { params: { q: query, ...params } }),

  // Seller product management
  getSellerProducts: (params = {}) => api.get('/seller/products', { params }),
  createProduct: (productData) => api.post('/products', { product: productData }),
  updateProduct: (id, productData) => api.patch(`/products/${id}`, { product: productData }),
  deleteProduct: (id) => api.delete(`/products/${id}`),
  bulkUpdateProducts: (data) => api.patch('/seller/products/bulk', data),

  // Product images
  uploadImages: (id, formData) => api.post(`/products/${id}/images`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),
  uploadProductImages: (productId, images) => api.post(`/products/${productId}/images`, images, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),
  deleteProductImage: (productId, imageId) => api.delete(`/products/${productId}/images/${imageId}`),

  // Product analytics
  getProductAnalytics: (id) => api.get(`/seller/products/${id}/analytics`),
};

// Dashboard Services
export const dashboardAPI = {
  getAdminDashboard: () => api.get('/admin/dashboard/overview'),
  getSellerDashboard: () => api.get('/seller/dashboard/overview'),
  getBuyerDashboard: () => api.get('/buyer/dashboard/overview'),
};

// Chat Services
export const chatAPI = {
  // Get all conversations
  getConversations: () => api.get('/chat/conversations'),

  // Get single conversation
  getConversation: (id) => api.get(`/chat/conversations/${id}`),

  // Create new conversation
  createConversation: (data) => api.post('/chat/conversations', data),

  // Get messages for conversation
  getMessages: (conversationId, params) => api.get(`/chat/conversations/${conversationId}/messages`, { params }),

  // Send message
  sendMessage: (data) => {
    if (data.attachments && data.attachments.length > 0) {
      const formData = new FormData();
      formData.append('conversation_id', data.conversation_id);
      formData.append('content', data.content);
      formData.append('type', data.type);
      data.attachments.forEach((file) => formData.append('attachments', file));

      return api.post('/chat/messages', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    }
    return api.post('/chat/messages', data);
  },

  // Mark conversation as read
  markAsRead: (conversationId) => api.post(`/chat/conversations/${conversationId}/read`),

  // Archive conversation
  archiveConversation: (conversationId) => api.post(`/chat/conversations/${conversationId}/archive`),

  // Delete conversation
  deleteConversation: (conversationId) => api.delete(`/chat/conversations/${conversationId}`),

  // Get unread count
  getUnreadCount: () => api.get('/chat/unread-count'),

  // Search conversations
  searchConversations: (query) => api.get('/chat/conversations/search', {
    params: { q: query },
  }),
};

// File Services
export const fileAPI = {
  uploadFile: (formData) => api.post('/files/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),
  getFiles: (params = {}) => api.get('/files', { params }),
  deleteFile: (id) => api.delete(`/files/${id}`),
  downloadFile: (id) => api.get(`/files/${id}/download`, { responseType: 'blob' }),
};

// Dispute API
export const disputeAPI = {
  // User dispute operations
  createDispute: (data) => api.post('/disputes', data),
  getUserDisputes: (params) => api.get('/disputes/user', { params }),
  getDispute: (disputeId) => api.get(`/disputes/${disputeId}`),
  respondToDispute: (disputeId, data) => api.post(`/disputes/${disputeId}/respond`, data),
  uploadEvidence: (formData) => api.post('/disputes/evidence', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),
  downloadAttachment: (attachmentId) => api.get(`/disputes/attachments/${attachmentId}/download`, {
    responseType: 'blob',
  }),

  // Admin/Moderator operations
  getAllDisputes: (params) => api.get('/admin/disputes', { params }),
  moderateDispute: (disputeId, data) => api.post(`/admin/disputes/${disputeId}/moderate`, data),
  resolveDispute: (disputeId, data) => api.post(`/admin/disputes/${disputeId}/resolve`, data),
  escalateDispute: (disputeId, data) => api.post(`/admin/disputes/${disputeId}/escalate`, data),
  getDisputeStats: () => api.get('/admin/disputes/stats'),

  // Transaction related
  getTransaction: (transactionId) => api.get(`/transactions/${transactionId}`),
  getTransactionDisputes: (transactionId) => api.get(`/transactions/${transactionId}/disputes`),
};

// Analytics API
export const analyticsAPI = {
  // Admin analytics
  getAdminAnalytics: (params) => api.get('/admin/analytics', { params }),
  getPlatformMetrics: () => api.get('/admin/analytics/metrics'),
  getUserGrowthData: (params) => api.get('/admin/analytics/user-growth', { params }),
  getRevenueData: (params) => api.get('/admin/analytics/revenue', { params }),
  getTransactionData: (params) => api.get('/admin/analytics/transactions', { params }),

  // Seller analytics
  getSellerAnalytics: (params) => api.get('/seller/analytics', { params }),
  getSalesData: (params) => api.get('/seller/analytics/sales', { params }),
  getProductPerformance: (params) => api.get('/seller/analytics/products', { params }),
  getCustomerAnalytics: (params) => api.get('/seller/analytics/customers', { params }),

  // User analytics
  getUserAnalytics: (params) => api.get('/user/analytics', { params }),
  getSpendingAnalysis: (params) => api.get('/user/analytics/spending', { params }),
  getPurchaseHistory: (params) => api.get('/user/analytics/purchases', { params }),

  // Reports
  generateReport: (data) => api.post('/analytics/reports', data),
  getReports: () => api.get('/analytics/reports'),
  getReport: (reportId) => api.get(`/analytics/reports/${reportId}`),
  downloadReport: (reportId) => api.get(`/analytics/reports/${reportId}/download`, {
    responseType: 'blob',
  }),

  // Real-time data
  getRealTimeMetrics: () => api.get('/analytics/realtime'),
  getSystemHealth: () => api.get('/analytics/health'),
};

// Search API
export const searchAPI = {
  // Main search
  search: (params) => api.get('/search', { params }),
  getSuggestions: (params) => api.get('/search/suggestions', { params }),
  getFilterOptions: () => api.get('/search/filters'),

  // Search history and trends
  getRecentSearches: () => api.get('/search/recent'),
  getTrendingSearches: () => api.get('/search/trending'),
  saveSearch: (data) => api.post('/search/save', data),
  clearSearchHistory: () => api.delete('/search/history'),

  // Advanced search
  advancedSearch: (params) => api.post('/search/advanced', params),
  searchByImage: (formData) => api.post('/search/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  }),

  // Search analytics
  getSearchAnalytics: () => api.get('/search/analytics'),
  trackSearchClick: (data) => api.post('/search/track-click', data),
};

// Recommendation API
export const recommendationAPI = {
  // Main recommendations
  getPersonalizedRecommendations: (params) => api.get('/recommendations/personalized', { params }),
  getTrendingProducts: (params) => api.get('/recommendations/trending', { params }),
  getSimilarProducts: (productId, params) => api.get(`/recommendations/similar/${productId}`, { params }),
  getCategoryRecommendations: (categoryId, params) => api.get(`/recommendations/category/${categoryId}`, { params }),

  // User behavior based
  getRecentlyViewed: (params) => api.get('/recommendations/recently-viewed', { params }),
  getWishlistBasedRecommendations: (params) => api.get('/recommendations/wishlist-based', { params }),
  getCollaborativeRecommendations: (params) => api.get('/recommendations/collaborative', { params }),

  // Interaction tracking
  trackInteraction: (data) => api.post('/recommendations/track', data),
  provideFeedback: (data) => api.post('/recommendations/feedback', data),

  // Recommendation management
  getRecommendationSettings: () => api.get('/recommendations/settings'),
  updateRecommendationSettings: (data) => api.put('/recommendations/settings', data),

  // Analytics
  getRecommendationAnalytics: () => api.get('/recommendations/analytics'),
};

// Tools API
export const toolsAPI = {
  // General tools
  getAvailableTools: () => api.get('/tools'),
  getToolUsage: () => api.get('/tools/usage'),

  // Price calculator
  calculatePricing: (data) => api.post('/tools/price-calculator', data),
  getMarketAnalysis: (params) => api.get('/tools/market-analysis', { params }),

  // Market analyzer
  analyzeMarket: (data) => api.post('/tools/market-analyzer', data),
  getCompetitorAnalysis: (params) => api.get('/tools/competitor-analysis', { params }),
  getTrendAnalysis: (params) => api.get('/tools/trend-analysis', { params }),

  // Automation tools
  getAutomationRules: () => api.get('/tools/automation/rules'),
  createAutomationRule: (data) => api.post('/tools/automation/rules', data),
  updateAutomationRule: (ruleId, data) => api.put(`/tools/automation/rules/${ruleId}`, data),
  deleteAutomationRule: (ruleId) => api.delete(`/tools/automation/rules/${ruleId}`),

  // Security tools
  scanForFraud: (data) => api.post('/tools/security/fraud-scan', data),
  getSecurityReport: () => api.get('/tools/security/report'),
  updateSecuritySettings: (data) => api.put('/tools/security/settings', data),
};

// Testing API
export const testingAPI = {
  // Test suites
  getTestSuites: () => api.get('/testing/suites'),
  runTestSuite: (suiteId) => api.post(`/testing/suites/${suiteId}/run`),
  runAllTests: () => api.post('/testing/run-all'),
  getTestResults: (suiteId) => api.get(`/testing/suites/${suiteId}/results`),

  // Performance monitoring
  getPerformanceMetrics: () => api.get('/testing/performance/metrics'),
  runPerformanceTest: (data) => api.post('/testing/performance/test', data),
  getPerformanceHistory: (params) => api.get('/testing/performance/history', { params }),

  // Quality assurance
  getQualityReports: () => api.get('/testing/quality/reports'),
  runQualityCheck: (data) => api.post('/testing/quality/check', data),
  getCodeCoverage: () => api.get('/testing/quality/coverage'),

  // Optimization
  getOptimizationData: () => api.get('/testing/optimization'),
  runOptimization: (data) => api.post('/testing/optimization/run', data),
  getOptimizationRecommendations: () => api.get('/testing/optimization/recommendations'),

  // System health
  getSystemHealth: () => api.get('/testing/health'),
  runHealthCheck: () => api.post('/testing/health/check'),

  // Load testing
  runLoadTest: (data) => api.post('/testing/load-test', data),
  getLoadTestResults: (testId) => api.get(`/testing/load-test/${testId}/results`),
};

// Notification Services
export const notificationAPI = {
  // Get all notifications
  getNotifications: (params) => api.get('/notifications', { params }),

  // Get unread count
  getUnreadCount: () => api.get('/notifications/unread-count'),

  // Mark notification as read
  markAsRead: (notificationId) => api.post(`/notifications/${notificationId}/read`),

  // Mark all notifications as read
  markAllAsRead: () => api.post('/notifications/mark-all-read'),

  // Delete notification
  deleteNotification: (notificationId) => api.delete(`/notifications/${notificationId}`),

  // Delete multiple notifications
  deleteMultiple: (notificationIds) => api.post('/notifications/delete-multiple', {
    notification_ids: notificationIds,
  }),

  // Get notification settings
  getSettings: () => api.get('/notifications/settings'),

  // Update notification settings
  updateSettings: (settings) => api.put('/notifications/settings', settings),

  // Subscribe to push notifications
  subscribePush: (subscription) => api.post('/notifications/push/subscribe', subscription),

  // Unsubscribe from push notifications
  unsubscribePush: () => api.post('/notifications/push/unsubscribe'),
};
