import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { productAPI } from '../services/apiServices';

const useProducts = (filters = {}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  // Use useCallback to memoize the function and include it in dependencies
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await productAPI.getProducts(filters);
      if (response.data.success) {
        setProducts(response.data.products);
        setPagination(response.data.pagination);
      } else {
        throw new Error('Failed to fetch products');
      }
    } catch (err) {
      setError(err.message);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  }, [filters]); // Include filters in the dependency array

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]); // Now we can safely include fetchProducts

  return {
    products,
    loading,
    error,
    pagination,
    refetch: fetchProducts,
  };
};

export const useProduct = (productId) => {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Use useCallback here too for consistency and to avoid unnecessary re-renders
  const fetchProduct = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await productAPI.getProduct(productId);
      if (response.data.success) {
        setProduct(response.data.product);
      } else {
        throw new Error('Product not found');
      }
    } catch (err) {
      setError(err.message);
      toast.error('Failed to load product');
    } finally {
      setLoading(false);
    }
  }, [productId]); // Include productId in the dependency array

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId, fetchProduct]); // Include both dependencies

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
};

export default useProducts;
