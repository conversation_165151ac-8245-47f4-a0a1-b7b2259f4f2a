import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  RocketLaunchIcon,
  CpuChipIcon,
  PhotoIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { testingAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function OptimizationTools({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission, isAdmin } = useRoleAccess();
  
  const [optimizations, setOptimizations] = useState({});
  const [loading, setLoading] = useState(false);
  const [runningOptimizations, setRunningOptimizations] = useState(new Set());
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    if (hasPermission('optimization.view')) {
      fetchOptimizationData();
    }
  }, [hasPermission]);

  const fetchOptimizationData = async () => {
    try {
      setLoading(true);
      const response = await testingAPI.getOptimizationData();
      
      if (response.data.success) {
        setOptimizations(response.data.optimizations);
        setRecommendations(response.data.recommendations || []);
      }
    } catch (error) {
      logger.error('Failed to fetch optimization data:', error);
      toast.error('Failed to load optimization data');
    } finally {
      setLoading(false);
    }
  };

  const runOptimization = async (optimizationType) => {
    if (!hasPermission('optimization.run')) {
      toast.error('You do not have permission to run optimizations');
      return;
    }

    try {
      setRunningOptimizations(prev => new Set([...prev, optimizationType]));
      
      const response = await testingAPI.runOptimization({ type: optimizationType });
      
      if (response.data.success) {
        setOptimizations(prev => ({
          ...prev,
          [optimizationType]: response.data.result
        }));
        
        toast.success(`${optimizationType} optimization completed`);
        fetchOptimizationData(); // Refresh data
      }
    } catch (error) {
      logger.error('Optimization failed:', error);
      toast.error('Optimization failed');
    } finally {
      setRunningOptimizations(prev => {
        const newSet = new Set(prev);
        newSet.delete(optimizationType);
        return newSet;
      });
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value) => {
    return `${Math.round(value * 100)}%`;
  };

  const getOptimizationStatus = (optimization) => {
    if (!optimization) return 'pending';
    if (optimization.improvement > 0.2) return 'excellent';
    if (optimization.improvement > 0.1) return 'good';
    if (optimization.improvement > 0) return 'minor';
    return 'no_improvement';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'good':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'minor':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'no_improvement':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const renderOptimizationCard = (title, description, type, icon: Icon, optimization) => {
    const isRunning = runningOptimizations.has(type);
    const status = getOptimizationStatus(optimization);
    const statusColor = getStatusColor(status);

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Icon className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <p className="text-sm text-gray-500">{description}</p>
            </div>
          </div>

          {hasPermission('optimization.run') && (
            <button
              onClick={() => runOptimization(type)}
              disabled={isRunning}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              {isRunning ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <RocketLaunchIcon className="h-4 w-4 mr-2" />
                  Optimize
                </>
              )}
            </button>
          )}
        </div>

        {optimization ? (
          <div className="space-y-4">
            {/* Status */}
            <div className={`p-3 rounded-lg border ${statusColor}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">
                    {status === 'excellent' ? 'Excellent Improvement' :
                     status === 'good' ? 'Good Improvement' :
                     status === 'minor' ? 'Minor Improvement' :
                     'No Significant Improvement'}
                  </span>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {formatPercentage(optimization.improvement)} improvement
                  </div>
                  <div className="text-xs text-gray-500">
                    Last run: {new Date(optimization.timestamp).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Metrics */}
            {optimization.metrics && (
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(optimization.metrics).map(([key, value]) => (
                  <div key={key} className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {typeof value === 'number' && key.includes('size') ? 
                        formatBytes(value) : 
                        typeof value === 'number' && key.includes('time') ?
                        `${value}ms` :
                        value
                      }
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {key.replace('_', ' ')}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Details */}
            {optimization.details && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Optimization Details</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  {optimization.details.map((detail, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <span className="text-blue-600">•</span>
                      <span>{detail}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">Not Optimized</h4>
            <p className="text-gray-500">Run optimization to improve performance</p>
          </div>
        )}
      </div>
    );
  };

  if (!hasPermission('optimization.view')) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <RocketLaunchIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">You don't have permission to view optimization tools.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <RocketLaunchIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Optimization Tools</h1>
            <p className="text-gray-600 mt-1">
              Performance optimization and system tuning
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchOptimizationData}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>

          {hasPermission('optimization.run') && (
            <button
              onClick={() => {
                runOptimization('bundle');
                runOptimization('images');
                runOptimization('cache');
                runOptimization('database');
              }}
              disabled={runningOptimizations.size > 0}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {runningOptimizations.size > 0 ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <BoltIcon className="h-4 w-4 mr-2" />
                  Optimize All
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Optimization Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderOptimizationCard(
          'Bundle Optimization',
          'Optimize JavaScript and CSS bundles',
          'bundle',
          DocumentTextIcon,
          optimizations.bundle
        )}
        
        {renderOptimizationCard(
          'Image Optimization',
          'Compress and optimize images',
          'images',
          PhotoIcon,
          optimizations.images
        )}
        
        {renderOptimizationCard(
          'Cache Optimization',
          'Optimize caching strategies',
          'cache',
          CpuChipIcon,
          optimizations.cache
        )}
        
        {renderOptimizationCard(
          'Database Optimization',
          'Optimize database queries and indexes',
          'database',
          ChartBarIcon,
          optimizations.database
        )}
      </div>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            <h2 className="text-xl font-bold text-gray-900">Optimization Recommendations</h2>
          </div>

          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  rec.priority === 'high' ? 'bg-red-50 border-red-200' :
                  rec.priority === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                  'bg-blue-50 border-blue-200'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">
                      {rec.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {rec.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Impact: {rec.impact}</span>
                      <span>Effort: {rec.effort}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                      rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {rec.priority} priority
                    </span>
                    
                    {hasPermission('optimization.run') && rec.actionable && (
                      <button
                        onClick={() => runOptimization(rec.type)}
                        className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Apply Fix
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Summary */}
      {Object.keys(optimizations).length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <ChartBarIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">Performance Summary</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {(() => {
              const totalOptimizations = Object.keys(optimizations).length;
              const avgImprovement = Object.values(optimizations).reduce((sum, opt) => 
                sum + (opt.improvement || 0), 0) / totalOptimizations;
              const excellentCount = Object.values(optimizations).filter(opt => 
                getOptimizationStatus(opt) === 'excellent').length;
              const totalSavings = Object.values(optimizations).reduce((sum, opt) => 
                sum + (opt.metrics?.size_reduction || 0), 0);

              return (
                <>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{totalOptimizations}</div>
                    <div className="text-sm text-gray-500">Optimizations</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatPercentage(avgImprovement)}
                    </div>
                    <div className="text-sm text-gray-500">Avg Improvement</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{excellentCount}</div>
                    <div className="text-sm text-gray-500">Excellent Results</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatBytes(totalSavings)}
                    </div>
                    <div className="text-sm text-gray-500">Total Savings</div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}

export default OptimizationTools;
