import React from 'react';
import {
  TrendingUpIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

function RevenueChart({ data, className = '' }) {
  if (!data || !data.length) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 rounded-lg ${className}`}>
        <div className="text-center">
          <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No Revenue Data</h4>
          <p className="text-sm text-gray-500">Revenue data will appear here once available</p>
        </div>
      </div>
    );
  }

  // Calculate max value for scaling
  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue;

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate bar height percentage
  const getBarHeight = (value) => {
    if (range === 0) return 50; // Default height if all values are the same
    return ((value - minValue) / range) * 80 + 10; // 10-90% range
  };

  // Calculate trend
  const getTrend = () => {
    if (data.length < 2) return { direction: 'neutral', percentage: 0 };
    
    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;
    const change = ((lastValue - firstValue) / firstValue) * 100;
    
    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      percentage: Math.abs(change).toFixed(1)
    };
  };

  const trend = getTrend();

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-gray-900">
            {formatCurrency(data[data.length - 1]?.value || 0)}
          </span>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend.direction === 'up' 
              ? 'bg-green-100 text-green-800' 
              : trend.direction === 'down'
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            <TrendingUpIcon className={`h-3 w-3 ${
              trend.direction === 'down' ? 'rotate-180' : ''
            }`} />
            <span>{trend.percentage}%</span>
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          Last {data.length} periods
        </div>
      </div>

      {/* Chart Area */}
      <div className="flex-1 flex items-end space-x-1 px-2">
        {data.map((item, index) => (
          <div key={index} className="flex-1 flex flex-col items-center group">
            {/* Bar */}
            <div className="w-full flex flex-col items-center">
              <div
                className="w-full bg-blue-500 rounded-t-sm hover:bg-blue-600 transition-colors duration-200 relative group-hover:shadow-lg"
                style={{ height: `${getBarHeight(item.value)}%` }}
              >
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  {formatCurrency(item.value)}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                </div>
              </div>
            </div>
            
            {/* Label */}
            <div className="mt-2 text-xs text-gray-500 text-center">
              {formatDate(item.date)}
            </div>
          </div>
        ))}
      </div>

      {/* Y-axis labels */}
      <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-8 -ml-16">
        <span className="text-xs text-gray-500">{formatCurrency(maxValue)}</span>
        <span className="text-xs text-gray-500">{formatCurrency((maxValue + minValue) / 2)}</span>
        <span className="text-xs text-gray-500">{formatCurrency(minValue)}</span>
      </div>

      {/* Summary Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-xs text-gray-500">Total</p>
            <p className="text-sm font-medium text-gray-900">
              {formatCurrency(data.reduce((sum, item) => sum + item.value, 0))}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Average</p>
            <p className="text-sm font-medium text-gray-900">
              {formatCurrency(data.reduce((sum, item) => sum + item.value, 0) / data.length)}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Peak</p>
            <p className="text-sm font-medium text-gray-900">
              {formatCurrency(maxValue)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RevenueChart;
