import { useSelector, useDispatch } from 'react-redux';
import { loginUser, registerUser, logoutUser } from '../redux/slice/authSlice';

const useAuth = () => {
  const dispatch = useDispatch();
  const {
    user, token, isAuthenticated, isLoading, error,
  } = useSelector((state) => state.auth);

  const login = (credentials) => dispatch(loginUser(credentials));
  const register = (userData) => dispatch(registerUser(userData));
  const logout = () => dispatch(logoutUser());

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
  };
};

export default useAuth;
