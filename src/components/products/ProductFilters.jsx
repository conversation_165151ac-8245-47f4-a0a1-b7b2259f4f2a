import React, { useState, useEffect } from 'react';
import {
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  AdjustmentsHorizontalIcon,
  StarIcon,
  MapPinIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

function ProductFilters({ 
  filters, 
  onFiltersChange, 
  categories = [], 
  brands = [],
  locations = [],
  isLoading = false,
  className = '' 
}) {
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    price: true,
    brand: false,
    condition: false,
    rating: false,
    location: false,
    features: false
  });

  const [priceRange, setPriceRange] = useState({
    min: filters.price_min || '',
    max: filters.price_max || ''
  });

  const [tempFilters, setTempFilters] = useState(filters);

  useEffect(() => {
    setTempFilters(filters);
    setPriceRange({
      min: filters.price_min || '',
      max: filters.price_max || ''
    });
  }, [filters]);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...tempFilters };
    
    if (Array.isArray(newFilters[key])) {
      if (newFilters[key].includes(value)) {
        newFilters[key] = newFilters[key].filter(item => item !== value);
      } else {
        newFilters[key] = [...newFilters[key], value];
      }
    } else {
      newFilters[key] = value;
    }

    setTempFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePriceChange = (type, value) => {
    const newRange = { ...priceRange, [type]: value };
    setPriceRange(newRange);
    
    const newFilters = { ...tempFilters };
    if (newRange.min) newFilters.price_min = parseFloat(newRange.min);
    if (newRange.max) newFilters.price_max = parseFloat(newRange.max);
    
    setTempFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      categories: [],
      brands: [],
      conditions: [],
      rating: null,
      locations: [],
      features: [],
      price_min: null,
      price_max: null
    };
    
    setTempFilters(clearedFilters);
    setPriceRange({ min: '', max: '' });
    onFiltersChange(clearedFilters);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (tempFilters.categories?.length) count += tempFilters.categories.length;
    if (tempFilters.brands?.length) count += tempFilters.brands.length;
    if (tempFilters.conditions?.length) count += tempFilters.conditions.length;
    if (tempFilters.locations?.length) count += tempFilters.locations.length;
    if (tempFilters.features?.length) count += tempFilters.features.length;
    if (tempFilters.rating) count += 1;
    if (tempFilters.price_min || tempFilters.price_max) count += 1;
    return count;
  };

  const conditionOptions = [
    { value: 'new', label: 'New', count: 245 },
    { value: 'like_new', label: 'Like New', count: 89 },
    { value: 'good', label: 'Good', count: 156 },
    { value: 'fair', label: 'Fair', count: 67 },
    { value: 'poor', label: 'Poor', count: 23 }
  ];

  const featureOptions = [
    { value: 'free_shipping', label: 'Free Shipping', count: 189 },
    { value: 'fast_delivery', label: 'Fast Delivery', count: 134 },
    { value: 'warranty', label: 'Warranty Included', count: 98 },
    { value: 'returns', label: 'Easy Returns', count: 167 },
    { value: 'verified_seller', label: 'Verified Seller', count: 234 }
  ];

  const renderStarRating = (rating) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600">& up</span>
      </div>
    );
  };

  const FilterSection = ({ title, isExpanded, onToggle, children, count }) => (
    <div className="border-b border-gray-200 pb-4">
      <button
        onClick={onToggle}
        className="flex items-center justify-between w-full py-2 text-left"
      >
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900">{title}</span>
          {count > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
              {count}
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUpIcon className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDownIcon className="h-4 w-4 text-gray-500" />
        )}
      </button>
      {isExpanded && <div className="mt-3">{children}</div>}
    </div>
  );

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5 text-gray-500" />
          <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          {getActiveFilterCount() > 0 && (
            <span className="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full">
              {getActiveFilterCount()}
            </span>
          )}
        </div>
        
        {getActiveFilterCount() > 0 && (
          <button
            onClick={clearFilters}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Clear all
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Categories */}
        <FilterSection
          title="Categories"
          isExpanded={expandedSections.category}
          onToggle={() => toggleSection('category')}
          count={tempFilters.categories?.length || 0}
        >
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {categories.map((category) => (
              <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={tempFilters.categories?.includes(category.id) || false}
                  onChange={() => handleFilterChange('categories', category.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{category.name}</span>
                <span className="text-xs text-gray-500">({category.count || 0})</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Price Range */}
        <FilterSection
          title="Price Range"
          isExpanded={expandedSections.price}
          onToggle={() => toggleSection('price')}
          count={priceRange.min || priceRange.max ? 1 : 0}
        >
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Min Price
                </label>
                <input
                  type="number"
                  value={priceRange.min}
                  onChange={(e) => handlePriceChange('min', e.target.value)}
                  placeholder="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Max Price
                </label>
                <input
                  type="number"
                  value={priceRange.max}
                  onChange={(e) => handlePriceChange('max', e.target.value)}
                  placeholder="Any"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            {/* Quick Price Ranges */}
            <div className="flex flex-wrap gap-2">
              {[
                { label: 'Under GH₵100', min: 0, max: 100 },
                { label: 'GH₵100-500', min: 100, max: 500 },
                { label: 'GH₵500-1000', min: 500, max: 1000 },
                { label: 'Over GH₵1000', min: 1000, max: null }
              ].map((range) => (
                <button
                  key={range.label}
                  onClick={() => {
                    setPriceRange({ min: range.min.toString(), max: range.max?.toString() || '' });
                    handlePriceChange('min', range.min.toString());
                    if (range.max) handlePriceChange('max', range.max.toString());
                  }}
                  className="px-3 py-1 text-xs border border-gray-300 rounded-full hover:bg-gray-50"
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>
        </FilterSection>

        {/* Brands */}
        {brands.length > 0 && (
          <FilterSection
            title="Brands"
            isExpanded={expandedSections.brand}
            onToggle={() => toggleSection('brand')}
            count={tempFilters.brands?.length || 0}
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {brands.map((brand) => (
                <label key={brand.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={tempFilters.brands?.includes(brand.id) || false}
                    onChange={() => handleFilterChange('brands', brand.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{brand.name}</span>
                  <span className="text-xs text-gray-500">({brand.count || 0})</span>
                </label>
              ))}
            </div>
          </FilterSection>
        )}

        {/* Condition */}
        <FilterSection
          title="Condition"
          isExpanded={expandedSections.condition}
          onToggle={() => toggleSection('condition')}
          count={tempFilters.conditions?.length || 0}
        >
          <div className="space-y-2">
            {conditionOptions.map((condition) => (
              <label key={condition.value} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={tempFilters.conditions?.includes(condition.value) || false}
                  onChange={() => handleFilterChange('conditions', condition.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{condition.label}</span>
                <span className="text-xs text-gray-500">({condition.count})</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Rating */}
        <FilterSection
          title="Customer Rating"
          isExpanded={expandedSections.rating}
          onToggle={() => toggleSection('rating')}
          count={tempFilters.rating ? 1 : 0}
        >
          <div className="space-y-2">
            {[4, 3, 2, 1].map((rating) => (
              <label key={rating} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="rating"
                  checked={tempFilters.rating === rating}
                  onChange={() => handleFilterChange('rating', rating)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                {renderStarRating(rating)}
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Location */}
        {locations.length > 0 && (
          <FilterSection
            title="Location"
            isExpanded={expandedSections.location}
            onToggle={() => toggleSection('location')}
            count={tempFilters.locations?.length || 0}
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {locations.map((location) => (
                <label key={location.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={tempFilters.locations?.includes(location.id) || false}
                    onChange={() => handleFilterChange('locations', location.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <MapPinIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">{location.name}</span>
                  <span className="text-xs text-gray-500">({location.count || 0})</span>
                </label>
              ))}
            </div>
          </FilterSection>
        )}

        {/* Features */}
        <FilterSection
          title="Features"
          isExpanded={expandedSections.features}
          onToggle={() => toggleSection('features')}
          count={tempFilters.features?.length || 0}
        >
          <div className="space-y-2">
            {featureOptions.map((feature) => (
              <label key={feature.value} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={tempFilters.features?.includes(feature.value) || false}
                  onChange={() => handleFilterChange('features', feature.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <TagIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-700">{feature.label}</span>
                <span className="text-xs text-gray-500">({feature.count})</span>
              </label>
            ))}
          </div>
        </FilterSection>
      </div>

      {/* Apply Filters Button (Mobile) */}
      <div className="mt-6 lg:hidden">
        <button
          onClick={() => onFiltersChange(tempFilters)}
          disabled={isLoading}
          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
          {isLoading ? 'Applying...' : `Apply Filters (${getActiveFilterCount()})`}
        </button>
      </div>
    </div>
  );
}

export default ProductFilters;
