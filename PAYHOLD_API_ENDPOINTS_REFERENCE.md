# PayHold API Endpoints - Quick Reference

## 🔐 Authentication Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/auth/register` | User registration | No |
| POST | `/api/v1/auth/login` | User login | No |
| POST | `/api/v1/auth/refresh` | Refresh JWT token | Yes |
| DELETE | `/api/v1/auth/logout` | User logout | Yes |
| POST | `/api/v1/auth/forgot_password` | Request password reset | No |
| POST | `/api/v1/auth/reset_password` | Reset password with token | No |
| POST | `/api/v1/auth/verify_email` | Verify email address | No |
| POST | `/api/v1/auth/resend_verification` | Resend verification email | No |

## 👤 User Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/users/profile` | Get current user profile | Yes |
| PATCH | `/api/v1/users/profile` | Update user profile | Yes |
| PATCH | `/api/v1/users/change_password` | Change password | Yes |
| POST | `/api/v1/users/upload_avatar` | Upload profile image | Yes |
| GET | `/api/v1/users/preferences` | Get user preferences | Yes |
| PATCH | `/api/v1/users/preferences` | Update preferences | Yes |
| DELETE | `/api/v1/users/account` | Delete user account | Yes |

## 🛍️ Product Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/products` | List all products | No |
| GET | `/api/v1/products/:id` | Get single product | No |
| POST | `/api/v1/products` | Create product (Seller) | Yes |
| PATCH | `/api/v1/products/:id` | Update product (Seller) | Yes |
| DELETE | `/api/v1/products/:id` | Delete product (Seller) | Yes |
| POST | `/api/v1/products/:id/images` | Upload product images | Yes |
| DELETE | `/api/v1/products/:id/images/:image_id` | Delete product image | Yes |
| GET | `/api/v1/products/categories` | Get product categories | No |
| GET | `/api/v1/products/featured` | Get featured products | No |
| POST | `/api/v1/products/:id/favorite` | Add to favorites | Yes |
| DELETE | `/api/v1/products/:id/favorite` | Remove from favorites | Yes |

## 🌐 External Transactions (All Platforms & Offline Deals)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/external_transactions` | List external transactions | Yes |
| GET | `/api/v1/external_transactions/:id` | Get single external transaction | Yes |
| POST | `/api/v1/external_transactions` | Create external transaction | Yes |
| PATCH | `/api/v1/external_transactions/:id/accept_transaction` | Accept transaction (Seller) | Yes |
| PATCH | `/api/v1/external_transactions/:id/decline_transaction` | Decline transaction (Seller) | Yes |
| POST | `/api/v1/external_transactions/:id/fund_escrow` | Fund escrow (Buyer) | Yes |
| PATCH | `/api/v1/external_transactions/:id/confirm_delivery` | Confirm delivery (Buyer) | Yes |
| POST | `/api/v1/external_transactions/:id/create_dispute` | Create dispute | Yes |
| PATCH | `/api/v1/external_transactions/:id/cancel` | Cancel transaction | Yes |
| GET | `/api/v1/external_transactions/statistics` | Get transaction statistics | Yes |

## 📦 Order Management (Internal Store)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/orders` | List user orders | Yes |
| GET | `/api/v1/orders/:id` | Get single order | Yes |
| POST | `/api/v1/orders` | Create new order | Yes |
| PATCH | `/api/v1/orders/:id/status` | Update order status (Seller) | Yes |
| PATCH | `/api/v1/orders/:id/cancel` | Cancel order | Yes |
| POST | `/api/v1/orders/:id/dispute` | Create dispute | Yes |
| GET | `/api/v1/orders/:id/tracking` | Get order tracking | Yes |
| POST | `/api/v1/orders/:id/review` | Add order review | Yes |

## 💳 Payment Processing
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/payments/initialize` | Initialize payment | Yes |
| GET | `/api/v1/payments/:reference/verify` | Verify payment | Yes |
| GET | `/api/v1/payments` | Get payment history | Yes |
| GET | `/api/v1/payments/:id` | Get payment details | Yes |
| POST | `/api/v1/payments/:id/refund` | Request refund | Yes |
| GET | `/api/v1/payments/methods` | Get payment methods | Yes |
| POST | `/api/v1/payments/webhook` | Paystack webhook | No |

## 🏦 Escrow Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/escrow/:order_id` | Get escrow details | Yes |
| POST | `/api/v1/escrow/:order_id/release` | Release escrow funds | Yes |
| POST | `/api/v1/escrow/:order_id/dispute` | Dispute escrow | Yes |
| GET | `/api/v1/escrow/transactions` | Get escrow transactions | Yes |

## 📊 Dashboard APIs

### Admin Dashboard
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/admin/dashboard/overview` | Admin dashboard overview | Admin |
| GET | `/api/v1/admin/dashboard/users` | User management | Admin |
| GET | `/api/v1/admin/dashboard/orders` | Order management | Admin |
| GET | `/api/v1/admin/dashboard/payments` | Payment management | Admin |
| GET | `/api/v1/admin/dashboard/disputes` | Dispute management | Admin |
| GET | `/api/v1/admin/dashboard/analytics` | System analytics | Admin |
| GET | `/api/v1/admin/dashboard/reports` | Generate reports | Admin |

### Seller Dashboard
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/seller/dashboard/overview` | Seller dashboard overview | Seller |
| GET | `/api/v1/seller/dashboard/products` | Product management | Seller |
| GET | `/api/v1/seller/dashboard/orders` | Order management | Seller |
| GET | `/api/v1/seller/dashboard/analytics` | Sales analytics | Seller |
| GET | `/api/v1/seller/dashboard/reviews` | Customer reviews | Seller |

### Buyer Dashboard
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/buyer/dashboard/overview` | Buyer dashboard overview | Buyer |
| GET | `/api/v1/buyer/dashboard/orders` | Order history | Buyer |
| GET | `/api/v1/buyer/dashboard/favorites` | Favorite products | Buyer |
| GET | `/api/v1/buyer/dashboard/reviews` | My reviews | Buyer |

## 💬 Real-time Communication
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/chat/conversations` | List conversations | Yes |
| GET | `/api/v1/chat/conversations/:id` | Get conversation | Yes |
| POST | `/api/v1/chat/conversations` | Create conversation | Yes |
| GET | `/api/v1/chat/conversations/:id/messages` | Get messages | Yes |
| POST | `/api/v1/chat/messages` | Send message | Yes |
| PATCH | `/api/v1/chat/messages/:id/read` | Mark as read | Yes |
| POST | `/api/v1/chat/messages/:id/attachments` | Upload attachment | Yes |

### WebSocket Channels
- **ChatChannel**: Real-time messaging
- **NotificationChannel**: Real-time notifications
- **OrderChannel**: Order status updates

## 🔍 Search & Recommendations
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/search/products` | Search products | No |
| GET | `/api/v1/search/advanced` | Advanced search | No |
| GET | `/api/v1/search/suggestions` | Search suggestions | No |
| GET | `/api/v1/recommendations/products` | Product recommendations | Yes |
| GET | `/api/v1/recommendations/similar/:id` | Similar products | No |
| GET | `/api/v1/recommendations/trending` | Trending products | No |

## 🔒 Security Features
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/security/setup_2fa` | Setup 2FA | Yes |
| POST | `/api/v1/security/enable_2fa` | Enable 2FA | Yes |
| POST | `/api/v1/security/disable_2fa` | Disable 2FA | Yes |
| POST | `/api/v1/security/verify_2fa` | Verify 2FA token | Yes |
| GET | `/api/v1/security/devices` | List trusted devices | Yes |
| DELETE | `/api/v1/security/devices/:id` | Remove device | Yes |
| GET | `/api/v1/security/activity` | Security activity log | Yes |

## 📁 File Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/files` | List files | Yes |
| GET | `/api/v1/files/:id` | Get file info | Yes |
| POST | `/api/v1/files/upload` | Upload single file | Yes |
| POST | `/api/v1/files/upload_multiple` | Upload multiple files | Yes |
| GET | `/api/v1/files/:id/download` | Download file | Yes |
| DELETE | `/api/v1/files/:id` | Delete file | Yes |
| GET | `/api/v1/files/categories` | Get file categories | Yes |
| GET | `/api/v1/files/types` | Get supported types | Yes |

## 📈 Analytics & Business Intelligence
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/analytics/overview` | Analytics overview | Admin |
| GET | `/api/v1/analytics/revenue` | Revenue analytics | Admin |
| GET | `/api/v1/analytics/users` | User analytics | Admin |
| GET | `/api/v1/analytics/products` | Product analytics | Admin |
| GET | `/api/v1/analytics/orders` | Order analytics | Admin |
| GET | `/api/v1/analytics/business_intelligence` | BI dashboard | Admin |
| GET | `/api/v1/analytics/insights` | Business insights | Admin |
| GET | `/api/v1/analytics/cohort` | Cohort analysis | Admin |
| POST | `/api/v1/analytics/custom_report` | Custom reports | Admin |

## 🔔 Advanced Notifications
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/advanced_notifications` | List notifications | Yes |
| POST | `/api/v1/advanced_notifications/send` | Send notification | Admin |
| POST | `/api/v1/advanced_notifications/bulk_send` | Bulk send | Admin |
| GET | `/api/v1/advanced_notifications/preferences` | Get preferences | Yes |
| PATCH | `/api/v1/advanced_notifications/preferences` | Update preferences | Yes |
| GET | `/api/v1/advanced_notifications/templates` | List templates | Admin |
| POST | `/api/v1/advanced_notifications/templates` | Create template | Admin |
| GET | `/api/v1/advanced_notifications/unread_count` | Unread count | Yes |
| POST | `/api/v1/advanced_notifications/mark_read` | Mark as read | Yes |

## 🛡️ System Monitoring
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/monitoring/health` | System health check | No |
| GET | `/api/v1/monitoring/metrics` | System metrics | Admin |
| GET | `/api/v1/monitoring/logs` | System logs | Admin |
| POST | `/api/v1/monitoring/log` | Create log entry | Admin |
| GET | `/api/v1/monitoring/dashboard` | Monitoring dashboard | Admin |
| GET | `/api/v1/monitoring/alerts` | System alerts | Admin |

## 🚀 Performance & Optimization
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/performance/analyze` | Performance analysis | Admin |
| GET | `/api/v1/performance/monitor` | Real-time monitoring | Admin |
| GET | `/api/v1/performance/report` | Performance report | Admin |
| GET | `/api/v1/performance/recommendations` | Optimization tips | Admin |
| GET | `/api/v1/performance/health_score` | System health score | Admin |

## 💾 Backup & Recovery
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/backup_recovery/create_backup` | Create backup | Admin |
| POST | `/api/v1/backup_recovery/restore` | Restore from backup | Admin |
| GET | `/api/v1/backup_recovery/backups` | List backups | Admin |
| GET | `/api/v1/backup_recovery/backups/:id/status` | Backup status | Admin |
| POST | `/api/v1/backup_recovery/schedule` | Schedule backup | Admin |
| GET | `/api/v1/backup_recovery/analytics` | Backup analytics | Admin |

## 🧪 Testing & Documentation
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/docs` | API documentation | No |
| GET | `/api/v1/docs/openapi` | OpenAPI specification | No |
| GET | `/api/v1/docs/swagger` | Swagger UI | No |
| POST | `/api/v1/testing/automated_suite` | Run test suite | Admin |
| GET | `/api/v1/testing/automated_report/:id` | Test report | Admin |

## 📋 Common Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 20, max: 100)

### Filtering
- `search`: Search query
- `category`: Filter by category
- `status`: Filter by status
- `start_date`: Start date filter (YYYY-MM-DD)
- `end_date`: End date filter (YYYY-MM-DD)
- `sort`: Sort field
- `order`: Sort order (asc, desc)

### Product Specific
- `min_price`: Minimum price
- `max_price`: Maximum price
- `seller_id`: Filter by seller
- `featured`: Show only featured products

### Order Specific
- `order_status`: Filter by order status
- `payment_status`: Filter by payment status
- `date_range`: Date range filter

## 🔑 Authentication Headers

All protected endpoints require:
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## 📱 Response Format

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Operation successful",
  "pagination": {...} // For paginated responses
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "error_code": "ERROR_CODE",
  "details": {...},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🌐 WebSocket Connection

```javascript
// WebSocket URL
ws://localhost:3000/cable

// Authentication
ws://localhost:3000/cable?token=<jwt_token>

// Channels
- ChatChannel
- NotificationChannel
- OrderChannel
```

## 🚀 Quick Integration Tips

1. **Base URL**: `http://localhost:3000/api/v1`
2. **Authentication**: JWT Bearer token in Authorization header
3. **Content-Type**: `application/json` for most requests
4. **File Uploads**: Use `multipart/form-data`
5. **WebSocket**: Use Action Cable for real-time features
6. **Pagination**: Most list endpoints support pagination
7. **Error Handling**: Check `success` field in response
8. **Rate Limiting**: Respect rate limits (if implemented)

This reference covers all 200+ API endpoints available in the PayHold backend system. Each endpoint is fully functional and ready for frontend integration.
