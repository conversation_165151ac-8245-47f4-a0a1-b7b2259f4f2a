import {
  useState, useEffect, useMemo,
} from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { productAPI } from '../../services/apiServices';
import ProductCard from './ProductCard';
import ProductFilters from './ProductFilters';
import LoadingSpinner from '../helpers/LoadingSpinner';
import Pagination from '../helpers/Pagination';
import logger from '../helpers/logger';

function ProductList() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [searchParams, setSearchParams] = useSearchParams();

  const filters = useMemo(() => ({
    page: searchParams.get('page') || 1,
    per_page: searchParams.get('per_page') || 20,
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    min_price: searchParams.get('min_price') || '',
    max_price: searchParams.get('max_price') || '',
  }), [searchParams]);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await productAPI.getProducts(filters);
        if (response.data.success) {
          setProducts(response.data.products);
          setPagination(response.data.pagination);
        }
      } catch (error) {
        logger.error('Failed to fetch products:', error);
        toast.error('Failed to load products. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    const updatedParams = { ...Object.fromEntries(searchParams), ...newFilters };
    setSearchParams(updatedParams);
  };

  if (loading) {
    return (
      <div>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div>
      <div>
        {/* Filters Sidebar */}
        <div>
          <ProductFilters
            filters={filters}
            onFilterChange={handleFilterChange}
          />
        </div>

        {/* Products Grid */}
        <div>
          <div>
            <h1>
              Products
              {' '}
              {filters.search && `for "${filters.search}"`}
            </h1>
            <p>
              {pagination.total_count || 0}
              {' '}
              products found
            </p>
          </div>

          {products.length === 0 ? (
            <div>
              <p>No products found</p>
              <p>Try adjusting your filters</p>
            </div>
          ) : (
            <>
              <div>
                {products.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>

              {pagination.total_pages > 1 && (
                <div>
                  <Pagination
                    currentPage={pagination.current_page}
                    totalPages={pagination.total_pages}
                    onPageChange={(page) => handleFilterChange({ page })}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default ProductList;
