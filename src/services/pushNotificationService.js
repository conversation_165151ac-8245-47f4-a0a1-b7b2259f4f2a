import { notificationAPI } from './apiServices';
import logger from '../components/helpers/logger';

class PushNotificationService {
  constructor() {
    this.isSupported = 'Notification' in window && 'serviceWorker' in navigator;
    this.permission = this.isSupported ? Notification.permission : 'denied';
    this.subscription = null;
    this.registration = null;
  }

  // Check if push notifications are supported
  isSupported() {
    return this.isSupported;
  }

  // Get current permission status
  getPermission() {
    return this.permission;
  }

  // Request permission for push notifications
  async requestPermission() {
    if (!this.isSupported) {
      throw new Error('Push notifications are not supported in this browser');
    }

    try {
      const permission = await Notification.requestPermission();
      this.permission = permission;

      if (permission === 'granted') {
        await this.registerServiceWorker();
        await this.subscribe();
        return true;
      }
      if (permission === 'denied') {
        throw new Error('Push notifications were denied by the user');
      }
      throw new Error('Push notification permission was dismissed');
    } catch (error) {
      logger.error('Failed to request push notification permission:', error);
      throw error;
    }
  }

  // Register service worker
  async registerServiceWorker() {
    if (!('serviceWorker' in navigator)) {
      throw new Error('Service workers are not supported');
    }

    try {
      // Register the service worker
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      logger.log('Service worker registered:', this.registration);

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;

      return this.registration;
    } catch (error) {
      logger.error('Failed to register service worker:', error);
      throw error;
    }
  }

  // Subscribe to push notifications
  async subscribe() {
    if (!this.registration) {
      await this.registerServiceWorker();
    }

    try {
      // Check if already subscribed
      const existingSubscription = await this.registration.pushManager.getSubscription();

      if (existingSubscription) {
        this.subscription = existingSubscription;
        logger.log('Already subscribed to push notifications');
        return existingSubscription;
      }

      // Create new subscription
      const vapidPublicKey = process.env.REACT_APP_VAPID_PUBLIC_KEY;

      if (!vapidPublicKey) {
        throw new Error('VAPID public key not configured');
      }

      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey),
      });

      this.subscription = subscription;
      logger.log('Subscribed to push notifications:', subscription);

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);

      return subscription;
    } catch (error) {
      logger.error('Failed to subscribe to push notifications:', error);
      throw error;
    }
  }

  // Unsubscribe from push notifications
  async unsubscribe() {
    if (!this.subscription) {
      logger.log('Not subscribed to push notifications');
      return;
    }

    try {
      // Unsubscribe from push manager
      await this.subscription.unsubscribe();

      // Notify server
      await notificationAPI.unsubscribePush();

      this.subscription = null;
      logger.log('Unsubscribed from push notifications');
    } catch (error) {
      logger.error('Failed to unsubscribe from push notifications:', error);
      throw error;
    }
  }

  // Send subscription to server
  async sendSubscriptionToServer(subscription) {
    try {
      const subscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')),
        },
      };

      await notificationAPI.subscribePush(subscriptionData);
      logger.log('Subscription sent to server');
    } catch (error) {
      logger.error('Failed to send subscription to server:', error);
      throw error;
    }
  }

  // Show local notification
  showNotification(title, options = {}) {
    if (!this.isSupported || this.permission !== 'granted') {
      logger.warn('Cannot show notification: permission not granted');
      return;
    }

    const defaultOptions = {
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      vibrate: [200, 100, 200],
      tag: 'payhold-notification',
      renotify: true,
      requireInteraction: false,
      ...options,
    };

    if (this.registration) {
      // Show notification via service worker
      this.registration.showNotification(title, defaultOptions);
      return;
    }
    // Fallback to browser notification
    // eslint-disable-next-line no-new
    new Notification(title, defaultOptions);
  }

  // Handle notification click
  static handleNotificationClick(event) {
    event.notification.close();

    const notificationData = event.notification.data;

    if (notificationData && notificationData.url) {
      // Open the specified URL
      event.waitUntil(
        // eslint-disable-next-line no-undef
        clients.matchAll().then((clientList) => {
          // Check if there's already a window/tab open with the target URL
          const existingClient = clientList.find((client) => client.url === notificationData.url && 'focus' in client);
          if (existingClient) {
            return existingClient.focus();
          }

          // If not, open a new window/tab
          // eslint-disable-next-line no-undef
          if (clients.openWindow) {
            // eslint-disable-next-line no-undef
            return clients.openWindow(notificationData.url);
          }
          return null;
        }),
      );
    }
  }

  // Utility function to convert VAPID key
  // eslint-disable-next-line class-methods-use-this
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; i += 1) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Utility function to convert ArrayBuffer to Base64
  // eslint-disable-next-line class-methods-use-this
  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i += 1) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  // Check subscription status
  async checkSubscription() {
    if (!this.registration) {
      return null;
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription();
      this.subscription = subscription;
      return subscription;
    } catch (error) {
      logger.error('Failed to check subscription:', error);
      return null;
    }
  }

  // Initialize push notifications
  async initialize() {
    if (!this.isSupported) {
      logger.warn('Push notifications are not supported');
      return false;
    }

    try {
      // Register service worker
      await this.registerServiceWorker();

      // Check existing subscription
      await this.checkSubscription();

      // Set up message listener for service worker
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'NOTIFICATION_CLICK') {
            this.handleNotificationClick(event.data);
          }
        });
      }

      return true;
    } catch (error) {
      logger.error('Failed to initialize push notifications:', error);
      return false;
    }
  }

  // Test notification
  async testNotification() {
    if (this.permission !== 'granted') {
      throw new Error('Push notification permission not granted');
    }

    this.showNotification('Test Notification', {
      body: 'This is a test notification from PayHold',
      icon: '/icon-192x192.png',
      data: {
        url: window.location.origin,
      },
    });
    return true;
  }
}

// Create singleton instance
const pushNotificationService = new PushNotificationService();

export default pushNotificationService;
