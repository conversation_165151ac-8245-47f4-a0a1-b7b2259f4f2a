import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowPathIcon,
  DevicePhoneMobileIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import pushNotificationService from '../../services/pushNotificationService';
import logger from '../helpers/logger';

function PushNotificationManager({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState('default');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [subscription, setSubscription] = useState(null);

  useEffect(() => {
    if (user) {
      initializePushNotifications();
    }
  }, [user]);

  const initializePushNotifications = async () => {
    try {
      setLoading(true);
      
      // Check if push notifications are supported
      const supported = pushNotificationService.isSupported();
      setIsSupported(supported);
      
      if (!supported) {
        logger.warn('Push notifications are not supported in this browser');
        return;
      }

      // Get current permission status
      const currentPermission = pushNotificationService.getPermission();
      setPermission(currentPermission);

      // Initialize the service
      await pushNotificationService.initialize();

      // Check subscription status
      const currentSubscription = await pushNotificationService.checkSubscription();
      setSubscription(currentSubscription);
      setIsSubscribed(!!currentSubscription);

    } catch (error) {
      logger.error('Failed to initialize push notifications:', error);
      toast.error('Failed to initialize push notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleEnablePushNotifications = async () => {
    try {
      setLoading(true);
      
      // Request permission and subscribe
      await pushNotificationService.requestPermission();
      
      // Update state
      setPermission('granted');
      setIsSubscribed(true);
      
      const newSubscription = await pushNotificationService.checkSubscription();
      setSubscription(newSubscription);
      
      toast.success('Push notifications enabled successfully!');
      
    } catch (error) {
      logger.error('Failed to enable push notifications:', error);
      
      if (error.message.includes('denied')) {
        toast.error('Push notifications were denied. Please enable them in your browser settings.');
      } else if (error.message.includes('dismissed')) {
        toast.warning('Push notification permission was dismissed. You can enable them later.');
      } else {
        toast.error('Failed to enable push notifications. Please try again.');
      }
      
      // Update permission state
      setPermission(pushNotificationService.getPermission());
    } finally {
      setLoading(false);
    }
  };

  const handleDisablePushNotifications = async () => {
    try {
      setLoading(true);
      
      await pushNotificationService.unsubscribe();
      
      setIsSubscribed(false);
      setSubscription(null);
      
      toast.success('Push notifications disabled');
      
    } catch (error) {
      logger.error('Failed to disable push notifications:', error);
      toast.error('Failed to disable push notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      setTesting(true);
      
      await pushNotificationService.testNotification();
      toast.success('Test notification sent!');
      
    } catch (error) {
      logger.error('Failed to send test notification:', error);
      toast.error('Failed to send test notification');
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = () => {
    if (loading) {
      return <ArrowPathIcon className="h-5 w-5 text-gray-400 animate-spin" />;
    }

    switch (permission) {
      case 'granted':
        return isSubscribed 
          ? <CheckCircleIcon className="h-5 w-5 text-green-500" />
          : <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'denied':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <BellIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    if (loading) {
      return 'Checking status...';
    }

    if (!isSupported) {
      return 'Not supported in this browser';
    }

    switch (permission) {
      case 'granted':
        return isSubscribed ? 'Enabled and active' : 'Permission granted but not subscribed';
      case 'denied':
        return 'Blocked by user';
      default:
        return 'Not enabled';
    }
  };

  const getStatusColor = () => {
    switch (permission) {
      case 'granted':
        return isSubscribed ? 'text-green-600' : 'text-yellow-600';
      case 'denied':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DevicePhoneMobileIcon className="h-6 w-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Push Notifications</h3>
            <p className="text-sm text-gray-600">
              Get instant notifications for important events
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-6">
        {/* Browser Support Check */}
        {!isSupported && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-yellow-800">
                  Browser Not Supported
                </h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Your browser doesn't support push notifications. Please use a modern browser like Chrome, Firefox, or Safari.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Permission Denied */}
        {isSupported && permission === 'denied' && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XCircleIcon className="h-5 w-5 text-red-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-red-800">
                  Notifications Blocked
                </h4>
                <p className="text-sm text-red-700 mt-1">
                  Push notifications are blocked for this site. To enable them:
                </p>
                <ol className="text-sm text-red-700 mt-2 list-decimal list-inside space-y-1">
                  <li>Click the lock icon in your browser's address bar</li>
                  <li>Change notifications from "Block" to "Allow"</li>
                  <li>Refresh this page</li>
                </ol>
              </div>
            </div>
          </div>
        )}

        {/* Enable/Disable Controls */}
        {isSupported && permission !== 'denied' && (
          <div className="space-y-4">
            {!isSubscribed ? (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex items-start">
                  <BellIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div className="ml-3 flex-1">
                    <h4 className="text-sm font-medium text-blue-800">
                      Enable Push Notifications
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Stay updated with real-time notifications for:
                    </p>
                    <ul className="text-sm text-blue-700 mt-2 list-disc list-inside space-y-1">
                      <li>Transaction updates and payment confirmations</li>
                      <li>New messages from buyers and sellers</li>
                      <li>Order status changes and delivery updates</li>
                      <li>Important security alerts</li>
                    </ul>
                    
                    <button
                      onClick={handleEnablePushNotifications}
                      disabled={loading}
                      className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? (
                        <>
                          <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                          Enabling...
                        </>
                      ) : (
                        <>
                          <BellIcon className="h-4 w-4 mr-2" />
                          Enable Notifications
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-400 mt-0.5" />
                  <div className="ml-3 flex-1">
                    <h4 className="text-sm font-medium text-green-800">
                      Push Notifications Enabled
                    </h4>
                    <p className="text-sm text-green-700 mt-1">
                      You'll receive push notifications for important events.
                    </p>
                    
                    <div className="mt-4 flex items-center space-x-3">
                      <button
                        onClick={handleTestNotification}
                        disabled={testing}
                        className="inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {testing ? (
                          <>
                            <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          'Test Notification'
                        )}
                      </button>
                      
                      <button
                        onClick={handleDisablePushNotifications}
                        disabled={loading}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <>
                            <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                            Disabling...
                          </>
                        ) : (
                          'Disable'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Subscription Details (for debugging) */}
        {subscription && process.env.NODE_ENV === 'development' && (
          <details className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <summary className="text-sm font-medium text-gray-700 cursor-pointer">
              Subscription Details (Debug)
            </summary>
            <pre className="text-xs text-gray-600 mt-2 overflow-x-auto">
              {JSON.stringify(subscription, null, 2)}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

export default PushNotificationManager;
