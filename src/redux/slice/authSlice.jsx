import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { authAPI } from '../../services/apiServices';

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials);
      const { user, token } = response.data;

      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));

      toast.success('Login successful!');
      return { user, token };
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Login failed');
    }
  },
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData);
      const { user, token } = response.data;

      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));

      toast.success('Registration successful!');
      return { user, token };
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Registration failed');
    }
  },
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout();
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      toast.success('Logged out successfully!');
      return { success: true };
    } catch (error) {
      // Even if API call fails, clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      return rejectWithValue(error.response.data);
    }
  },
);

const initialState = {
  user: JSON.parse(localStorage.getItem('user')) || null,
  token: localStorage.getItem('authToken') || null,
  isLoading: false,
  error: null,
  isAuthenticated: !!localStorage.getItem('authToken'),
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => ({
      ...state,
      error: null,
    }),
    updateUser: (state, action) => ({
      ...state,
      user: { ...state.user, ...action.payload },
      isAuthenticated: true,
      error: null,
    //   localStorage.setItem('user', JSON.stringify(state.user));
    }),
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => ({
        ...state,
        isLoading: true,
        error: null,
      }))
      .addCase(loginUser.fulfilled, (state, action) => ({
        ...state,
        isLoading: false,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        error: null,
      }))
      .addCase(loginUser.rejected, (state, action) => ({
        ...state,
        isLoading: false,
        error: action.payload,
        isAuthenticated: false,
      }));

    // Register
    builder
      .addCase(registerUser.pending, (state) => ({
        ...state,
        isLoading: true,
        error: null,
      }))
      .addCase(registerUser.fulfilled, (state, action) => ({
        ...state,
        isLoading: false,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        error: null,
      }))
      .addCase(registerUser.rejected, (state, action) => ({
        ...state,
        isLoading: false,
        error: action.payload,
        isAuthenticated: false,
      }));
    // Logout
    builder
      .addCase(logoutUser.fulfilled, (state) => ({
        ...state,
        isLoading: false,
        user: null,
        token: null,
        isAuthenticated: false,
        error: null,
      }));
  },
});

export const { clearError, updateUser } = authSlice.actions;
export default authSlice.reducer;
