import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  MinusIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import ChatWindow from './ChatWindow';
import ConversationList from './ConversationList';
import { chatAPI } from '../../services/apiServices';
import { useSocket } from '../../hooks/useSocket';
import logger from '../helpers/logger';

function ChatManager({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const socket = useSocket();
  
  const [isOpen, setIsOpen] = useState(false);
  const [activeChats, setActiveChats] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [showConversationList, setShowConversationList] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [minimizedChats, setMinimizedChats] = useState(new Set());

  useEffect(() => {
    if (user) {
      fetchUnreadCount();
    }
  }, [user]);

  useEffect(() => {
    if (socket) {
      socket.on('new_message', handleNewMessage);
      socket.on('conversation_updated', handleConversationUpdated);

      return () => {
        socket.off('new_message', handleNewMessage);
        socket.off('conversation_updated', handleConversationUpdated);
      };
    }
  }, [socket]);

  const fetchUnreadCount = async () => {
    try {
      const response = await chatAPI.getUnreadCount();
      if (response.data.success) {
        setUnreadCount(response.data.count);
      }
    } catch (error) {
      logger.error('Failed to fetch unread count:', error);
    }
  };

  const handleNewMessage = (data) => {
    // Update unread count if message is not from current user
    if (data.message.sender_id !== user.id) {
      setUnreadCount(prev => prev + 1);
      
      // Show notification if chat is not open or conversation is not active
      const isConversationActive = activeChats.some(chat => chat.id === data.conversation_id);
      if (!isOpen || !isConversationActive) {
        toast.info(`New message from ${data.sender_name}`, {
          onClick: () => {
            openConversation(data.conversation);
          }
        });
      }
    }
  };

  const handleConversationUpdated = (data) => {
    // Update active chats if conversation is open
    setActiveChats(prev => prev.map(chat => 
      chat.id === data.conversation.id ? data.conversation : chat
    ));
  };

  const openConversation = async (conversation) => {
    try {
      // Check if conversation is already open
      const existingChat = activeChats.find(chat => chat.id === conversation.id);
      if (existingChat) {
        // Bring to front and unminimize
        setMinimizedChats(prev => {
          const newSet = new Set(prev);
          newSet.delete(conversation.id);
          return newSet;
        });
        return;
      }

      // Fetch full conversation details if needed
      let fullConversation = conversation;
      if (!conversation.participants) {
        const response = await chatAPI.getConversation(conversation.id);
        if (response.data.success) {
          fullConversation = response.data.conversation;
        }
      }

      // Add to active chats (limit to 3 open chats)
      setActiveChats(prev => {
        const updated = [fullConversation, ...prev.slice(0, 2)];
        return updated;
      });

      setSelectedConversation(fullConversation);
      setIsOpen(true);
      
      // Mark as read
      await chatAPI.markAsRead(conversation.id);
      setUnreadCount(prev => Math.max(0, prev - (conversation.unread_count || 0)));
      
    } catch (error) {
      logger.error('Failed to open conversation:', error);
      toast.error('Failed to open conversation');
    }
  };

  const closeConversation = (conversationId) => {
    setActiveChats(prev => prev.filter(chat => chat.id !== conversationId));
    setMinimizedChats(prev => {
      const newSet = new Set(prev);
      newSet.delete(conversationId);
      return newSet;
    });
    
    if (selectedConversation?.id === conversationId) {
      setSelectedConversation(null);
    }
  };

  const minimizeConversation = (conversationId) => {
    setMinimizedChats(prev => {
      const newSet = new Set(prev);
      if (newSet.has(conversationId)) {
        newSet.delete(conversationId);
      } else {
        newSet.add(conversationId);
      }
      return newSet;
    });
  };

  const startNewConversation = async (userId, transactionId = null) => {
    try {
      const response = await chatAPI.createConversation({
        participant_id: userId,
        transaction_id: transactionId
      });
      
      if (response.data.success) {
        const conversation = response.data.conversation;
        await openConversation(conversation);
      }
    } catch (error) {
      logger.error('Failed to start conversation:', error);
      toast.error('Failed to start conversation');
    }
  };

  const toggleChatManager = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setShowConversationList(true);
    }
  };

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <>
      {/* Chat Toggle Button */}
      <div className="fixed bottom-4 left-4 z-40">
        <button
          onClick={toggleChatManager}
          className="relative p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors"
          title="Open chat"
        >
          <ChatBubbleLeftRightIcon className="h-6 w-6" />
          {unreadCount > 0 && (
            <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </button>
      </div>

      {/* Chat Manager Panel */}
      {isOpen && (
        <div className="fixed bottom-20 left-4 w-80 h-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-blue-600 text-white rounded-t-lg">
            <h2 className="text-lg font-semibold">Chat</h2>
            <div className="flex items-center space-x-2">
              {activeChats.length > 0 && (
                <button
                  onClick={() => setShowConversationList(!showConversationList)}
                  className="text-white hover:text-gray-200"
                  title={showConversationList ? 'Hide conversations' : 'Show conversations'}
                >
                  {showConversationList ? <MinusIcon className="h-4 w-4" /> : <PlusIcon className="h-4 w-4" />}
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-gray-200"
                title="Close chat"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            {showConversationList ? (
              <ConversationList
                onConversationSelect={openConversation}
                selectedConversationId={selectedConversation?.id}
                className="h-full border-0 rounded-none shadow-none"
              />
            ) : selectedConversation ? (
              <div className="h-full p-4">
                <div className="text-center text-gray-500 text-sm">
                  Chat moved to separate window
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-sm">Select a conversation to start chatting</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Active Chat Windows */}
      {activeChats.map((conversation, index) => (
        <ChatWindow
          key={conversation.id}
          conversation={conversation}
          onClose={() => closeConversation(conversation.id)}
          onMinimize={() => minimizeConversation(conversation.id)}
          isMinimized={minimizedChats.has(conversation.id)}
          className={`z-30`}
          style={{
            right: `${(index * 320) + 16}px`,
            bottom: 0
          }}
        />
      ))}
    </>
  );
}

// Export function to start conversation from other components
export const startConversation = async (userId, transactionId = null) => {
  try {
    const response = await chatAPI.createConversation({
      participant_id: userId,
      transaction_id: transactionId
    });
    
    if (response.data.success) {
      // Trigger chat manager to open this conversation
      window.dispatchEvent(new CustomEvent('openConversation', {
        detail: { conversation: response.data.conversation }
      }));
    }
  } catch (error) {
    logger.error('Failed to start conversation:', error);
    toast.error('Failed to start conversation');
  }
};

export default ChatManager;
