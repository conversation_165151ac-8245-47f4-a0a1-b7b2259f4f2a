import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Loading states
  globalLoading: false,
  pageLoading: false,
  
  // Modal states
  modals: {
    productModal: {
      isOpen: false,
      productId: null,
      mode: 'view' // 'view', 'edit', 'create'
    },
    confirmModal: {
      isOpen: false,
      title: '',
      message: '',
      onConfirm: null,
      onCancel: null,
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      type: 'default' // 'default', 'danger', 'warning'
    },
    imageModal: {
      isOpen: false,
      images: [],
      currentIndex: 0
    },
    chatModal: {
      isOpen: false,
      conversationId: null,
      userId: null
    }
  },
  
  // Sidebar and navigation
  sidebar: {
    isOpen: false,
    activeItem: null
  },
  
  // Notifications
  notifications: [],
  
  // Toast messages
  toasts: [],
  
  // Search
  search: {
    isOpen: false,
    query: '',
    results: [],
    isSearching: false
  },
  
  // Filters
  filters: {
    isOpen: false,
    activeFilters: {}
  },
  
  // Theme and display
  theme: 'light', // 'light', 'dark', 'auto'
  
  // Mobile responsiveness
  isMobile: false,
  screenSize: 'desktop', // 'mobile', 'tablet', 'desktop'
  
  // Page-specific UI state
  pageState: {
    products: {
      viewMode: 'grid', // 'grid', 'list'
      sortBy: 'newest',
      showFilters: false
    },
    dashboard: {
      activeTab: 'overview',
      showSidebar: true
    },
    chat: {
      showUserList: true,
      selectedConversation: null
    }
  },
  
  // Error handling
  errors: [],
  
  // Connection status
  isOnline: true,
  
  // Performance monitoring
  performance: {
    pageLoadTime: 0,
    apiResponseTimes: {}
  }
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Global loading
    setGlobalLoading: (state, action) => {
      state.globalLoading = action.payload;
    },
    
    setPageLoading: (state, action) => {
      state.pageLoading = action.payload;
    },
    
    // Modal management
    openModal: (state, action) => {
      const { modalName, data = {} } = action.payload;
      if (state.modals[modalName]) {
        state.modals[modalName] = {
          ...state.modals[modalName],
          isOpen: true,
          ...data
        };
      }
    },
    
    closeModal: (state, action) => {
      const modalName = action.payload;
      if (state.modals[modalName]) {
        state.modals[modalName] = {
          ...initialState.modals[modalName],
          isOpen: false
        };
      }
    },
    
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalName => {
        state.modals[modalName] = {
          ...initialState.modals[modalName],
          isOpen: false
        };
      });
    },
    
    // Confirm modal
    showConfirmModal: (state, action) => {
      const {
        title,
        message,
        onConfirm,
        onCancel,
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        type = 'default'
      } = action.payload;
      
      state.modals.confirmModal = {
        isOpen: true,
        title,
        message,
        onConfirm,
        onCancel,
        confirmText,
        cancelText,
        type
      };
    },
    
    // Sidebar
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
    
    setSidebarOpen: (state, action) => {
      state.sidebar.isOpen = action.payload;
    },
    
    setActiveSidebarItem: (state, action) => {
      state.sidebar.activeItem = action.payload;
    },
    
    // Notifications
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        read: false,
        ...action.payload
      };
      state.notifications.unshift(notification);
    },
    
    markNotificationAsRead: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.read = true;
      }
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    removeNotification: (state, action) => {
      const notificationId = action.payload;
      state.notifications = state.notifications.filter(n => n.id !== notificationId);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Toast messages
    addToast: (state, action) => {
      const toast = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload
      };
      state.toasts.push(toast);
    },
    
    removeToast: (state, action) => {
      const toastId = action.payload;
      state.toasts = state.toasts.filter(t => t.id !== toastId);
    },
    
    clearToasts: (state) => {
      state.toasts = [];
    },
    
    // Search
    toggleSearch: (state) => {
      state.search.isOpen = !state.search.isOpen;
      if (!state.search.isOpen) {
        state.search.query = '';
        state.search.results = [];
      }
    },
    
    setSearchQuery: (state, action) => {
      state.search.query = action.payload;
    },
    
    setSearchResults: (state, action) => {
      state.search.results = action.payload;
    },
    
    setSearching: (state, action) => {
      state.search.isSearching = action.payload;
    },
    
    // Filters
    toggleFilters: (state) => {
      state.filters.isOpen = !state.filters.isOpen;
    },
    
    setFiltersOpen: (state, action) => {
      state.filters.isOpen = action.payload;
    },
    
    setActiveFilters: (state, action) => {
      state.filters.activeFilters = { ...state.filters.activeFilters, ...action.payload };
    },
    
    clearActiveFilters: (state) => {
      state.filters.activeFilters = {};
    },
    
    // Theme
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    
    // Screen size
    setScreenSize: (state, action) => {
      const size = action.payload;
      state.screenSize = size;
      state.isMobile = size === 'mobile';
      
      // Auto-close sidebar on mobile
      if (state.isMobile) {
        state.sidebar.isOpen = false;
      }
    },
    
    // Page-specific state
    setPageState: (state, action) => {
      const { page, data } = action.payload;
      if (state.pageState[page]) {
        state.pageState[page] = { ...state.pageState[page], ...data };
      }
    },
    
    // Products page
    setProductsViewMode: (state, action) => {
      state.pageState.products.viewMode = action.payload;
    },
    
    setProductsSortBy: (state, action) => {
      state.pageState.products.sortBy = action.payload;
    },
    
    toggleProductsFilters: (state) => {
      state.pageState.products.showFilters = !state.pageState.products.showFilters;
    },
    
    // Dashboard page
    setDashboardTab: (state, action) => {
      state.pageState.dashboard.activeTab = action.payload;
    },
    
    toggleDashboardSidebar: (state) => {
      state.pageState.dashboard.showSidebar = !state.pageState.dashboard.showSidebar;
    },
    
    // Chat page
    toggleChatUserList: (state) => {
      state.pageState.chat.showUserList = !state.pageState.chat.showUserList;
    },
    
    setSelectedConversation: (state, action) => {
      state.pageState.chat.selectedConversation = action.payload;
    },
    
    // Error handling
    addError: (state, action) => {
      const error = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload
      };
      state.errors.push(error);
    },
    
    removeError: (state, action) => {
      const errorId = action.payload;
      state.errors = state.errors.filter(e => e.id !== errorId);
    },
    
    clearErrors: (state) => {
      state.errors = [];
    },
    
    // Connection status
    setOnlineStatus: (state, action) => {
      state.isOnline = action.payload;
    },
    
    // Performance monitoring
    setPageLoadTime: (state, action) => {
      state.performance.pageLoadTime = action.payload;
    },
    
    setApiResponseTime: (state, action) => {
      const { endpoint, time } = action.payload;
      state.performance.apiResponseTimes[endpoint] = time;
    },
    
    // Bulk actions
    resetPageState: (state, action) => {
      const page = action.payload;
      if (state.pageState[page]) {
        state.pageState[page] = initialState.pageState[page];
      }
    },
    
    resetUIState: (state) => {
      return {
        ...initialState,
        theme: state.theme, // Preserve theme
        screenSize: state.screenSize, // Preserve screen size
        isMobile: state.isMobile
      };
    }
  }
});

export const {
  setGlobalLoading,
  setPageLoading,
  openModal,
  closeModal,
  closeAllModals,
  showConfirmModal,
  toggleSidebar,
  setSidebarOpen,
  setActiveSidebarItem,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearNotifications,
  addToast,
  removeToast,
  clearToasts,
  toggleSearch,
  setSearchQuery,
  setSearchResults,
  setSearching,
  toggleFilters,
  setFiltersOpen,
  setActiveFilters,
  clearActiveFilters,
  setTheme,
  toggleTheme,
  setScreenSize,
  setPageState,
  setProductsViewMode,
  setProductsSortBy,
  toggleProductsFilters,
  setDashboardTab,
  toggleDashboardSidebar,
  toggleChatUserList,
  setSelectedConversation,
  addError,
  removeError,
  clearErrors,
  setOnlineStatus,
  setPageLoadTime,
  setApiResponseTime,
  resetPageState,
  resetUIState
} = uiSlice.actions;

export default uiSlice.reducer;
