import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import {
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentArrowDownIcon,
  PaperClipIcon,
  UserIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { disputeAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import logger from '../helpers/logger';

function DisputeDetails({ disputeId, onUpdate, className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission } = useRoleAccess();
  
  const [dispute, setDispute] = useState(null);
  const [loading, setLoading] = useState(false);
  const [responding, setResponding] = useState(false);
  const [showResponseForm, setShowResponseForm] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm();

  useEffect(() => {
    if (disputeId) {
      fetchDispute();
    }
  }, [disputeId]);

  const fetchDispute = async () => {
    try {
      setLoading(true);
      const response = await disputeAPI.getDispute(disputeId);
      
      if (response.data.success) {
        setDispute(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to fetch dispute:', error);
      toast.error('Failed to load dispute details');
    } finally {
      setLoading(false);
    }
  };

  const handleResponse = async (data) => {
    try {
      setResponding(true);
      
      const response = await disputeAPI.respondToDispute(disputeId, {
        message: data.message,
        response_type: data.response_type || 'response'
      });
      
      if (response.data.success) {
        toast.success('Response submitted successfully');
        setShowResponseForm(false);
        reset();
        fetchDispute();
        onUpdate?.(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to submit response:', error);
      toast.error('Failed to submit response');
    } finally {
      setResponding(false);
    }
  };

  const handleModeratorAction = async (action, resolution = null) => {
    try {
      const response = await disputeAPI.moderateDispute(disputeId, {
        action,
        resolution
      });
      
      if (response.data.success) {
        toast.success(`Dispute ${action} successfully`);
        fetchDispute();
        onUpdate?.(response.data.dispute);
      }
    } catch (error) {
      logger.error(`Failed to ${action} dispute:`, error);
      toast.error(`Failed to ${action} dispute`);
    }
  };

  const downloadAttachment = async (attachmentId, filename) => {
    try {
      const response = await disputeAPI.downloadAttachment(attachmentId);
      
      // Create blob and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      logger.error('Failed to download attachment:', error);
      toast.error('Failed to download attachment');
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'open':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="h-3 w-3 mr-1" />
            Open
          </span>
        );
      case 'in_review':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <ClockIcon className="h-3 w-3 mr-1" />
            In Review
          </span>
        );
      case 'resolved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Resolved
          </span>
        );
      case 'closed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <XCircleIcon className="h-3 w-3 mr-1" />
            Closed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Unknown
          </span>
        );
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canRespond = () => {
    if (!dispute) return false;
    
    // User can respond if they're involved in the dispute and it's open
    const isInvolved = dispute.created_by === user.id || 
                      dispute.transaction?.buyer_id === user.id || 
                      dispute.transaction?.seller_id === user.id;
    
    return isInvolved && ['open', 'in_review'].includes(dispute.status);
  };

  const canModerate = () => {
    return hasPermission('dispute.moderate');
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!dispute) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Dispute not found</h3>
        <p className="text-gray-500">The requested dispute could not be found.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Dispute #{dispute.id}</h2>
              <p className="text-gray-600 mt-1">
                Created on {formatDate(dispute.created_at)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {getStatusBadge(dispute.status)}
          </div>
        </div>
      </div>

      {/* Dispute Information */}
      <div className="p-6 border-b border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Info */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Dispute Details</h3>
            
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Reason:</span>
                <p className="text-sm text-gray-900 mt-1 capitalize">
                  {dispute.reason.replace(/_/g, ' ')}
                </p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Requested Action:</span>
                <p className="text-sm text-gray-900 mt-1 capitalize">
                  {dispute.requested_action.replace(/_/g, ' ')}
                </p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Description:</span>
                <p className="text-sm text-gray-900 mt-1">{dispute.description}</p>
              </div>
              
              {dispute.evidence_description && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Evidence Description:</span>
                  <p className="text-sm text-gray-900 mt-1">{dispute.evidence_description}</p>
                </div>
              )}
            </div>
          </div>

          {/* Transaction Info */}
          {dispute.transaction && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Transaction Details</h3>
              
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Transaction ID:</span>
                  <p className="text-sm text-gray-900 mt-1">{dispute.transaction.id}</p>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">Amount:</span>
                  <p className="text-sm text-gray-900 mt-1">GH₵{dispute.transaction.amount}</p>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">Date:</span>
                  <p className="text-sm text-gray-900 mt-1">
                    {formatDate(dispute.transaction.created_at)}
                  </p>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">Status:</span>
                  <p className="text-sm text-gray-900 mt-1 capitalize">
                    {dispute.transaction.status}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Attachments */}
      {dispute.attachments && dispute.attachments.length > 0 && (
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Evidence Files</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {dispute.attachments.map((attachment, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center space-x-3">
                  <PaperClipIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{attachment.filename}</p>
                    <p className="text-xs text-gray-500">
                      {(attachment.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={() => downloadAttachment(attachment.id, attachment.filename)}
                  className="text-blue-600 hover:text-blue-800"
                  title="Download"
                >
                  <DocumentArrowDownIcon className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Responses/Messages */}
      {dispute.responses && dispute.responses.length > 0 && (
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Responses</h3>
          
          <div className="space-y-4">
            {dispute.responses.map((response, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {response.user?.avatar ? (
                      <img
                        src={response.user.avatar}
                        alt={response.user.name}
                        className="h-8 w-8 rounded-full"
                      />
                    ) : (
                      <UserIcon className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-gray-900">
                        {response.user?.name || 'Unknown User'}
                      </span>
                      
                      {response.user?.role === 'moderator' || response.user?.role === 'admin' && (
                        <ShieldCheckIcon className="h-4 w-4 text-blue-500" title="Moderator" />
                      )}
                      
                      <span className="text-xs text-gray-500">
                        {formatDate(response.created_at)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-700">{response.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Response Form */}
      {canRespond() && (
        <div className="p-6 border-b border-gray-200">
          {!showResponseForm ? (
            <button
              onClick={() => setShowResponseForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
              Add Response
            </button>
          ) : (
            <form onSubmit={handleSubmit(handleResponse)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Response
                </label>
                <textarea
                  {...register('message', { 
                    required: 'Please enter your response',
                    minLength: {
                      value: 10,
                      message: 'Response must be at least 10 characters'
                    }
                  })}
                  rows={4}
                  placeholder="Enter your response to this dispute..."
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                {errors.message && (
                  <p className="text-red-600 text-sm mt-1">{errors.message.message}</p>
                )}
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  type="submit"
                  disabled={responding}
                  className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {responding ? 'Submitting...' : 'Submit Response'}
                </button>
                
                <button
                  type="button"
                  onClick={() => {
                    setShowResponseForm(false);
                    reset();
                  }}
                  className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </form>
          )}
        </div>
      )}

      {/* Moderator Actions */}
      {canModerate() && dispute.status !== 'resolved' && dispute.status !== 'closed' && (
        <div className="p-6 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Moderator Actions</h3>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handleModeratorAction('resolve', 'favor_buyer')}
              className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"
            >
              Resolve - Favor Buyer
            </button>
            
            <button
              onClick={() => handleModeratorAction('resolve', 'favor_seller')}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              Resolve - Favor Seller
            </button>
            
            <button
              onClick={() => handleModeratorAction('close')}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Close Dispute
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default DisputeDetails;
