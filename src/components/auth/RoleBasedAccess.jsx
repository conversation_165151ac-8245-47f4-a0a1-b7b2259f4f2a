import React from 'react';
import { useSelector } from 'react-redux';

// Role hierarchy (higher number = more permissions)
const ROLE_HIERARCHY = {
  'guest': 0,
  'buyer': 1,
  'seller': 2,
  'premium_seller': 3,
  'moderator': 4,
  'admin': 5,
  'super_admin': 6
};

// Permission definitions
const PERMISSIONS = {
  // User permissions
  'user.view_profile': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'user.edit_profile': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'user.delete_account': ['buyer', 'seller', 'premium_seller'],
  
  // Product permissions
  'product.view': ['guest', 'buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'product.create': ['seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'product.edit': ['seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'product.delete': ['seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'product.feature': ['premium_seller', 'moderator', 'admin', 'super_admin'],
  'product.moderate': ['moderator', 'admin', 'super_admin'],
  
  // Transaction permissions
  'transaction.create': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'transaction.view_own': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'transaction.view_all': ['moderator', 'admin', 'super_admin'],
  'transaction.moderate': ['moderator', 'admin', 'super_admin'],
  'transaction.refund': ['moderator', 'admin', 'super_admin'],
  
  // Chat permissions
  'chat.send_message': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'chat.view_conversations': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'chat.moderate': ['moderator', 'admin', 'super_admin'],
  
  // Payment permissions
  'payment.process': ['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'payment.refund': ['seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'payment.view_analytics': ['premium_seller', 'moderator', 'admin', 'super_admin'],
  
  // Dispute permissions
  'dispute.create': ['buyer', 'seller', 'premium_seller'],
  'dispute.respond': ['buyer', 'seller', 'premium_seller'],
  'dispute.moderate': ['moderator', 'admin', 'super_admin'],
  'dispute.resolve': ['moderator', 'admin', 'super_admin'],
  
  // Admin permissions
  'admin.view_dashboard': ['moderator', 'admin', 'super_admin'],
  'admin.manage_users': ['admin', 'super_admin'],
  'admin.manage_roles': ['super_admin'],
  'admin.view_reports': ['moderator', 'admin', 'super_admin'],
  'admin.system_settings': ['super_admin'],
  
  // Notification permissions
  'notification.send': ['moderator', 'admin', 'super_admin'],
  'notification.broadcast': ['admin', 'super_admin'],
  
  // Analytics permissions
  'analytics.view_own': ['seller', 'premium_seller', 'moderator', 'admin', 'super_admin'],
  'analytics.view_all': ['admin', 'super_admin'],
  
  // Feature permissions
  'feature.bulk_operations': ['premium_seller', 'moderator', 'admin', 'super_admin'],
  'feature.advanced_search': ['premium_seller', 'moderator', 'admin', 'super_admin'],
  'feature.priority_support': ['premium_seller', 'moderator', 'admin', 'super_admin'],
  'feature.custom_branding': ['premium_seller'],
  'feature.api_access': ['premium_seller', 'admin', 'super_admin']
};

// Role-based access control hook
export const useRoleAccess = () => {
  const { user } = useSelector((state) => state.auth);
  
  const hasRole = (requiredRole) => {
    if (!user || !user.role) return false;
    
    const userRoleLevel = ROLE_HIERARCHY[user.role] || 0;
    const requiredRoleLevel = ROLE_HIERARCHY[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  };
  
  const hasPermission = (permission) => {
    if (!user || !user.role) return false;
    
    const allowedRoles = PERMISSIONS[permission] || [];
    return allowedRoles.includes(user.role);
  };
  
  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };
  
  const hasAllPermissions = (permissions) => {
    return permissions.every(permission => hasPermission(permission));
  };
  
  const canAccessResource = (resource, action = 'view') => {
    const permission = `${resource}.${action}`;
    return hasPermission(permission);
  };
  
  const getRoleLevel = () => {
    return ROLE_HIERARCHY[user?.role] || 0;
  };
  
  const getRoleName = () => {
    return user?.role || 'guest';
  };
  
  const isGuest = () => !user || user.role === 'guest';
  const isBuyer = () => hasRole('buyer');
  const isSeller = () => hasRole('seller');
  const isPremiumSeller = () => user?.role === 'premium_seller';
  const isModerator = () => hasRole('moderator');
  const isAdmin = () => hasRole('admin');
  const isSuperAdmin = () => user?.role === 'super_admin';
  
  return {
    user,
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessResource,
    getRoleLevel,
    getRoleName,
    isGuest,
    isBuyer,
    isSeller,
    isPremiumSeller,
    isModerator,
    isAdmin,
    isSuperAdmin
  };
};

// Role-based access component
function RoleBasedAccess({ 
  children, 
  roles = [], 
  permissions = [], 
  requireAll = false,
  fallback = null,
  showFallback = true 
}) {
  const { hasRole, hasPermission, hasAnyPermission, hasAllPermissions } = useRoleAccess();
  
  // Check role access
  const hasRoleAccess = roles.length === 0 || roles.some(role => hasRole(role));
  
  // Check permission access
  let hasPermissionAccess = true;
  if (permissions.length > 0) {
    hasPermissionAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }
  
  // Grant access if both role and permission checks pass
  const hasAccess = hasRoleAccess && hasPermissionAccess;
  
  if (hasAccess) {
    return <>{children}</>;
  }
  
  if (showFallback && fallback) {
    return <>{fallback}</>;
  }
  
  return null;
}

// Higher-order component for role-based access
export const withRoleAccess = (WrappedComponent, accessConfig = {}) => {
  return function RoleAccessWrapper(props) {
    const {
      roles = [],
      permissions = [],
      requireAll = false,
      fallback = null,
      showFallback = true
    } = accessConfig;
    
    return (
      <RoleBasedAccess
        roles={roles}
        permissions={permissions}
        requireAll={requireAll}
        fallback={fallback}
        showFallback={showFallback}
      >
        <WrappedComponent {...props} />
      </RoleBasedAccess>
    );
  };
};

// Specific role components for convenience
export const AdminOnly = ({ children, fallback = null }) => (
  <RoleBasedAccess roles={['admin', 'super_admin']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const ModeratorOnly = ({ children, fallback = null }) => (
  <RoleBasedAccess roles={['moderator', 'admin', 'super_admin']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const SellerOnly = ({ children, fallback = null }) => (
  <RoleBasedAccess roles={['seller', 'premium_seller', 'moderator', 'admin', 'super_admin']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const PremiumOnly = ({ children, fallback = null }) => (
  <RoleBasedAccess roles={['premium_seller', 'moderator', 'admin', 'super_admin']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const AuthenticatedOnly = ({ children, fallback = null }) => (
  <RoleBasedAccess roles={['buyer', 'seller', 'premium_seller', 'moderator', 'admin', 'super_admin']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

// Permission-based components
export const CanCreateProduct = ({ children, fallback = null }) => (
  <RoleBasedAccess permissions={['product.create']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const CanModerate = ({ children, fallback = null }) => (
  <RoleBasedAccess permissions={['transaction.moderate', 'dispute.moderate']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

export const CanViewAnalytics = ({ children, fallback = null }) => (
  <RoleBasedAccess permissions={['analytics.view_own', 'analytics.view_all']} fallback={fallback}>
    {children}
  </RoleBasedAccess>
);

// Role badge component
export const RoleBadge = ({ role, className = '' }) => {
  const getRoleColor = (role) => {
    switch (role) {
      case 'super_admin':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'moderator':
        return 'bg-orange-100 text-orange-800';
      case 'premium_seller':
        return 'bg-yellow-100 text-yellow-800';
      case 'seller':
        return 'bg-green-100 text-green-800';
      case 'buyer':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getRoleLabel = (role) => {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'premium_seller':
        return 'Premium Seller';
      default:
        return role.charAt(0).toUpperCase() + role.slice(1);
    }
  };
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(role)} ${className}`}>
      {getRoleLabel(role)}
    </span>
  );
};

export default RoleBasedAccess;
