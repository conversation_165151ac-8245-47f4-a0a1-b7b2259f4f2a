import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import {
  HeartIcon,
  ShareIcon,
  StarIcon,
  ShieldCheckIcon,
  TruckIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { productAPI } from '../services/apiServices';
import { addToCart } from '../redux/slice/cartSlice';
import useAuth from '../hooks/useAuth';
import Button from '../components/helpers/Button';
import LoadingSpinner from '../components/helpers/LoadingSpinner';

function ProductDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuthenticated } = useAuth();

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [addingToCart, setAddingToCart] = useState(false);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await productAPI.getProduct(id);

      if (response.data.success) {
        setProduct(response.data.product);
      } else {
        throw new Error('Product not found');
      }
    } catch (err) {
      setError(err.message || 'Failed to load product');
      toast.error('Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      toast.info('Please login to add items to cart');
      navigate('/login');
      return;
    }

    try {
      setAddingToCart(true);
      dispatch(addToCart({ product, quantity }));
      toast.success('Product added to cart!');
    } catch (error) {
      toast.error('Failed to add product to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  const handleBuyNow = () => {
    if (!isAuthenticated) {
      toast.info('Please login to make a purchase');
      navigate('/login');
      return;
    }

    // Add to cart and redirect to checkout
    dispatch(addToCart({ product, quantity }));
    navigate('/checkout');
  };

  const handleContactSeller = () => {
    if (!isAuthenticated) {
      toast.info('Please login to contact seller');
      navigate('/login');
      return;
    }

    // Navigate to chat with seller
    navigate(`/chat/seller/${product.seller.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h2>
          <p className="text-gray-600 mb-8">{error || 'The product you are looking for does not exist.'}</p>
          <Button onClick={() => navigate('/products')}>
            Browse Products
          </Button>
        </div>
      </div>
    );
  }

  const totalPrice = product.price * quantity;
  const escrowFee = totalPrice * 0.05; // 5% escrow fee
  const finalPrice = totalPrice + escrowFee;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Product Images */}
            <div>
              <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden mb-4">
                <img
                  src={product.images?.[selectedImage]?.url || '/placeholder-product.jpg'}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {product.images && product.images.length > 1 && (
                <div className="grid grid-cols-4 gap-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImage(index)}
                      className={`aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden ${
                        selectedImage === index ? 'ring-2 ring-blue-500' : ''
                      }`}
                    >
                      <img
                        src={image.url}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {product.name}
                  </h1>
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(product.rating || 0)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      (
                      {product.reviews_count || 0}
                      {' '}
                      reviews)
                    </span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => setIsFavorite(!isFavorite)}
                    className="p-2 text-gray-400 hover:text-red-500"
                  >
                    {isFavorite ? (
                      <HeartSolidIcon className="h-6 w-6 text-red-500" />
                    ) : (
                      <HeartIcon className="h-6 w-6" />
                    )}
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600">
                    <ShareIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Price */}
              <div className="mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  GH₵
                  {' '}
                  {product.price.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">
                  + GH₵
                  {' '}
                  {escrowFee.toFixed(2)}
                  {' '}
                  escrow fee (5%)
                </div>
              </div>

              {/* Seller Info */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={product.seller?.avatar || '/default-avatar.jpg'}
                      alt={product.seller?.name}
                      className="h-10 w-10 rounded-full"
                    />
                    <div>
                      <p className="font-medium text-gray-900">
                        {product.seller?.name || 'Unknown Seller'}
                      </p>
                      <div className="flex items-center space-x-1">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">
                          {product.seller?.rating || 'No rating'}
                          (
                          {product.seller?.total_sales || 0}
                          {' '}
                          sales)
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleContactSeller}
                    className="flex items-center space-x-1"
                  >
                    <ChatBubbleLeftRightIcon className="h-4 w-4" />
                    <span>Contact</span>
                  </Button>
                </div>
              </div>

              {/* Quantity Selector */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border border-gray-300 rounded-md min-w-[60px] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    +
                  </button>
                  <span className="text-sm text-gray-600 ml-4">
                    {product.stock_quantity}
                    {' '}
                    available
                  </span>
                </div>
              </div>

              {/* Total Price */}
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <span>Total (including escrow fee):</span>
                  <span className="text-blue-600">
                    GH₵
                    {finalPrice.toFixed(2)}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={handleBuyNow}
                  className="w-full bg-blue-600 text-white py-3 text-lg font-semibold"
                  disabled={product.stock_quantity === 0}
                >
                  Buy Now - Secure with Escrow
                </Button>

                <Button
                  onClick={handleAddToCart}
                  variant="outline"
                  className="w-full py-3 text-lg font-semibold"
                  disabled={addingToCart || product.stock_quantity === 0}
                >
                  {addingToCart ? 'Adding...' : 'Add to Cart'}
                </Button>
              </div>

              {/* Security Features */}
              <div className="mt-6 space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <ShieldCheckIcon className="h-5 w-5 text-green-500" />
                  <span>Protected by PayHold Escrow</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <TruckIcon className="h-5 w-5 text-blue-500" />
                  <span>Free shipping on orders over GH₵ 200</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="border-t border-gray-200 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Description</h2>
            <div className="prose max-w-none text-gray-600">
              {product.description || 'No description available.'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProductDetailPage;
