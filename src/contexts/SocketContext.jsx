import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import io from 'socket.io-client';
import { toast } from 'react-toastify';
import logger from '../components/helpers/logger';

const SocketContext = createContext();

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:8000';

export const useSocketContext = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocketContext must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const { user, token } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  useEffect(() => {
    if (user && token) {
      initializeSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [user, token]);

  const initializeSocket = () => {
    try {
      // Disconnect existing socket if any
      if (socketRef.current) {
        socketRef.current.disconnect();
      }

      logger.log('Initializing socket connection...');

      // Create new socket connection
      socketRef.current = io(SOCKET_URL, {
        auth: {
          token: token,
          user_id: user.id
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: 5
      });

      // Connection event handlers
      socketRef.current.on('connect', () => {
        logger.log('Socket connected:', socketRef.current.id);
        setIsConnected(true);
        setConnectionError(null);
        setReconnectAttempts(0);
        
        // Join user room for personal notifications
        socketRef.current.emit('join_user_room', { 
          user_id: user.id,
          user_role: user.role 
        });

        // Show connection restored message if this was a reconnection
        if (reconnectAttempts > 0) {
          toast.success('Connection restored');
        }
      });

      socketRef.current.on('disconnect', (reason) => {
        logger.log('Socket disconnected:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, try to reconnect
          socketRef.current.connect();
        }
      });

      socketRef.current.on('connect_error', (error) => {
        logger.error('Socket connection error:', error);
        setConnectionError(error.message);
        setIsConnected(false);
        
        // Show user-friendly error message
        if (error.message.includes('authentication')) {
          toast.error('Authentication failed. Please login again.');
        } else if (reconnectAttempts === 0) {
          // Only show error on first attempt
          toast.error('Connection failed. Retrying...');
        }
      });

      socketRef.current.on('reconnect', (attemptNumber) => {
        logger.log('Socket reconnected after', attemptNumber, 'attempts');
        setIsConnected(true);
        setConnectionError(null);
        setReconnectAttempts(attemptNumber);
      });

      socketRef.current.on('reconnect_attempt', (attemptNumber) => {
        logger.log('Reconnection attempt:', attemptNumber);
        setReconnectAttempts(attemptNumber);
      });

      socketRef.current.on('reconnect_error', (error) => {
        logger.error('Socket reconnection error:', error);
        setConnectionError(error.message);
      });

      socketRef.current.on('reconnect_failed', () => {
        logger.error('Socket reconnection failed');
        setConnectionError('Failed to reconnect');
        toast.error('Unable to connect to server. Please refresh the page.');
      });

      // Global event handlers
      setupGlobalEventHandlers();

    } catch (error) {
      logger.error('Failed to initialize socket:', error);
      setConnectionError(error.message);
    }
  };

  const setupGlobalEventHandlers = () => {
    if (!socketRef.current) return;

    // Notification events
    socketRef.current.on('notification', (data) => {
      handleNotification(data);
      
      // Dispatch to Redux store if needed
      dispatch({ type: 'notifications/addNotification', payload: data.notification });
    });

    // Transaction events
    socketRef.current.on('transaction_update', (data) => {
      handleTransactionUpdate(data);
      
      // Dispatch to Redux store
      dispatch({ type: 'transactions/updateTransaction', payload: data.transaction });
    });

    // Order events
    socketRef.current.on('order_update', (data) => {
      handleOrderUpdate(data);
      
      // Dispatch to Redux store
      dispatch({ type: 'orders/updateOrder', payload: data.order });
    });

    // Chat events
    socketRef.current.on('new_message', (data) => {
      handleNewMessage(data);
      
      // Dispatch to Redux store
      dispatch({ type: 'chat/addMessage', payload: data });
    });

    socketRef.current.on('user_typing', (data) => {
      dispatch({ type: 'chat/setUserTyping', payload: data });
    });

    socketRef.current.on('user_stopped_typing', (data) => {
      dispatch({ type: 'chat/setUserStoppedTyping', payload: data });
    });

    // System events
    socketRef.current.on('system_message', (data) => {
      handleSystemMessage(data);
    });

    // User events
    socketRef.current.on('user_status_change', (data) => {
      dispatch({ type: 'users/updateUserStatus', payload: data });
    });

    // Dispute events
    socketRef.current.on('dispute_update', (data) => {
      handleDisputeUpdate(data);
      
      // Dispatch to Redux store
      dispatch({ type: 'disputes/updateDispute', payload: data.dispute });
    });
  };

  const disconnectSocket = () => {
    if (socketRef.current) {
      logger.log('Disconnecting socket...');
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
      setConnectionError(null);
      setReconnectAttempts(0);
    }
  };

  // Event handlers
  const handleNotification = (data) => {
    const { type, title, message, action_url } = data.notification;
    
    const toastOptions = {
      onClick: action_url ? () => window.location.href = action_url : undefined,
      autoClose: type === 'error' ? false : 5000
    };

    switch (type) {
      case 'success':
        toast.success(message || title, toastOptions);
        break;
      case 'error':
        toast.error(message || title, toastOptions);
        break;
      case 'warning':
        toast.warning(message || title, toastOptions);
        break;
      case 'info':
      default:
        toast.info(message || title, toastOptions);
        break;
    }
  };

  const handleTransactionUpdate = (data) => {
    const { transaction, status, message } = data;
    
    // Dispatch custom event for components to listen to
    window.dispatchEvent(new CustomEvent('transactionUpdate', {
      detail: { transaction, status, message }
    }));

    // Show notification
    toast.info(`Transaction #${transaction.id}: ${message}`, {
      onClick: () => window.location.href = `/transactions/${transaction.id}`
    });
  };

  const handleOrderUpdate = (data) => {
    const { order, status, message } = data;
    
    // Dispatch custom event for components to listen to
    window.dispatchEvent(new CustomEvent('orderUpdate', {
      detail: { order, status, message }
    }));

    // Show notification
    toast.info(`Order #${order.id}: ${message}`, {
      onClick: () => window.location.href = `/orders/${order.id}`
    });
  };

  const handleNewMessage = (data) => {
    const { message, conversation, sender_name } = data;
    
    // Dispatch custom event for chat components
    window.dispatchEvent(new CustomEvent('newMessage', {
      detail: { message, conversation, sender_name }
    }));

    // Show notification if message is not from current user
    if (message.sender_id !== user.id) {
      toast.info(`New message from ${sender_name}`, {
        onClick: () => {
          // Open chat with this conversation
          window.dispatchEvent(new CustomEvent('openConversation', {
            detail: { conversation }
          }));
        }
      });
    }
  };

  const handleDisputeUpdate = (data) => {
    const { dispute, status, message } = data;
    
    // Dispatch custom event for dispute components
    window.dispatchEvent(new CustomEvent('disputeUpdate', {
      detail: { dispute, status, message }
    }));

    // Show notification
    toast.warning(`Dispute #${dispute.id}: ${message}`, {
      onClick: () => window.location.href = `/disputes/${dispute.id}`
    });
  };

  const handleSystemMessage = (data) => {
    const { type, message, persistent } = data;
    
    const toastOptions = {
      autoClose: persistent ? false : 8000,
      closeOnClick: !persistent
    };

    switch (type) {
      case 'maintenance':
        toast.warning(`System Maintenance: ${message}`, toastOptions);
        break;
      case 'announcement':
        toast.info(`Announcement: ${message}`, toastOptions);
        break;
      case 'security':
        toast.error(`Security Alert: ${message}`, toastOptions);
        break;
      default:
        toast.info(message, toastOptions);
    }
  };

  // Socket utility functions
  const emit = (event, data) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
      return true;
    } else {
      logger.warn('Socket not connected. Cannot emit event:', event);
      return false;
    }
  };

  const on = (event, callback) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event, callback) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback);
    }
  };

  // Room management
  const joinRoom = (room) => {
    return emit('join_room', { room });
  };

  const leaveRoom = (room) => {
    return emit('leave_room', { room });
  };

  // Chat functions
  const joinConversation = (conversationId) => {
    return emit('join_conversation', { conversation_id: conversationId });
  };

  const leaveConversation = (conversationId) => {
    return emit('leave_conversation', { conversation_id: conversationId });
  };

  const sendTyping = (conversationId) => {
    return emit('typing', { conversation_id: conversationId });
  };

  const stopTyping = (conversationId) => {
    return emit('stop_typing', { conversation_id: conversationId });
  };

  // Transaction functions
  const joinTransaction = (transactionId) => {
    return emit('join_transaction', { transaction_id: transactionId });
  };

  const leaveTransaction = (transactionId) => {
    return emit('leave_transaction', { transaction_id: transactionId });
  };

  // Order functions
  const joinOrder = (orderId) => {
    return emit('join_order', { order_id: orderId });
  };

  const leaveOrder = (orderId) => {
    return emit('leave_order', { order_id: orderId });
  };

  // Dispute functions
  const joinDispute = (disputeId) => {
    return emit('join_dispute', { dispute_id: disputeId });
  };

  const leaveDispute = (disputeId) => {
    return emit('leave_dispute', { dispute_id: disputeId });
  };

  const contextValue = {
    socket: socketRef.current,
    isConnected,
    connectionError,
    reconnectAttempts,
    emit,
    on,
    off,
    joinRoom,
    leaveRoom,
    joinConversation,
    leaveConversation,
    sendTyping,
    stopTyping,
    joinTransaction,
    leaveTransaction,
    joinOrder,
    leaveOrder,
    joinDispute,
    leaveDispute
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketContext;
