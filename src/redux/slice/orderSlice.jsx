import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { orderAPI } from '../../services/apiServices';

// Async thunks
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await orderAPI.getOrders(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch orders');
    }
  }
);

export const fetchOrder = createAsyncThunk(
  'orders/fetchOrder',
  async (orderId, { rejectWithValue }) => {
    try {
      const response = await orderAPI.getOrder(orderId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch order');
    }
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (orderData, { rejectWithValue }) => {
    try {
      const response = await orderAPI.createOrder(orderData);
      toast.success('Order created successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create order';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateOrderStatus = createAsyncThunk(
  'orders/updateOrderStatus',
  async ({ orderId, status, notes }, { rejectWithValue }) => {
    try {
      const response = await orderAPI.updateOrderStatus(orderId, status, notes);
      toast.success('Order status updated successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update order status';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'orders/cancelOrder',
  async ({ orderId, reason }, { rejectWithValue }) => {
    try {
      const response = await orderAPI.cancelOrder(orderId, reason);
      toast.success('Order cancelled successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to cancel order';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const createReview = createAsyncThunk(
  'orders/createReview',
  async ({ orderId, reviewData }, { rejectWithValue }) => {
    try {
      const response = await orderAPI.createReview(orderId, reviewData);
      toast.success('Review submitted successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to submit review';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

const initialState = {
  // Orders data
  orders: [],
  currentOrder: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 20
  },
  
  // Filters
  filters: {
    status: '',
    date_from: '',
    date_to: '',
    search: ''
  },
  
  // Loading states
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isCancelling: false,
  isSubmittingReview: false,
  
  // Error states
  error: null,
  createError: null,
  updateError: null,
  cancelError: null,
  reviewError: null,
  
  // Order statistics
  stats: {
    total_orders: 0,
    pending_orders: 0,
    completed_orders: 0,
    cancelled_orders: 0,
    total_spent: 0,
    total_earned: 0
  }
};

const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.createError = null;
      state.updateError = null;
      state.cancelError = null;
      state.reviewError = null;
    },
    
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    
    updateOrderInList: (state, action) => {
      const updatedOrder = action.payload;
      const index = state.orders.findIndex(order => order.id === updatedOrder.id);
      if (index !== -1) {
        state.orders[index] = updatedOrder;
      }
    },
    
    addOrderToList: (state, action) => {
      state.orders.unshift(action.payload);
    },
    
    removeOrderFromList: (state, action) => {
      const orderId = action.payload;
      state.orders = state.orders.filter(order => order.id !== orderId);
    },
    
    updateOrderStats: (state, action) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    
    // Real-time order updates
    orderStatusUpdated: (state, action) => {
      const { orderId, status, updatedAt } = action.payload;
      
      // Update in orders list
      const orderIndex = state.orders.findIndex(order => order.id === orderId);
      if (orderIndex !== -1) {
        state.orders[orderIndex].status = status;
        state.orders[orderIndex].updated_at = updatedAt;
      }
      
      // Update current order if it matches
      if (state.currentOrder?.id === orderId) {
        state.currentOrder.status = status;
        state.currentOrder.updated_at = updatedAt;
      }
    },
    
    orderPaymentUpdated: (state, action) => {
      const { orderId, paymentStatus, paymentReference } = action.payload;
      
      // Update in orders list
      const orderIndex = state.orders.findIndex(order => order.id === orderId);
      if (orderIndex !== -1) {
        state.orders[orderIndex].payment_status = paymentStatus;
        state.orders[orderIndex].payment_reference = paymentReference;
      }
      
      // Update current order if it matches
      if (state.currentOrder?.id === orderId) {
        state.currentOrder.payment_status = paymentStatus;
        state.currentOrder.payment_reference = paymentReference;
      }
    }
  },
  
  extraReducers: (builder) => {
    // Fetch Orders
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.orders || [];
        state.pagination = action.payload.pagination || initialState.pagination;
        state.stats = action.payload.stats || state.stats;
        state.error = null;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Fetch Single Order
    builder
      .addCase(fetchOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentOrder = action.payload.order;
        state.error = null;
      })
      .addCase(fetchOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Create Order
    builder
      .addCase(createOrder.pending, (state) => {
        state.isCreating = true;
        state.createError = null;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.isCreating = false;
        const newOrder = action.payload.order;
        state.orders.unshift(newOrder);
        state.currentOrder = newOrder;
        state.createError = null;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.isCreating = false;
        state.createError = action.payload;
      });

    // Update Order Status
    builder
      .addCase(updateOrderStatus.pending, (state) => {
        state.isUpdating = true;
        state.updateError = null;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.isUpdating = false;
        const updatedOrder = action.payload.order;
        
        // Update in orders list
        const index = state.orders.findIndex(order => order.id === updatedOrder.id);
        if (index !== -1) {
          state.orders[index] = updatedOrder;
        }
        
        // Update current order if it's the same
        if (state.currentOrder?.id === updatedOrder.id) {
          state.currentOrder = updatedOrder;
        }
        
        state.updateError = null;
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.isUpdating = false;
        state.updateError = action.payload;
      });

    // Cancel Order
    builder
      .addCase(cancelOrder.pending, (state) => {
        state.isCancelling = true;
        state.cancelError = null;
      })
      .addCase(cancelOrder.fulfilled, (state, action) => {
        state.isCancelling = false;
        const cancelledOrder = action.payload.order;
        
        // Update in orders list
        const index = state.orders.findIndex(order => order.id === cancelledOrder.id);
        if (index !== -1) {
          state.orders[index] = cancelledOrder;
        }
        
        // Update current order if it's the same
        if (state.currentOrder?.id === cancelledOrder.id) {
          state.currentOrder = cancelledOrder;
        }
        
        state.cancelError = null;
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.isCancelling = false;
        state.cancelError = action.payload;
      });

    // Create Review
    builder
      .addCase(createReview.pending, (state) => {
        state.isSubmittingReview = true;
        state.reviewError = null;
      })
      .addCase(createReview.fulfilled, (state, action) => {
        state.isSubmittingReview = false;
        const { orderId, review } = action.payload;
        
        // Update order with review
        const index = state.orders.findIndex(order => order.id === orderId);
        if (index !== -1) {
          state.orders[index].review = review;
          state.orders[index].has_review = true;
        }
        
        // Update current order if it's the same
        if (state.currentOrder?.id === orderId) {
          state.currentOrder.review = review;
          state.currentOrder.has_review = true;
        }
        
        state.reviewError = null;
      })
      .addCase(createReview.rejected, (state, action) => {
        state.isSubmittingReview = false;
        state.reviewError = action.payload;
      });
  }
});

export const {
  clearError,
  setFilters,
  clearFilters,
  clearCurrentOrder,
  updateOrderInList,
  addOrderToList,
  removeOrderFromList,
  updateOrderStats,
  orderStatusUpdated,
  orderPaymentUpdated
} = orderSlice.actions;

export default orderSlice.reducer;
