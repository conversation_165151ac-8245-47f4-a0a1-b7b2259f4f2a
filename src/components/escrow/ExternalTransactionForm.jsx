import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import {
  ShoppingBagIcon,
  LinkIcon,
  CurrencyDollarIcon,
  MapPinIcon,
  UserIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import LoadingSpinner from '../helpers/LoadingSpinner';
import logger from '../helpers/logger';

// External transaction API (to be added to apiServices.jsx)
const externalTransactionAPI = {
  createTransaction: (transactionData) => fetch('/api/v1/external_transactions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
    body: JSON.stringify({ transaction: transactionData }),
  }).then((res) => res.json()),

  searchSellers: (query) => fetch(`/api/v1/users/search?role=seller&q=${encodeURIComponent(query)}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('authToken')}`,
    },
  }).then((res) => res.json()),
};

function ExternalTransactionForm() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sellers, setSellers] = useState([]);
  const [searchingSellers, setSearchingSellers] = useState(false);
  const [selectedSeller, setSelectedSeller] = useState(null);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      external_store_name: '',
      external_product_url: '',
      product_name: '',
      product_description: '',
      base_amount: '',
      currency: 'GHS',
      seller_search: '',
      seller_id: '',
      shipping_address: {
        street: user?.address?.street || '',
        city: user?.address?.city || '',
        region: user?.address?.region || '',
        country: user?.address?.country || 'Ghana',
      },
      notes: '',
    },
  });

  const watchedAmount = watch('base_amount');
  const watchedSellerSearch = watch('seller_search');
  const watchedPlatform = watch('external_store_name');

  // Calculate fees
  const baseAmount = parseFloat(watchedAmount) || 0;
  const escrowFee = baseAmount * 0.05; // 5% fee
  const totalAmount = baseAmount + escrowFee;

  // Search sellers
  useEffect(() => {
    const searchSellers = async () => {
      if (watchedSellerSearch && watchedSellerSearch.length >= 2) {
        setSearchingSellers(true);
        try {
          const response = await externalTransactionAPI.searchSellers(watchedSellerSearch);
          if (response.success) {
            setSellers(response.sellers || []);
          }
        } catch (error) {
          logger.error('Error searching sellers:', error);
        } finally {
          setSearchingSellers(false);
        }
      } else {
        setSellers([]);
      }
    };

    const debounceTimer = setTimeout(searchSellers, 300);
    return () => clearTimeout(debounceTimer);
  }, [watchedSellerSearch]);

  const handleSellerSelect = (seller) => {
    setSelectedSeller(seller);
    setValue('seller_id', seller.id);
    setValue('seller_search', `${seller.first_name} ${seller.last_name} (@${seller.username})`);
    setSellers([]);
  };

  const onSubmit = async (formData) => {
    try {
      setIsSubmitting(true);

      // Validate required fields
      if (!selectedSeller) {
        toast.error('Please select a seller');
        return;
      }

      // Check if URL is required for selected platform
      const selectedPlatform = supportedPlatforms.find((p) => p.value === formData.external_store_name);
      if (selectedPlatform?.requiresUrl && !formData.external_product_url) {
        toast.error('Please provide the product URL for this platform');
        return;
      }

      // Prepare transaction data
      const transactionData = {
        seller_id: selectedSeller.id,
        external_store_name: formData.external_store_name === 'Other'
          ? formData.custom_platform_name
          : formData.external_store_name,
        external_product_url: formData.external_product_url || null,
        product_name: formData.product_name,
        product_description: formData.product_description,
        base_amount: parseFloat(formData.base_amount),
        escrow_fee: escrowFee,
        total_amount: totalAmount,
        currency: formData.currency,
        shipping_address: formData.shipping_address,
        notes: formData.notes,
        transaction_type: 'external',
        platform_type: formData.external_store_name, // Keep original platform type for categorization
      };

      const response = await externalTransactionAPI.createTransaction(transactionData);

      if (response.success) {
        toast.success('External transaction created successfully!');
        navigate(`/transactions/${response.transaction.id}`);
      } else {
        throw new Error(response.error || 'Failed to create transaction');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to create external transaction');
    } finally {
      setIsSubmitting(false);
    }
  };

  const supportedPlatforms = [
    { name: 'Facebook Marketplace', value: 'Facebook', requiresUrl: true },
    { name: 'TikTok Shop', value: 'TikTok', requiresUrl: true },
    { name: 'Instagram Shop', value: 'Instagram', requiresUrl: true },
    { name: 'WhatsApp Business', value: 'WhatsApp', requiresUrl: false },
    { name: 'Telegram', value: 'Telegram', requiresUrl: false },
    { name: 'Twitter/X', value: 'Twitter', requiresUrl: true },
    { name: 'LinkedIn', value: 'LinkedIn', requiresUrl: true },
    { name: 'Snapchat', value: 'Snapchat', requiresUrl: false },
    { name: 'YouTube', value: 'YouTube', requiresUrl: true },
    { name: 'Discord', value: 'Discord', requiresUrl: false },
    { name: 'Reddit', value: 'Reddit', requiresUrl: true },
    { name: 'Pinterest', value: 'Pinterest', requiresUrl: true },
    { name: 'Offline Deal', value: 'Offline', requiresUrl: false },
    { name: 'Phone Call', value: 'Phone', requiresUrl: false },
    { name: 'In-Person Meeting', value: 'InPerson', requiresUrl: false },
    { name: 'Other Platform', value: 'Other', requiresUrl: false },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <LinkIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Create External Transaction
              </h1>
              <p className="text-sm text-gray-600">
                Secure your purchase from any external platform, social media, app, or offline deal
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
          {/* Platform & Product Info */}
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <ShoppingBagIcon className="h-5 w-5 mr-2 text-gray-500" />
              Product Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Platform/Store Name *
                </label>
                <select
                  {...register('external_store_name', { required: 'Platform is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select platform...</option>
                  {supportedPlatforms.map((platform) => (
                    <option key={platform.value} value={platform.value}>
                      {platform.name}
                    </option>
                  ))}
                </select>
                {errors.external_store_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.external_store_name.message}</p>
                )}
              </div>

              {/* Custom platform name field when "Other" is selected */}
              {watchedPlatform === 'Other' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom Platform Name *
                  </label>
                  <input
                    type="text"
                    {...register('custom_platform_name', {
                      required: watchedPlatform === 'Other' ? 'Please specify the platform name' : false,
                    })}
                    placeholder="e.g., Local Marketplace, Custom App, etc."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.custom_platform_name && (
                    <p className="mt-1 text-sm text-red-600">{errors.custom_platform_name.message}</p>
                  )}
                </div>
              )}

              {/* Conditional URL field */}
              {(() => {
                const selectedPlatform = supportedPlatforms.find((p) => p.value === watchedPlatform);
                const requiresUrl = selectedPlatform?.requiresUrl;

                return (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product URL
                      {' '}
                      {requiresUrl ? '*' : '(Optional)'}
                    </label>
                    <input
                      type="url"
                      {...register('external_product_url', requiresUrl ? {
                        required: 'Product URL is required for this platform',
                        pattern: {
                          value: /^https?:\/\/.+/,
                          message: 'Please enter a valid URL',
                        },
                      } : {})}
                      placeholder={
                        requiresUrl
                          ? 'https://facebook.com/marketplace/item/...'
                          : 'Product URL (if available)'
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    {errors.external_product_url && (
                      <p className="mt-1 text-sm text-red-600">{errors.external_product_url.message}</p>
                    )}
                    {!requiresUrl && (
                      <p className="mt-1 text-xs text-gray-500">
                        For offline deals or platforms without URLs, you can leave this empty
                      </p>
                    )}
                  </div>
                );
              })()}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                {...register('product_name', { required: 'Product name is required' })}
                placeholder="Enter the product name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.product_name && (
                <p className="mt-1 text-sm text-red-600">{errors.product_name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Description
              </label>
              <textarea
                {...register('product_description')}
                rows={3}
                placeholder="Describe the product (optional)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Seller Selection */}
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <UserIcon className="h-5 w-5 mr-2 text-gray-500" />
              Seller Information
            </h2>

            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search & Select Seller *
              </label>
              <input
                type="text"
                {...register('seller_search', { required: 'Please select a seller' })}
                placeholder="Search by name or username..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              {/* Seller search results */}
              {sellers.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {sellers.map((seller) => (
                    <button
                      key={seller.id}
                      type="button"
                      onClick={() => handleSellerSelect(seller)}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3"
                    >
                      <img
                        src={seller.avatar || '/default-avatar.jpg'}
                        alt={seller.first_name}
                        className="h-8 w-8 rounded-full"
                      />
                      <div>
                        <p className="font-medium text-gray-900">
                          {seller.first_name}
                          {' '}
                          {seller.last_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          @
                          {seller.username}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {searchingSellers && (
                <div className="absolute right-3 top-10">
                  <LoadingSpinner size="small" />
                </div>
              )}

              {errors.seller_search && (
                <p className="mt-1 text-sm text-red-600">{errors.seller_search.message}</p>
              )}
            </div>

            {selectedSeller && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center space-x-3">
                  <img
                    src={selectedSeller.avatar || '/default-avatar.jpg'}
                    alt={selectedSeller.first_name}
                    className="h-10 w-10 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-green-900">
                      {selectedSeller.first_name}
                      {' '}
                      {selectedSeller.last_name}
                    </p>
                    <p className="text-sm text-green-700">
                      @
                      {selectedSeller.username}
                      {' '}
                      • Rating:
                      {' '}
                      {selectedSeller.rating || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Amount & Fees */}
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <CurrencyDollarIcon className="h-5 w-5 mr-2 text-gray-500" />
              Payment Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Amount *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">GH₵</span>
                  <input
                    type="number"
                    step="0.01"
                    min="1"
                    {...register('base_amount', {
                      required: 'Amount is required',
                      min: { value: 1, message: 'Amount must be at least GH₵1' },
                    })}
                    placeholder="0.00"
                    className="w-full pl-12 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                {errors.base_amount && (
                  <p className="mt-1 text-sm text-red-600">{errors.base_amount.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  {...register('currency')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="GHS">Ghanaian Cedi (GH₵)</option>
                  <option value="USD">US Dollar ($)</option>
                  <option value="EUR">Euro (€)</option>
                </select>
              </div>
            </div>

            {/* Fee Breakdown */}
            {baseAmount > 0 && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-900 mb-3">Payment Breakdown</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Product Amount:</span>
                    <span className="font-medium">
                      GH₵
                      {baseAmount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Escrow Fee (5%):</span>
                    <span className="font-medium">
                      GH₵
                      {escrowFee.toFixed(2)}
                    </span>
                  </div>
                  <div className="border-t border-gray-300 pt-2 flex justify-between">
                    <span className="font-medium text-gray-900">Total Amount:</span>
                    <span className="font-bold text-blue-600">
                      GH₵
                      {totalAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Shipping Address */}
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <MapPinIcon className="h-5 w-5 mr-2 text-gray-500" />
              Shipping Address
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Street Address *
                </label>
                <input
                  type="text"
                  {...register('shipping_address.street', { required: 'Street address is required' })}
                  placeholder="Enter street address"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {errors.shipping_address?.street && (
                  <p className="mt-1 text-sm text-red-600">{errors.shipping_address.street.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  {...register('shipping_address.city', { required: 'City is required' })}
                  placeholder="Enter city"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {errors.shipping_address?.city && (
                  <p className="mt-1 text-sm text-red-600">{errors.shipping_address.city.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Region *
                </label>
                <select
                  {...register('shipping_address.region', { required: 'Region is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select region...</option>
                  <option value="Greater Accra">Greater Accra</option>
                  <option value="Ashanti">Ashanti</option>
                  <option value="Western">Western</option>
                  <option value="Central">Central</option>
                  <option value="Eastern">Eastern</option>
                  <option value="Volta">Volta</option>
                  <option value="Northern">Northern</option>
                  <option value="Upper East">Upper East</option>
                  <option value="Upper West">Upper West</option>
                  <option value="Brong Ahafo">Brong Ahafo</option>
                </select>
                {errors.shipping_address?.region && (
                  <p className="mt-1 text-sm text-red-600">{errors.shipping_address.region.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Country
                </label>
                <input
                  type="text"
                  {...register('shipping_address.country')}
                  placeholder="Ghana"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              placeholder="Any special instructions or notes for the seller..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-3 mt-0.5" />
              <div className="text-sm">
                <h3 className="font-medium text-yellow-800 mb-1">Important Notice</h3>
                <p className="text-yellow-700">
                  By creating this transaction, you agree that the seller will be notified and must accept
                  the transaction before you can proceed with payment. Ensure all details are correct as
                  they cannot be changed once the seller accepts.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !selectedSeller}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Creating Transaction...
                </>
              ) : (
                'Create Transaction'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ExternalTransactionForm;
