import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  BellIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  ComputerDesktopIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { notificationAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function NotificationSettings({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  
  const [settings, setSettings] = useState({
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    in_app_notifications: true,
    categories: {
      transactions: {
        email: true,
        push: true,
        sms: false,
        in_app: true
      },
      orders: {
        email: true,
        push: true,
        sms: false,
        in_app: true
      },
      messages: {
        email: false,
        push: true,
        sms: false,
        in_app: true
      },
      marketing: {
        email: true,
        push: false,
        sms: false,
        in_app: false
      },
      security: {
        email: true,
        push: true,
        sms: true,
        in_app: true
      },
      system: {
        email: false,
        push: true,
        sms: false,
        in_app: true
      }
    },
    quiet_hours: {
      enabled: false,
      start_time: '22:00',
      end_time: '08:00',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [pushSupported, setPushSupported] = useState(false);

  useEffect(() => {
    // Check if push notifications are supported
    setPushSupported('Notification' in window && 'serviceWorker' in navigator);
    
    if (user) {
      fetchSettings();
    }
  }, [user]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await notificationAPI.getSettings();
      
      if (response.data.success) {
        setSettings(prev => ({
          ...prev,
          ...response.data.settings
        }));
      }
    } catch (error) {
      logger.error('Failed to fetch notification settings:', error);
      toast.error('Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      const response = await notificationAPI.updateSettings(settings);
      
      if (response.data.success) {
        toast.success('Notification settings saved');
      }
    } catch (error) {
      logger.error('Failed to save notification settings:', error);
      toast.error('Failed to save notification settings');
    } finally {
      setSaving(false);
    }
  };

  const requestPushPermission = async () => {
    if (!pushSupported) {
      toast.error('Push notifications are not supported in this browser');
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        setSettings(prev => ({
          ...prev,
          push_notifications: true
        }));
        toast.success('Push notifications enabled');
      } else if (permission === 'denied') {
        toast.error('Push notifications denied. Please enable them in your browser settings.');
      }
    } catch (error) {
      logger.error('Failed to request push permission:', error);
      toast.error('Failed to enable push notifications');
    }
  };

  const handleGlobalToggle = (type, enabled) => {
    setSettings(prev => ({
      ...prev,
      [`${type}_notifications`]: enabled
    }));
  };

  const handleCategoryToggle = (category, type, enabled) => {
    setSettings(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [category]: {
          ...prev.categories[category],
          [type]: enabled
        }
      }
    }));
  };

  const handleQuietHoursToggle = (enabled) => {
    setSettings(prev => ({
      ...prev,
      quiet_hours: {
        ...prev.quiet_hours,
        enabled
      }
    }));
  };

  const handleQuietHoursChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      quiet_hours: {
        ...prev.quiet_hours,
        [field]: value
      }
    }));
  };

  const categories = [
    {
      key: 'transactions',
      name: 'Transactions',
      description: 'Payment confirmations, escrow updates, refunds'
    },
    {
      key: 'orders',
      name: 'Orders',
      description: 'Order confirmations, shipping updates, delivery notifications'
    },
    {
      key: 'messages',
      name: 'Messages',
      description: 'New chat messages from buyers and sellers'
    },
    {
      key: 'security',
      name: 'Security',
      description: 'Login alerts, password changes, account security'
    },
    {
      key: 'marketing',
      name: 'Marketing',
      description: 'Promotions, new features, product recommendations'
    },
    {
      key: 'system',
      name: 'System',
      description: 'Maintenance notifications, system updates'
    }
  ];

  const notificationTypes = [
    {
      key: 'email',
      name: 'Email',
      icon: EnvelopeIcon,
      description: 'Receive notifications via email'
    },
    {
      key: 'push',
      name: 'Push',
      icon: ComputerDesktopIcon,
      description: 'Browser push notifications'
    },
    {
      key: 'sms',
      name: 'SMS',
      icon: DevicePhoneMobileIcon,
      description: 'Text message notifications'
    },
    {
      key: 'in_app',
      name: 'In-App',
      icon: BellIcon,
      description: 'Notifications within the app'
    }
  ];

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Notification Settings</h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage how you receive notifications from PayHold
            </p>
          </div>
          
          <button
            onClick={saveSettings}
            disabled={saving}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      <div className="p-6 space-y-8">
        {/* Global Settings */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-4">Global Settings</h3>
          
          <div className="space-y-4">
            {notificationTypes.map((type) => {
              const Icon = type.icon;
              const isEnabled = settings[`${type.key}_notifications`];
              const isDisabled = type.key === 'push' && !pushSupported;
              
              return (
                <div key={type.key} className="flex items-center justify-between py-3">
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{type.name}</p>
                      <p className="text-sm text-gray-500">{type.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {type.key === 'push' && !isEnabled && pushSupported && (
                      <button
                        onClick={requestPushPermission}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Enable
                      </button>
                    )}
                    
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={isEnabled}
                        onChange={(e) => handleGlobalToggle(type.key, e.target.checked)}
                        disabled={isDisabled}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"></div>
                    </label>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Category Settings */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-4">Notification Categories</h3>
          
          <div className="space-y-6">
            {categories.map((category) => (
              <div key={category.key} className="border border-gray-200 rounded-lg p-4">
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900">{category.name}</h4>
                  <p className="text-sm text-gray-500">{category.description}</p>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {notificationTypes.map((type) => {
                    const isGlobalEnabled = settings[`${type.key}_notifications`];
                    const isCategoryEnabled = settings.categories[category.key]?.[type.key];
                    const isDisabled = !isGlobalEnabled || (type.key === 'push' && !pushSupported);
                    
                    return (
                      <label key={type.key} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={isCategoryEnabled && isGlobalEnabled}
                          onChange={(e) => handleCategoryToggle(category.key, type.key, e.target.checked)}
                          disabled={isDisabled}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                        />
                        <span className={`text-sm ${isDisabled ? 'text-gray-400' : 'text-gray-700'}`}>
                          {type.name}
                        </span>
                      </label>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quiet Hours */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-4">Quiet Hours</h3>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-gray-900">Enable Quiet Hours</p>
                <p className="text-sm text-gray-500">Pause non-urgent notifications during specified hours</p>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.quiet_hours.enabled}
                  onChange={(e) => handleQuietHoursToggle(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {settings.quiet_hours.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Time
                  </label>
                  <input
                    type="time"
                    value={settings.quiet_hours.start_time}
                    onChange={(e) => handleQuietHoursChange('start_time', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Time
                  </label>
                  <input
                    type="time"
                    value={settings.quiet_hours.end_time}
                    onChange={(e) => handleQuietHoursChange('end_time', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotificationSettings;
