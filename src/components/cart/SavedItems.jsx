import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  ShoppingCartIcon,
  TrashIcon,
  HeartIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

function SavedItems({ 
  savedItems = [], 
  onMoveToCart, 
  onRemoveItem, 
  onToggleWishlist,
  isLoading = false 
}) {
  const [movingItems, setMovingItems] = useState(new Set());
  const [removingItems, setRemovingItems] = useState(new Set());

  const handleMoveToCart = async (item) => {
    if (item.product.stock_quantity === 0) {
      toast.error('This item is currently out of stock');
      return;
    }

    setMovingItems(prev => new Set(prev).add(item.id));
    try {
      await onMoveToCart(item.id);
      toast.success('Item moved to cart');
    } catch (error) {
      toast.error(error.message || 'Failed to move item to cart');
    } finally {
      setMovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(item.id);
        return newSet;
      });
    }
  };

  const handleRemoveItem = async (itemId) => {
    setRemovingItems(prev => new Set(prev).add(itemId));
    try {
      await onRemoveItem(itemId);
      toast.success('Item removed from saved items');
    } catch (error) {
      toast.error(error.message || 'Failed to remove item');
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleToggleWishlist = async (item) => {
    try {
      await onToggleWishlist(item.id, !item.is_wishlisted);
      toast.success(
        item.is_wishlisted ? 'Removed from wishlist' : 'Added to wishlist'
      );
    } catch (error) {
      toast.error(error.message || 'Failed to update wishlist');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex space-x-4">
                <div className="h-16 w-16 bg-gray-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (savedItems.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No saved items</h3>
        <p className="mt-1 text-sm text-gray-500">
          Items you save for later will appear here
        </p>
        <div className="mt-6">
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">
            Saved for Later ({savedItems.length})
          </h2>
          <button
            onClick={() => {
              // Move all available items to cart
              savedItems
                .filter(item => item.product.stock_quantity > 0)
                .forEach(item => handleMoveToCart(item));
            }}
            disabled={savedItems.every(item => item.product.stock_quantity === 0)}
            className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            Move all to cart
          </button>
        </div>
      </div>

      {/* Saved Items List */}
      <div className="divide-y divide-gray-200">
        {savedItems.map((item) => {
          const isOutOfStock = item.product.stock_quantity === 0;
          const isLowStock = item.product.stock_quantity > 0 && item.product.stock_quantity <= 5;
          const isMoving = movingItems.has(item.id);
          const isRemoving = removingItems.has(item.id);
          const isProcessing = isMoving || isRemoving;

          return (
            <div
              key={item.id}
              className={`p-6 ${isOutOfStock ? 'opacity-75' : ''} ${
                isProcessing ? 'pointer-events-none' : ''
              }`}
            >
              <div className="flex items-start space-x-4">
                {/* Product Image */}
                <div className="flex-shrink-0 relative">
                  <Link to={`/products/${item.product.id}`}>
                    <img
                      src={item.product.primary_image?.url || '/placeholder-product.jpg'}
                      alt={item.product.name}
                      className="h-20 w-20 object-cover rounded-md border border-gray-200 hover:opacity-75 transition-opacity"
                    />
                  </Link>
                  {isProcessing && (
                    <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <Link
                        to={`/products/${item.product.id}`}
                        className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
                      >
                        {item.product.name}
                      </Link>
                      
                      <div className="mt-1 flex items-center space-x-2 text-xs text-gray-500">
                        <span>By {item.product.seller?.store_name || item.product.seller?.username}</span>
                        {item.product.condition && item.product.condition !== 'new' && (
                          <>
                            <span>•</span>
                            <span className="capitalize">{item.product.condition}</span>
                          </>
                        )}
                      </div>

                      {/* Stock Status */}
                      <div className="mt-2">
                        {isOutOfStock ? (
                          <div className="flex items-center text-red-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            <span className="text-xs font-medium">Out of Stock</span>
                          </div>
                        ) : isLowStock ? (
                          <div className="flex items-center text-yellow-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            <span className="text-xs font-medium">
                              Only {item.product.stock_quantity} left
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center text-green-600">
                            <span className="text-xs">In Stock</span>
                          </div>
                        )}
                      </div>

                      {/* Saved Date */}
                      <div className="mt-1 text-xs text-gray-500">
                        Saved on {formatDate(item.saved_at || item.created_at)}
                      </div>
                    </div>

                    {/* Price */}
                    <div className="text-right ml-4">
                      <div className="text-lg font-semibold text-gray-900">
                        GH₵ {item.product.price.toFixed(2)}
                      </div>
                      {item.product.original_price && item.product.original_price > item.product.price && (
                        <div className="text-xs text-gray-500 line-through">
                          GH₵ {item.product.original_price.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {/* Move to Cart */}
                      <button
                        onClick={() => handleMoveToCart(item)}
                        disabled={isOutOfStock || isProcessing}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isMoving ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Moving...
                          </>
                        ) : (
                          <>
                            <ShoppingCartIcon className="h-3 w-3 mr-1" />
                            {isOutOfStock ? 'Out of Stock' : 'Move to Cart'}
                          </>
                        )}
                      </button>

                      {/* View Product */}
                      <Link
                        to={`/products/${item.product.id}`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <EyeIcon className="h-3 w-3 mr-1" />
                        View
                      </Link>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Wishlist Toggle */}
                      <button
                        onClick={() => handleToggleWishlist(item)}
                        disabled={isProcessing}
                        className={`p-2 rounded-md transition-colors ${
                          item.is_wishlisted 
                            ? 'text-red-500 hover:text-red-600 bg-red-50' 
                            : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                        title={item.is_wishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                      >
                        {item.is_wishlisted ? (
                          <HeartIconSolid className="h-4 w-4" />
                        ) : (
                          <HeartIcon className="h-4 w-4" />
                        )}
                      </button>

                      {/* Remove */}
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        disabled={isProcessing}
                        className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Remove from saved items"
                      >
                        {isRemoving ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                        ) : (
                          <TrashIcon className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Price Alert */}
                  {item.price_alert_enabled && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-center text-blue-800">
                        <ClockIcon className="h-4 w-4 mr-2" />
                        <span className="text-xs">
                          You'll be notified if the price drops below GH₵ {item.price_alert_threshold?.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {savedItems.filter(item => item.product.stock_quantity > 0).length} of {savedItems.length} items available
          </span>
          <Link
            to="/products"
            className="text-blue-600 hover:text-blue-800"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
}

export default SavedItems;
