# PayHold Backend - Frontend Integration Guide

## 🚀 Complete API Documentation for React Frontend Integration

This comprehensive guide provides everything your React frontend needs to integrate seamlessly with the PayHold backend - a complete escrow and mini-store platform for Ghana.

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Core API Endpoints](#core-api-endpoints)
4. [User Management](#user-management)
5. [Product Management](#product-management)
6. [Order Management](#order-management)
7. [Payment Integration](#payment-integration)
8. [Dashboard APIs](#dashboard-apis)
9. [Real-time Features](#real-time-features)
10. [File Management](#file-management)
11. [Advanced Features](#advanced-features)
12. [Error Handling](#error-handling)
13. [Frontend Integration Examples](#frontend-integration-examples)

## 🏗️ System Overview

### Base Configuration
- **Base URL**: `http://localhost:3000/api/v1` (Development)
- **API Version**: v1
- **Authentication**: JWT Bearer Token
- **Content-Type**: `application/json`
- **Database**: PostgreSQL
- **Real-time**: WebSocket (Action Cable)
- **File Storage**: Active Storage with multi-provider support

### User Roles
- **Admin**: Full system access and management
- **Seller**: Product and order management
- **Buyer/Customer**: Purchase and order tracking

### Core Features
- ✅ Complete Escrow System with Paystack Integration
- ✅ Mini-Store with Product & Order Management
- ✅ Real-time Chat & Notifications
- ✅ Advanced Analytics & Reporting
- ✅ File Upload & Management
- ✅ Multi-factor Authentication
- ✅ Ghana-specific Payment Features (Mobile Money, GHS)

## 🔐 Authentication & Authorization

### 1. User Registration

**Endpoint**: `POST /api/v1/auth/register`

**Request Body**:
```json
{
  "user": {
    "email": "<EMAIL>",
    "password": "securepassword123",
    "password_confirmation": "securepassword123",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+233123456789",
    "role": "buyer"
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+233123456789",
    "role": "buyer",
    "email_verified": false,
    "created_at": "2024-01-15T10:30:00Z"
  },
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-01-22T10:30:00Z"
}
```

### 2. User Login

**Endpoint**: `POST /api/v1/auth/login`

**Request Body**:
```json
{
  "user": {
    "email": "<EMAIL>",
    "password": "securepassword123"
  }
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "buyer",
    "email_verified": true,
    "last_login_at": "2024-01-15T10:30:00Z"
  },
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-01-22T10:30:00Z"
}
```

### 3. Token Refresh

**Endpoint**: `POST /api/v1/auth/refresh`

**Headers**: `Authorization: Bearer <current_token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-01-22T10:30:00Z"
}
```

### 4. Logout

**Endpoint**: `DELETE /api/v1/auth/logout`

**Headers**: `Authorization: Bearer <token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Authentication Headers for All Protected Routes
```javascript
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${localStorage.getItem('authToken')}`
};
```

## 👤 User Management

### 1. Get Current User Profile

**Endpoint**: `GET /api/v1/users/profile`

**Headers**: `Authorization: Bearer <token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+233123456789",
    "role": "buyer",
    "email_verified": true,
    "phone_verified": false,
    "profile_image_url": "https://example.com/profile.jpg",
    "address": {
      "street": "123 Main St",
      "city": "Accra",
      "region": "Greater Accra",
      "country": "Ghana"
    },
    "preferences": {
      "currency": "GHS",
      "language": "en",
      "notifications": {
        "email": true,
        "sms": false,
        "push": true
      }
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Update User Profile

**Endpoint**: `PATCH /api/v1/users/profile`

**Request Body**:
```json
{
  "user": {
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+233123456789",
    "address": {
      "street": "456 New St",
      "city": "Kumasi",
      "region": "Ashanti",
      "country": "Ghana"
    },
    "preferences": {
      "currency": "GHS",
      "language": "en",
      "notifications": {
        "email": true,
        "sms": true,
        "push": true
      }
    }
  }
}
```

### 3. Change Password

**Endpoint**: `PATCH /api/v1/users/change_password`

**Request Body**:
```json
{
  "current_password": "oldpassword123",
  "new_password": "newpassword456",
  "new_password_confirmation": "newpassword456"
}
```

### 4. Upload Profile Image

**Endpoint**: `POST /api/v1/users/upload_avatar`

**Content-Type**: `multipart/form-data`

**Form Data**:
- `avatar`: File (image file)

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Profile image uploaded successfully",
  "profile_image_url": "https://example.com/new-profile.jpg"
}
```

## 🛍️ Product Management

### 1. Get All Products (Public)

**Endpoint**: `GET /api/v1/products`

**Query Parameters**:
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 20, max: 100)
- `category`: Filter by category
- `search`: Search query
- `min_price`: Minimum price filter
- `max_price`: Maximum price filter
- `seller_id`: Filter by seller
- `status`: Filter by status (active, inactive)

**Example**: `GET /api/v1/products?page=1&per_page=20&category=electronics&search=phone`

**Response (200 OK)**:
```json
{
  "success": true,
  "products": [
    {
      "id": 1,
      "name": "iPhone 15 Pro",
      "description": "Latest iPhone with advanced features",
      "price": 5500.00,
      "currency": "GHS",
      "category": "electronics",
      "status": "active",
      "stock_quantity": 10,
      "images": [
        {
          "id": 1,
          "url": "https://example.com/iphone-1.jpg",
          "alt": "iPhone front view",
          "is_primary": true
        },
        {
          "id": 2,
          "url": "https://example.com/iphone-2.jpg",
          "alt": "iPhone back view",
          "is_primary": false
        }
      ],
      "seller": {
        "id": 2,
        "name": "Tech Store Ghana",
        "rating": 4.8,
        "total_sales": 150
      },
      "specifications": {
        "brand": "Apple",
        "model": "iPhone 15 Pro",
        "storage": "256GB",
        "color": "Natural Titanium"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total_pages": 5,
    "total_count": 95,
    "has_next": true,
    "has_prev": false
  }
}
```

### 2. Get Single Product

**Endpoint**: `GET /api/v1/products/:id`

**Response (200 OK)**:
```json
{
  "success": true,
  "product": {
    "id": 1,
    "name": "iPhone 15 Pro",
    "description": "Latest iPhone with advanced features",
    "price": 5500.00,
    "currency": "GHS",
    "category": "electronics",
    "status": "active",
    "stock_quantity": 10,
    "images": [...],
    "seller": {...},
    "specifications": {...},
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "comment": "Excellent product!",
        "reviewer": {
          "id": 3,
          "name": "Jane Smith"
        },
        "created_at": "2024-01-14T15:20:00Z"
      }
    ],
    "average_rating": 4.8,
    "total_reviews": 12,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 3. Create Product (Seller Only)

**Endpoint**: `POST /api/v1/products`

**Headers**: `Authorization: Bearer <seller_token>`

**Request Body**:
```json
{
  "product": {
    "name": "Samsung Galaxy S24",
    "description": "Latest Samsung flagship smartphone",
    "price": 4800.00,
    "category": "electronics",
    "stock_quantity": 15,
    "specifications": {
      "brand": "Samsung",
      "model": "Galaxy S24",
      "storage": "256GB",
      "color": "Phantom Black"
    }
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "message": "Product created successfully",
  "product": {
    "id": 2,
    "name": "Samsung Galaxy S24",
    "description": "Latest Samsung flagship smartphone",
    "price": 4800.00,
    "currency": "GHS",
    "category": "electronics",
    "status": "active",
    "stock_quantity": 15,
    "seller_id": 2,
    "created_at": "2024-01-15T11:00:00Z"
  }
}
```

### 4. Update Product (Seller Only)

**Endpoint**: `PATCH /api/v1/products/:id`

**Request Body**:
```json
{
  "product": {
    "name": "Samsung Galaxy S24 - Updated",
    "price": 4500.00,
    "stock_quantity": 20
  }
}
```

### 5. Delete Product (Seller Only)

**Endpoint**: `DELETE /api/v1/products/:id`

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Product deleted successfully"
}
```

### 6. Upload Product Images

**Endpoint**: `POST /api/v1/products/:id/images`

**Content-Type**: `multipart/form-data`

**Form Data**:
- `images[]`: Multiple image files
- `primary_image_index`: Index of primary image (optional)

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Images uploaded successfully",
  "images": [
    {
      "id": 3,
      "url": "https://example.com/product-image-1.jpg",
      "alt": "Product image 1",
      "is_primary": true
    }
  ]
}
```

## 🌐 External Transactions (All Platforms & Offline Deals)

### 1. Create External Transaction (Buyer)

**Endpoint**: `POST /api/v1/external_transactions`

**Headers**: `Authorization: Bearer <buyer_token>`

**Request Body**:
```json
{
  "transaction": {
    "seller_id": 456,
    "external_store_name": "Facebook Marketplace",
    "external_product_url": "https://facebook.com/marketplace/item/123456",
    "product_name": "iPhone 15 Pro - 256GB",
    "product_description": "Brand new iPhone 15 Pro in Natural Titanium color",
    "base_amount": 5500.00,
    "currency": "GHS",
    "shipping_address": "123 Main St, Accra, Ghana",
    "notes": "Please handle with care"
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "message": "External transaction created successfully. Waiting for seller acceptance.",
  "transaction": {
    "id": 1,
    "transaction_reference": "EXT_20240115_A1B2C3D4",
    "status": "pending_seller_acceptance",
    "external_store_name": "Facebook Marketplace",
    "external_product_url": "https://facebook.com/marketplace/item/123456",
    "product_name": "iPhone 15 Pro - 256GB",
    "base_amount": 5500.00,
    "escrow_fee": 275.00,
    "total_amount": 5775.00,
    "currency": "GHS",
    "buyer": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "seller": {
      "id": 456,
      "name": "Tech Store Ghana",
      "email": "<EMAIL>"
    },
    "created_at": "2024-01-15T10:30:00Z"
  },
  "next_steps": {
    "buyer": "Wait for seller to accept the transaction",
    "seller": "Seller will receive notification to accept or decline"
  }
}
```

### 2. Accept External Transaction (Seller)

**Endpoint**: `PATCH /api/v1/external_transactions/:id/accept_transaction`

**Headers**: `Authorization: Bearer <seller_token>`

**Request Body**:
```json
{
  "notes": "I confirm this is my product and I'm ready to proceed with the transaction"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Transaction accepted successfully",
  "transaction": {
    "id": 1,
    "status": "accepted_pending_payment",
    "seller_accepted_at": "2024-01-15T10:45:00Z",
    "seller_notes": "I confirm this is my product..."
  },
  "next_steps": {
    "buyer": "Please proceed to make payment to fund the escrow",
    "seller": "Wait for buyer to fund the escrow account"
  }
}
```

### 3. Fund Escrow (Buyer)

**Endpoint**: `POST /api/v1/external_transactions/:id/fund_escrow`

**Headers**: `Authorization: Bearer <buyer_token>`

**Request Body**:
```json
{
  "callback_url": "https://yourfrontend.com/external-transactions/payment-callback"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Payment initialized successfully",
  "payment": {
    "reference": "EXT_20240115_A1B2C3D4_PAY",
    "authorization_url": "https://checkout.paystack.com/abc123def456",
    "amount": 5775.00,
    "currency": "GHS"
  },
  "transaction": {
    "id": 1,
    "status": "accepted_pending_payment",
    "payment_reference": "EXT_20240115_A1B2C3D4_PAY",
    "payment_url": "https://checkout.paystack.com/abc123def456"
  }
}
```

### 4. Confirm Delivery (Buyer)

**Endpoint**: `PATCH /api/v1/external_transactions/:id/confirm_delivery`

**Headers**: `Authorization: Bearer <buyer_token>`

**Request Body**:
```json
{
  "notes": "Product received in excellent condition. Very satisfied!"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "message": "Delivery confirmed and funds released to seller",
  "transaction": {
    "id": 1,
    "status": "completed",
    "delivery_confirmed_at": "2024-01-15T15:30:00Z",
    "buyer_notes": "Product received in excellent condition..."
  }
}
```

### 5. Get External Transactions

**Endpoint**: `GET /api/v1/external_transactions`

**Query Parameters**:
- `status`: Filter by status
- `start_date`: Start date filter (YYYY-MM-DD)
- `end_date`: End date filter (YYYY-MM-DD)
- `page`: Page number
- `per_page`: Items per page

**Response (200 OK)**:
```json
{
  "success": true,
  "transactions": [
    {
      "id": 1,
      "transaction_reference": "EXT_20240115_A1B2C3D4",
      "status": "completed",
      "external_store_name": "Facebook Marketplace",
      "product_name": "iPhone 15 Pro - 256GB",
      "base_amount": 5500.00,
      "escrow_fee": 275.00,
      "total_amount": 5775.00,
      "currency": "GHS",
      "buyer": {
        "id": 1,
        "name": "John Doe"
      },
      "seller": {
        "id": 456,
        "name": "Tech Store Ghana"
      },
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total_pages": 1,
    "total_count": 1
  }
}
```

### 6. Create Dispute for External Transaction

**Endpoint**: `POST /api/v1/external_transactions/:id/create_dispute`

**Headers**: `Authorization: Bearer <token>`

**Request Body**:
```json
{
  "reason": "Product not as described",
  "description": "The iPhone received is not the 256GB model as advertised. It's only 128GB and has visible scratches."
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "message": "Dispute created successfully",
  "dispute": {
    "id": 1,
    "reason": "Product not as described",
    "description": "The iPhone received is not the 256GB model...",
    "status": "open",
    "complainant": 1,
    "created_at": "2024-01-15T16:00:00Z"
  },
  "transaction": {
    "id": 1,
    "status": "disputed"
  }
}
```

### 7. Get External Transaction Statistics

**Endpoint**: `GET /api/v1/external_transactions/statistics`

**Headers**: `Authorization: Bearer <token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "statistics": {
    "total_transactions": 25,
    "completed_transactions": 20,
    "pending_transactions": 3,
    "disputed_transactions": 2,
    "total_amount_transacted": 125000.00,
    "total_fees_paid": 6250.00,
    "seller_earnings": 85000.00,
    "seller_rating": 4.8
  }
}
```

## 📦 Order Management (Internal Store)

### 1. Create Order (Buyer)

**Endpoint**: `POST /api/v1/orders`

**Headers**: `Authorization: Bearer <buyer_token>`

**Request Body**:
```json
{
  "order": {
    "items": [
      {
        "product_id": 1,
        "quantity": 2,
        "unit_price": 5500.00
      },
      {
        "product_id": 2,
        "quantity": 1,
        "unit_price": 4800.00
      }
    ],
    "shipping_address": {
      "street": "123 Main St",
      "city": "Accra",
      "region": "Greater Accra",
      "country": "Ghana",
      "postal_code": "GA-123-4567"
    },
    "notes": "Please handle with care"
  }
}
```

**Response (201 Created)**:
```json
{
  "success": true,
  "message": "Order created successfully",
  "order": {
    "id": 1,
    "order_number": "ORD-2024-001",
    "status": "pending_payment",
    "total_amount": 15800.00,
    "currency": "GHS",
    "items": [
      {
        "id": 1,
        "product": {
          "id": 1,
          "name": "iPhone 15 Pro",
          "image_url": "https://example.com/iphone.jpg"
        },
        "quantity": 2,
        "unit_price": 5500.00,
        "total_price": 11000.00
      },
      {
        "id": 2,
        "product": {
          "id": 2,
          "name": "Samsung Galaxy S24",
          "image_url": "https://example.com/samsung.jpg"
        },
        "quantity": 1,
        "unit_price": 4800.00,
        "total_price": 4800.00
      }
    ],
    "buyer": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "seller": {
      "id": 2,
      "name": "Tech Store Ghana"
    },
    "shipping_address": {
      "street": "123 Main St",
      "city": "Accra",
      "region": "Greater Accra",
      "country": "Ghana",
      "postal_code": "GA-123-4567"
    },
    "escrow": {
      "status": "pending",
      "release_date": null,
      "dispute_deadline": "2024-01-29T10:30:00Z"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Get User Orders

**Endpoint**: `GET /api/v1/orders`

**Query Parameters**:
- `status`: Filter by status (pending_payment, paid, processing, shipped, delivered, cancelled, disputed)
- `page`: Page number
- `per_page`: Items per page

**Response (200 OK)**:
```json
{
  "success": true,
  "orders": [
    {
      "id": 1,
      "order_number": "ORD-2024-001",
      "status": "paid",
      "total_amount": 15800.00,
      "currency": "GHS",
      "items_count": 2,
      "seller": {
        "id": 2,
        "name": "Tech Store Ghana"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T11:00:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total_pages": 1,
    "total_count": 1
  }
}
```

### 3. Get Single Order

**Endpoint**: `GET /api/v1/orders/:id`

**Response**: Same as create order response with full details

### 4. Update Order Status (Seller)

**Endpoint**: `PATCH /api/v1/orders/:id/status`

**Request Body**:
```json
{
  "status": "processing",
  "notes": "Order is being prepared for shipment"
}
```

### 5. Cancel Order

**Endpoint**: `PATCH /api/v1/orders/:id/cancel`

**Request Body**:
```json
{
  "reason": "Changed my mind",
  "refund_requested": true
}
```

## 💳 Payment Integration (Paystack)

### 1. Initialize Payment

**Endpoint**: `POST /api/v1/payments/initialize`

**Request Body**:
```json
{
  "order_id": 1,
  "payment_method": "card",
  "callback_url": "https://yourfrontend.com/payment/callback"
}
```

**Response (200 OK)**:
```json
{
  "success": true,
  "payment": {
    "id": 1,
    "reference": "PAY_2024_001_abc123",
    "amount": 15800.00,
    "currency": "GHS",
    "status": "pending",
    "payment_url": "https://checkout.paystack.com/abc123def456",
    "expires_at": "2024-01-15T11:30:00Z"
  },
  "redirect_url": "https://checkout.paystack.com/abc123def456"
}
```

### 2. Verify Payment

**Endpoint**: `GET /api/v1/payments/:reference/verify`

**Response (200 OK)**:
```json
{
  "success": true,
  "payment": {
    "id": 1,
    "reference": "PAY_2024_001_abc123",
    "amount": 15800.00,
    "currency": "GHS",
    "status": "successful",
    "gateway_response": "Successful",
    "paid_at": "2024-01-15T10:45:00Z",
    "fees": {
      "paystack_fee": 79.00,
      "platform_fee": 158.00,
      "total_fees": 237.00
    }
  },
  "order": {
    "id": 1,
    "status": "paid",
    "escrow_status": "funded"
  }
}
```

### 3. Get Payment History

**Endpoint**: `GET /api/v1/payments`

**Response (200 OK)**:
```json
{
  "success": true,
  "payments": [
    {
      "id": 1,
      "reference": "PAY_2024_001_abc123",
      "amount": 15800.00,
      "currency": "GHS",
      "status": "successful",
      "order": {
        "id": 1,
        "order_number": "ORD-2024-001"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "paid_at": "2024-01-15T10:45:00Z"
    }
  ],
  "pagination": {...}
}
```

### 4. Request Refund

**Endpoint**: `POST /api/v1/payments/:id/refund`

**Request Body**:
```json
{
  "reason": "Product not as described",
  "amount": 15800.00,
  "refund_type": "full"
}
```

## 📊 Dashboard APIs

### 1. Admin Dashboard Overview

**Endpoint**: `GET /api/v1/admin/dashboard/overview`

**Headers**: `Authorization: Bearer <admin_token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "dashboard": {
    "summary": {
      "total_users": 1250,
      "total_orders": 3420,
      "total_revenue": 2450000.00,
      "active_disputes": 12,
      "pending_verifications": 8
    },
    "growth_metrics": {
      "user_growth": 15.2,
      "order_growth": 23.8,
      "revenue_growth": 18.5
    },
    "recent_activities": [
      {
        "id": 1,
        "type": "new_order",
        "description": "New order #ORD-2024-001 created",
        "user": "John Doe",
        "amount": 15800.00,
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ],
    "top_products": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "sales_count": 45,
        "revenue": 247500.00
      }
    ],
    "system_health": {
      "status": "healthy",
      "uptime": 99.8,
      "response_time": 120
    }
  }
}
```

### 2. Seller Dashboard

**Endpoint**: `GET /api/v1/seller/dashboard/overview`

**Headers**: `Authorization: Bearer <seller_token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "dashboard": {
    "summary": {
      "total_products": 25,
      "total_orders": 156,
      "total_revenue": 125000.00,
      "pending_orders": 8,
      "low_stock_products": 3
    },
    "recent_orders": [
      {
        "id": 1,
        "order_number": "ORD-2024-001",
        "buyer": "John Doe",
        "total_amount": 15800.00,
        "status": "paid",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "top_products": [...],
    "revenue_chart": {
      "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
      "data": [12000, 15000, 18000, 22000, 25000]
    }
  }
}
```

### 3. Buyer Dashboard

**Endpoint**: `GET /api/v1/buyer/dashboard/overview`

**Headers**: `Authorization: Bearer <buyer_token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "dashboard": {
    "summary": {
      "total_orders": 12,
      "total_spent": 45600.00,
      "active_orders": 2,
      "completed_orders": 8,
      "favorite_products": 15
    },
    "recent_orders": [...],
    "order_status_distribution": {
      "pending": 1,
      "processing": 1,
      "shipped": 0,
      "delivered": 8,
      "cancelled": 2
    },
    "spending_chart": {
      "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
      "data": [5000, 8000, 12000, 15000, 18000]
    }
  }
}
```

## 💬 Real-time Features (WebSocket)

### 1. WebSocket Connection Setup

```javascript
// Frontend WebSocket connection
import ActionCable from '@rails/actioncable';

const cable = ActionCable.createConsumer('ws://localhost:3000/cable');

// Authentication with JWT
const subscription = cable.subscriptions.create(
  {
    channel: 'ChatChannel',
    conversation_id: 1
  },
  {
    connected() {
      console.log('Connected to chat channel');
    },
    
    disconnected() {
      console.log('Disconnected from chat channel');
    },
    
    received(data) {
      console.log('Received message:', data);
      // Handle incoming messages
      this.handleNewMessage(data);
    },
    
    handleNewMessage(message) {
      // Update your React state with new message
      setMessages(prevMessages => [...prevMessages, message]);
    }
  }
);
```

### 2. Send Chat Message

**Endpoint**: `POST /api/v1/chat/messages`

**Request Body**:
```json
{
  "message": {
    "conversation_id": 1,
    "content": "Hello, is this product still available?",
    "message_type": "text"
  }
}
```

**WebSocket Broadcast**:
```json
{
  "type": "new_message",
  "message": {
    "id": 1,
    "content": "Hello, is this product still available?",
    "message_type": "text",
    "sender": {
      "id": 1,
      "name": "John Doe",
      "avatar_url": "https://example.com/avatar.jpg"
    },
    "conversation_id": 1,
    "created_at": "2024-01-15T10:30:00Z",
    "read_receipts": []
  }
}
```

### 3. Real-time Notifications

**WebSocket Channel**: `NotificationChannel`

**Broadcast Format**:
```json
{
  "type": "notification",
  "notification": {
    "id": 1,
    "title": "Order Status Updated",
    "message": "Your order #ORD-2024-001 has been shipped",
    "category": "order_update",
    "priority": "medium",
    "data": {
      "order_id": 1,
      "new_status": "shipped"
    },
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

## 📁 File Management

### 1. Upload Files

**Endpoint**: `POST /api/v1/files/upload`

**Content-Type**: `multipart/form-data`

**Form Data**:
- `file`: File to upload
- `category`: File category (profile_images, product_images, documents, etc.)
- `description`: File description (optional)
- `tags`: Comma-separated tags (optional)

**Response (201 Created)**:
```json
{
  "success": true,
  "file": {
    "id": "uuid-123-456",
    "original_filename": "product-image.jpg",
    "filename": "product-image_20240115_103000_abc123.jpg",
    "file_type": "images",
    "content_type": "image/jpeg",
    "file_size": 2048576,
    "category": "product_images",
    "signed_url": "https://example.com/files/signed-url",
    "download_url": "/api/v1/files/uuid-123-456/download",
    "thumbnails": [
      {
        "name": "small",
        "width": 150,
        "height": 150,
        "url": "/thumbnails/uuid-123-456_small.jpg"
      },
      {
        "name": "medium",
        "width": 300,
        "height": 300,
        "url": "/thumbnails/uuid-123-456_medium.jpg"
      }
    ],
    "created_at": "2024-01-15T10:30:00Z"
  },
  "message": "File uploaded successfully"
}
```

### 2. Get File List

**Endpoint**: `GET /api/v1/files`

**Query Parameters**:
- `category`: Filter by category
- `file_type`: Filter by file type
- `search`: Search query
- `page`: Page number
- `per_page`: Items per page

**Response (200 OK)**:
```json
{
  "success": true,
  "files": [
    {
      "id": "uuid-123-456",
      "original_filename": "product-image.jpg",
      "file_type": "images",
      "file_size": 2048576,
      "category": "product_images",
      "signed_url": "https://example.com/files/signed-url",
      "thumbnail_url": "/thumbnails/uuid-123-456_medium.jpg",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {...}
}
```

## 🔧 Advanced Features

### 1. Two-Factor Authentication Setup

**Endpoint**: `POST /api/v1/security/setup_2fa`

**Response (200 OK)**:
```json
{
  "success": true,
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "secret": "JBSWY3DPEHPK3PXP",
  "backup_codes": [
    "12345678",
    "87654321",
    "11223344"
  ]
}
```

### 2. Enable 2FA

**Endpoint**: `POST /api/v1/security/enable_2fa`

**Request Body**:
```json
{
  "token": "123456"
}
```

### 3. Advanced Search

**Endpoint**: `GET /api/v1/search/advanced`

**Query Parameters**:
- `q`: Search query
- `type`: Search type (products, users, orders)
- `filters`: JSON encoded filters
- `sort`: Sort field
- `order`: Sort order (asc, desc)

**Response (200 OK)**:
```json
{
  "success": true,
  "results": {
    "products": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "price": 5500.00,
        "relevance_score": 0.95,
        "highlight": "iPhone 15 Pro with advanced features"
      }
    ],
    "total_results": 25,
    "search_time": 0.045,
    "suggestions": ["iphone 14", "iphone case", "apple watch"]
  }
}
```

### 4. Analytics Data

**Endpoint**: `GET /api/v1/analytics/business_intelligence`

**Headers**: `Authorization: Bearer <admin_token>`

**Response (200 OK)**:
```json
{
  "success": true,
  "analytics": {
    "kpis": {
      "total_revenue": 2450000.00,
      "total_orders": 3420,
      "average_order_value": 716.37,
      "customer_acquisition_cost": 45.50,
      "customer_lifetime_value": 1250.00
    },
    "revenue_analytics": {
      "monthly_revenue": [
        {"month": "2024-01", "revenue": 180000.00},
        {"month": "2024-02", "revenue": 220000.00}
      ],
      "revenue_by_category": [
        {"category": "electronics", "revenue": 1200000.00},
        {"category": "fashion", "revenue": 800000.00}
      ]
    },
    "forecasting": {
      "next_month_revenue": 280000.00,
      "confidence_interval": [250000.00, 310000.00],
      "growth_rate": 12.5
    }
  }
}
```

## ❌ Error Handling

### Standard Error Response Format

```json
{
  "success": false,
  "error": "Validation failed",
  "error_code": "VALIDATION_ERROR",
  "details": {
    "email": ["can't be blank", "is invalid"],
    "password": ["is too short (minimum is 8 characters)"]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### Common HTTP Status Codes

- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Error Categories

1. **Authentication Errors** (`AUTH_*`)
   - `AUTH_TOKEN_MISSING`: No authentication token provided
   - `AUTH_TOKEN_INVALID`: Invalid or expired token
   - `AUTH_INSUFFICIENT_PERMISSIONS`: User lacks required permissions

2. **Validation Errors** (`VALIDATION_*`)
   - `VALIDATION_FAILED`: General validation failure
   - `VALIDATION_REQUIRED_FIELD`: Required field missing
   - `VALIDATION_INVALID_FORMAT`: Invalid field format

3. **Payment Errors** (`PAYMENT_*`)
   - `PAYMENT_FAILED`: Payment processing failed
   - `PAYMENT_INSUFFICIENT_FUNDS`: Insufficient account balance
   - `PAYMENT_GATEWAY_ERROR`: External payment gateway error

4. **Business Logic Errors** (`BUSINESS_*`)
   - `BUSINESS_INSUFFICIENT_STOCK`: Product out of stock
   - `BUSINESS_ORDER_ALREADY_PAID`: Order already paid
   - `BUSINESS_ESCROW_RELEASE_FAILED`: Cannot release escrow funds

## 🚀 Frontend Integration Examples

### React Hooks for API Integration

```javascript
// useAuth.js - Authentication hook
import { useState, useEffect, createContext, useContext } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('authToken'));
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, [token]);

  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/v1/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        logout();
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user: { email, password }
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setToken(data.token);
        setUser(data.user);
        localStorage.setItem('authToken', data.token);
        return { success: true };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      return { success: false, error: 'Network error' };
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('authToken');
  };

  const value = {
    user,
    token,
    loading,
    login,
    logout,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

### API Service Layer

```javascript
// apiService.js - Centralized API service
class ApiService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const token = localStorage.getItem('authToken');
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Request failed');
      }
      
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Products
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/products?${queryString}`);
  }

  async getProduct(id) {
    return this.request(`/products/${id}`);
  }

  async createProduct(productData) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify({ product: productData })
    });
  }

  // Orders
  async createOrder(orderData) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify({ order: orderData })
    });
  }

  async getOrders(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/orders?${queryString}`);
  }

  // Payments
  async initializePayment(paymentData) {
    return this.request('/payments/initialize', {
      method: 'POST',
      body: JSON.stringify(paymentData)
    });
  }

  async verifyPayment(reference) {
    return this.request(`/payments/${reference}/verify`);
  }

  // File Upload
  async uploadFile(file, category = 'documents') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);

    return this.request('/files/upload', {
      method: 'POST',
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData
    });
  }
}

export default new ApiService();
```

### React Components Examples

```javascript
// ProductList.jsx - Product listing component
import React, { useState, useEffect } from 'react';
import ApiService from '../services/apiService';

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    page: 1,
    per_page: 20,
    search: '',
    category: ''
  });

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await ApiService.getProducts(filters);
      setProducts(data.products);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (loading) {
    return <div className="loading">Loading products...</div>;
  }

  return (
    <div className="product-list">
      <div className="search-bar">
        <input
          type="text"
          placeholder="Search products..."
          value={filters.search}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>
      
      <div className="products-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <img 
              src={product.images[0]?.url} 
              alt={product.name}
              className="product-image"
            />
            <h3>{product.name}</h3>
            <p className="price">GHS {product.price.toFixed(2)}</p>
            <p className="seller">by {product.seller.name}</p>
            <button className="btn-primary">View Details</button>
          </div>
        ))}
      </div>
      
      {pagination.total_pages > 1 && (
        <div className="pagination">
          {Array.from({ length: pagination.total_pages }, (_, i) => (
            <button
              key={i + 1}
              onClick={() => handlePageChange(i + 1)}
              className={pagination.current_page === i + 1 ? 'active' : ''}
            >
              {i + 1}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductList;
```

### WebSocket Integration

```javascript
// useWebSocket.js - WebSocket hook
import { useEffect, useRef } from 'react';
import ActionCable from '@rails/actioncable';
import { useAuth } from './useAuth';

export const useWebSocket = () => {
  const { token } = useAuth();
  const cableRef = useRef(null);

  useEffect(() => {
    if (token) {
      cableRef.current = ActionCable.createConsumer(
        `ws://localhost:3000/cable?token=${token}`
      );
    }

    return () => {
      if (cableRef.current) {
        cableRef.current.disconnect();
      }
    };
  }, [token]);

  const subscribeToChannel = (channelName, params, callbacks) => {
    if (!cableRef.current) return null;

    return cableRef.current.subscriptions.create(
      { channel: channelName, ...params },
      callbacks
    );
  };

  return { subscribeToChannel };
};

// Chat component using WebSocket
const ChatComponent = ({ conversationId }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const { subscribeToChannel } = useWebSocket();

  useEffect(() => {
    const subscription = subscribeToChannel(
      'ChatChannel',
      { conversation_id: conversationId },
      {
        received(data) {
          if (data.type === 'new_message') {
            setMessages(prev => [...prev, data.message]);
          }
        }
      }
    );

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [conversationId, subscribeToChannel]);

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      await ApiService.request('/chat/messages', {
        method: 'POST',
        body: JSON.stringify({
          message: {
            conversation_id: conversationId,
            content: newMessage,
            message_type: 'text'
          }
        })
      });
      setNewMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  return (
    <div className="chat-component">
      <div className="messages">
        {messages.map(message => (
          <div key={message.id} className="message">
            <strong>{message.sender.name}:</strong> {message.content}
          </div>
        ))}
      </div>
      <div className="message-input">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="Type a message..."
        />
        <button onClick={sendMessage}>Send</button>
      </div>
    </div>
  );
};
```

## 🔗 Quick Start Integration Checklist

### 1. Environment Setup
```bash
# Install dependencies
npm install @rails/actioncable

# Environment variables
REACT_APP_API_URL=http://localhost:3000/api/v1
REACT_APP_WS_URL=ws://localhost:3000/cable
```

### 2. Authentication Flow
1. Implement login/register forms
2. Store JWT token in localStorage
3. Add token to all API requests
4. Handle token expiration and refresh
5. Implement logout functionality

### 3. Core Features Implementation
1. Product listing and search
2. Shopping cart functionality
3. Order creation and tracking
4. Payment integration with Paystack
5. Real-time chat and notifications
6. File upload for product images
7. Dashboard for different user roles

### 4. Error Handling
1. Implement global error boundary
2. Handle API errors gracefully
3. Show user-friendly error messages
4. Implement retry mechanisms

### 5. Performance Optimization
1. Implement pagination for large lists
2. Use React.memo for expensive components
3. Implement image lazy loading
4. Cache API responses where appropriate

This comprehensive guide provides everything your React frontend needs to integrate seamlessly with the PayHold backend. The API is fully functional and ready for production use with proper error handling, authentication, and real-time features.

For any specific integration questions or additional endpoints, refer to the individual service files in the codebase or contact the backend development team.
