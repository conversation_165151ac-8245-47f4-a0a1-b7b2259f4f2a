export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Ghana phone number validation
export const isValidGhanaPhone = (phone) => {
  const cleaned = phone.replace(/\D/g, '');

  // Check for valid Ghana phone formats
  // Local: 0XXXXXXXXX (10 digits starting with 0)
  // International: 233XXXXXXXXX (12 digits starting with 233)
  if (cleaned.length === 10 && cleaned.startsWith('0')) {
    return true;
  }

  if (cleaned.length === 12 && cleaned.startsWith('233')) {
    return true;
  }

  return false;
};

// Password strength validation
export const validatePassword = (password) => {
  const errors = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Price validation
export const isValidPrice = (price) => {
  // handle strings like "1,234.56" or " 123.45 "
  const normalized = typeof price === 'string'
    ? price.replace(/[, ]+/g, '').trim()
    : price;

  const numPrice = Number(normalized);

  // ensure it's a finite number and positive
  return Number.isFinite(numPrice) && numPrice > 0;
};

// Required field validation
export const isRequired = (value) => value !== null && value !== undefined && value.toString().trim() !== '';
