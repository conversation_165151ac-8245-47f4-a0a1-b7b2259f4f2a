import React from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import { StarIcon, HeartIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import productAPI from '../../services/apiServices';

function ProductCard({ product }) {
  const [isFavorite, setIsFavorite] = React.useState(false);

  const handleToggleFavorite = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      if (isFavorite) {
        await productAPI.removeFavorite(product.id);
        toast.success('Removed from favorites');
      } else {
        await productAPI.addFavorite(product.id);
        toast.success('Added to favorites');
      }
      setIsFavorite(!isFavorite);
    } catch (error) {
      toast.error('Failed to update favorites');
    }
  };

  return (
    <Link to={`/products/${product.id}`} className="block">
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
        <div className="relative">
          <img
            src={product.images[0]?.url || '/placeholder-product.jpg'}
            alt={product.name}
            className="w-full h-48 object-cover"
          />
          <button
            type="button"
            onClick={handleToggleFavorite}
            className="absolute top-2 right-2 p-2 bg-white bg-opacity-80 rounded-full hover:bg-opacity-100 transition-opacity"
          >
            {isFavorite ? (
              <HeartIconSolid className="h-5 w-5 text-red-500" />
            ) : (
              <HeartIcon className="h-5 w-5 text-gray-600" />
            )}
          </button>
        </div>

        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
            {product.name}
          </h3>

          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl font-bold text-primary-600">
              GHS
              {' '}
              {product.price.toFixed(2)}
            </span>
            {product.average_rating && (
              <div className="flex items-center">
                <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600 ml-1">
                  {product.average_rating.toFixed(1)}
                  {' '}
                  (
                  {product.total_reviews}
                  )
                </span>
              </div>
            )}
          </div>

          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {product.description}
          </p>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">
              by
              {' '}
              {product.seller.name}
            </span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              product.stock_quantity > 0
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
            >
              {product.stock_quantity > 0 ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}

ProductCard.propTypes = {
  product: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    price: PropTypes.number.isRequired,
    images: PropTypes.arrayOf(
      PropTypes.shape({
        url: PropTypes.string,
      }),
    ).isRequired,
    average_rating: PropTypes.number,
    total_reviews: PropTypes.number,
    seller: PropTypes.shape({
      name: PropTypes.string.isRequired,
      // add other seller fields you use here
    }).isRequired,
    stock_quantity: PropTypes.number.isRequired,
  }).isRequired,
};

export default ProductCard;
