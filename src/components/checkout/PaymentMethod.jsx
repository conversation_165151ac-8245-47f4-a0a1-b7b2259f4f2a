import React, { useState } from 'react';
import { toast } from 'react-toastify';
import {
  CreditCardIcon,
  DevicePhoneMobileIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';

function PaymentMethod({ 
  selectedMethod, 
  onMethodSelect, 
  orderTotal,
  escrowFee,
  isLoading = false 
}) {
  const [showCardForm, setShowCardForm] = useState(false);
  const [cardDetails, setCardDetails] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });

  const paymentMethods = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, and other major cards',
      icon: CreditCardIcon,
      fees: 'No additional fees',
      processingTime: 'Instant',
      available: true
    },
    {
      id: 'mobile_money',
      name: 'Mobile Money',
      description: 'MTN, Vodafone, AirtelTigo',
      icon: DevicePhoneMobileIcon,
      fees: 'Standard network charges apply',
      processingTime: 'Instant',
      available: true
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      icon: BanknotesIcon,
      fees: 'Bank charges may apply',
      processingTime: '1-3 business days',
      available: false // Disabled for now
    }
  ];

  const handleMethodSelect = (method) => {
    if (!method.available) {
      toast.error('This payment method is currently unavailable');
      return;
    }

    onMethodSelect(method);
    
    if (method.id === 'card') {
      setShowCardForm(true);
    } else {
      setShowCardForm(false);
    }
  };

  const handleCardInputChange = (field, value) => {
    let formattedValue = value;

    // Format card number
    if (field === 'number') {
      formattedValue = value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim();
      if (formattedValue.length > 19) return; // Max 16 digits + 3 spaces
    }

    // Format expiry date
    if (field === 'expiry') {
      formattedValue = value.replace(/\D/g, '').replace(/(\d{2})(\d)/, '$1/$2');
      if (formattedValue.length > 5) return; // MM/YY format
    }

    // Format CVV
    if (field === 'cvv') {
      formattedValue = value.replace(/\D/g, '');
      if (formattedValue.length > 4) return; // Max 4 digits
    }

    setCardDetails(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const validateCardDetails = () => {
    const { number, expiry, cvv, name } = cardDetails;
    
    if (!name.trim()) {
      toast.error('Please enter cardholder name');
      return false;
    }

    if (!number.replace(/\s/g, '') || number.replace(/\s/g, '').length < 13) {
      toast.error('Please enter a valid card number');
      return false;
    }

    if (!expiry || expiry.length !== 5) {
      toast.error('Please enter a valid expiry date (MM/YY)');
      return false;
    }

    if (!cvv || cvv.length < 3) {
      toast.error('Please enter a valid CVV');
      return false;
    }

    // Validate expiry date
    const [month, year] = expiry.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    if (parseInt(month) < 1 || parseInt(month) > 12) {
      toast.error('Please enter a valid month (01-12)');
      return false;
    }

    if (parseInt(year) < currentYear || (parseInt(year) === currentYear && parseInt(month) < currentMonth)) {
      toast.error('Card has expired');
      return false;
    }

    return true;
  };

  const getCardType = (number) => {
    const cleanNumber = number.replace(/\s/g, '');
    
    if (cleanNumber.startsWith('4')) return 'visa';
    if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard';
    if (cleanNumber.startsWith('3')) return 'amex';
    
    return 'unknown';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <CreditCardIcon className="h-5 w-5 text-gray-500" />
        <h2 className="text-lg font-medium text-gray-900">Payment Method</h2>
      </div>

      {/* Payment Methods */}
      <div className="space-y-3">
        {paymentMethods.map((method) => {
          const Icon = method.icon;
          const isSelected = selectedMethod?.id === method.id;
          
          return (
            <div
              key={method.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : method.available
                  ? 'border-gray-200 hover:border-gray-300'
                  : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
              }`}
              onClick={() => handleMethodSelect(method)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {isSelected ? (
                      <CheckCircleIconSolid className="h-5 w-5 text-blue-500" />
                    ) : (
                      <div className="h-5 w-5 border-2 border-gray-300 rounded-full"></div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <Icon className="h-5 w-5 text-gray-500" />
                      <span className="text-sm font-medium text-gray-900">
                        {method.name}
                      </span>
                      {!method.available && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                          Coming Soon
                        </span>
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      {method.description}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Fees: {method.fees}</span>
                      <span>•</span>
                      <span>Processing: {method.processingTime}</span>
                    </div>
                  </div>
                </div>

                {method.available && (
                  <div className="flex items-center space-x-2">
                    <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                    <span className="text-xs text-green-600">Secure</span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Card Details Form */}
      {showCardForm && selectedMethod?.id === 'card' && (
        <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Card Details</h3>
          
          <div className="space-y-4">
            {/* Cardholder Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cardholder Name *
              </label>
              <input
                type="text"
                value={cardDetails.name}
                onChange={(e) => handleCardInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter name as it appears on card"
              />
            </div>

            {/* Card Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Card Number *
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={cardDetails.number}
                  onChange={(e) => handleCardInputChange('number', e.target.value)}
                  className="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="1234 5678 9012 3456"
                />
                {cardDetails.number && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {getCardType(cardDetails.number) === 'visa' && (
                      <div className="text-xs font-bold text-blue-600">VISA</div>
                    )}
                    {getCardType(cardDetails.number) === 'mastercard' && (
                      <div className="text-xs font-bold text-red-600">MC</div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Expiry and CVV */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expiry Date *
                </label>
                <input
                  type="text"
                  value={cardDetails.expiry}
                  onChange={(e) => handleCardInputChange('expiry', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="MM/YY"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CVV *
                </label>
                <input
                  type="text"
                  value={cardDetails.cvv}
                  onChange={(e) => handleCardInputChange('cvv', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="123"
                />
              </div>
            </div>

            {/* Security Notice */}
            <div className="flex items-start space-x-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <ShieldCheckIcon className="h-4 w-4 text-green-500 mt-0.5" />
              <div className="text-xs text-green-800">
                <p className="font-medium mb-1">Your payment is secure</p>
                <p>We use industry-standard encryption to protect your card details. Your information is never stored on our servers.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Money Instructions */}
      {selectedMethod?.id === 'mobile_money' && (
        <div className="border border-gray-200 rounded-lg p-6 bg-blue-50">
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
            <div className="text-sm text-blue-800">
              <h4 className="font-medium mb-2">Mobile Money Payment Instructions</h4>
              <ol className="list-decimal list-inside space-y-1">
                <li>You'll receive a payment prompt on your phone</li>
                <li>Enter your Mobile Money PIN to authorize the payment</li>
                <li>You'll receive a confirmation SMS once payment is successful</li>
                <li>Your order will be processed immediately after payment</li>
              </ol>
              <p className="mt-2 text-xs">
                Supported networks: MTN Mobile Money, Vodafone Cash, AirtelTigo Money
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Payment Summary */}
      {selectedMethod && (
        <div className="border border-gray-200 rounded-lg p-4 bg-white">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Payment Summary</h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Order Total</span>
              <span className="text-gray-900">GH₵ {orderTotal.toFixed(2)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Escrow Fee (5%)</span>
              <span className="text-gray-900">GH₵ {escrowFee.toFixed(2)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Method Fee</span>
              <span className="text-gray-900">GH₵ 0.00</span>
            </div>
            
            <div className="border-t border-gray-200 pt-2">
              <div className="flex justify-between font-medium">
                <span className="text-gray-900">Total to Pay</span>
                <span className="text-blue-600">GH₵ {(orderTotal + escrowFee).toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Validation Error */}
      {selectedMethod?.id === 'card' && showCardForm && (
        <div className="flex items-center space-x-2">
          <button
            onClick={validateCardDetails}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Validate Card Details
          </button>
        </div>
      )}
    </div>
  );
}

export default PaymentMethod;
