import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
  ArrowPathIcon,
  CalendarIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  SparklesIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';
import { userAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function SubscriptionManagement({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(false);
  const [upgrading, setUpgrading] = useState(false);
  const [canceling, setCanceling] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  useEffect(() => {
    if (user) {
      fetchSubscription();
    }
  }, [user]);

  const fetchSubscription = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getSubscription();
      
      if (response.data.success) {
        setSubscription(response.data.subscription);
      }
    } catch (error) {
      logger.error('Failed to fetch subscription:', error);
      toast.error('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId) => {
    try {
      setUpgrading(true);
      
      const response = await userAPI.upgradeSubscription(planId);
      
      if (response.data.success) {
        // Redirect to payment page or show success
        if (response.data.payment_url) {
          window.location.href = response.data.payment_url;
        } else {
          toast.success('Subscription upgraded successfully!');
          fetchSubscription();
          
          // Update user role in Redux
          dispatch({ 
            type: 'auth/updateUser', 
            payload: { ...user, role: response.data.new_role }
          });
        }
      }
    } catch (error) {
      logger.error('Failed to upgrade subscription:', error);
      toast.error('Failed to upgrade subscription');
    } finally {
      setUpgrading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setCanceling(true);
      
      const response = await userAPI.cancelSubscription();
      
      if (response.data.success) {
        toast.success('Subscription canceled successfully');
        setShowCancelConfirm(false);
        fetchSubscription();
      }
    } catch (error) {
      logger.error('Failed to cancel subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setCanceling(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIconSolid className="h-3 w-3 mr-1" />
            Active
          </span>
        );
      case 'canceled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="h-3 w-3 mr-1" />
            Canceled
          </span>
        );
      case 'past_due':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ArrowPathIcon className="h-3 w-3 mr-1" />
            Past Due
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Unknown
          </span>
        );
    }
  };

  const subscriptionPlans = [
    {
      id: 'basic',
      name: 'Basic',
      role: 'buyer',
      price: 0,
      period: 'month',
      description: 'Perfect for buyers',
      features: [
        'Browse and purchase products',
        'Basic escrow protection',
        'Standard customer support',
        'Basic chat functionality'
      ],
      icon: ShieldCheckIcon,
      color: 'gray'
    },
    {
      id: 'seller',
      name: 'Seller',
      role: 'seller',
      price: 29,
      period: 'month',
      description: 'For individual sellers',
      features: [
        'List up to 100 products',
        'Advanced escrow protection',
        'Priority customer support',
        'Advanced chat features',
        'Basic analytics dashboard',
        'Standard transaction fees'
      ],
      icon: StarIcon,
      color: 'blue'
    },
    {
      id: 'premium',
      name: 'Premium Seller',
      role: 'premium_seller',
      price: 99,
      period: 'month',
      description: 'For professional sellers',
      features: [
        'Unlimited product listings',
        'Premium escrow protection',
        'Priority support & dedicated manager',
        'Advanced chat & video calls',
        'Comprehensive analytics',
        'Reduced transaction fees',
        'Custom branding options',
        'Bulk operations',
        'API access'
      ],
      icon: TrophyIcon,
      color: 'yellow',
      popular: true
    }
  ];

  const currentPlan = subscriptionPlans.find(plan => plan.role === user?.role) || subscriptionPlans[0];

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CreditCardIcon className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Subscription</h2>
              <p className="text-gray-600 mt-1">Manage your subscription and billing</p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Subscription */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Current Plan</h3>
        
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className={`p-3 rounded-lg ${
                currentPlan.color === 'yellow' ? 'bg-yellow-100' :
                currentPlan.color === 'blue' ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                <currentPlan.icon className={`h-6 w-6 ${
                  currentPlan.color === 'yellow' ? 'text-yellow-600' :
                  currentPlan.color === 'blue' ? 'text-blue-600' : 'text-gray-600'
                }`} />
              </div>
              
              <div>
                <div className="flex items-center space-x-2">
                  <h4 className="text-xl font-semibold text-gray-900">{currentPlan.name}</h4>
                  {subscription?.status && getStatusBadge(subscription.status)}
                </div>
                <p className="text-gray-600 mt-1">{currentPlan.description}</p>
                
                {subscription && (
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    {subscription.current_period_end && (
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="h-4 w-4" />
                        <span>
                          {subscription.status === 'canceled' ? 'Expires' : 'Renews'} on {formatDate(subscription.current_period_end)}
                        </span>
                      </div>
                    )}
                    
                    {subscription.amount && (
                      <div className="flex items-center space-x-1">
                        <BanknotesIcon className="h-4 w-4" />
                        <span>GH₵{subscription.amount}/{subscription.interval}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {currentPlan.price === 0 ? 'Free' : `GH₵${currentPlan.price}`}
              </div>
              {currentPlan.price > 0 && (
                <div className="text-sm text-gray-500">per {currentPlan.period}</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Available Plans */}
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Available Plans</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {subscriptionPlans.map((plan) => {
            const Icon = plan.icon;
            const isCurrentPlan = plan.role === user?.role;
            const canUpgrade = subscriptionPlans.findIndex(p => p.role === user?.role) < subscriptionPlans.findIndex(p => p.id === plan.id);
            
            return (
              <div
                key={plan.id}
                className={`relative rounded-lg border-2 p-6 ${
                  plan.popular 
                    ? 'border-yellow-500 bg-yellow-50' 
                    : isCurrentPlan
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-500 text-white">
                      <SparklesIcon className="h-3 w-3 mr-1" />
                      Most Popular
                    </span>
                  </div>
                )}
                
                {isCurrentPlan && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                      <CheckCircleIconSolid className="h-3 w-3 mr-1" />
                      Current Plan
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <div className={`inline-flex p-3 rounded-lg mb-4 ${
                    plan.color === 'yellow' ? 'bg-yellow-100' :
                    plan.color === 'blue' ? 'bg-blue-100' : 'bg-gray-100'
                  }`}>
                    <Icon className={`h-8 w-8 ${
                      plan.color === 'yellow' ? 'text-yellow-600' :
                      plan.color === 'blue' ? 'text-blue-600' : 'text-gray-600'
                    }`} />
                  </div>
                  
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h4>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">
                      {plan.price === 0 ? 'Free' : `GH₵${plan.price}`}
                    </span>
                    {plan.price > 0 && (
                      <span className="text-gray-500">/{plan.period}</span>
                    )}
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-auto">
                  {isCurrentPlan ? (
                    <div className="space-y-2">
                      <button
                        disabled
                        className="w-full px-4 py-2 bg-gray-100 text-gray-500 text-sm font-medium rounded-md cursor-not-allowed"
                      >
                        Current Plan
                      </button>
                      
                      {subscription?.status === 'active' && plan.price > 0 && (
                        <button
                          onClick={() => setShowCancelConfirm(true)}
                          className="w-full px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                        >
                          Cancel Subscription
                        </button>
                      )}
                    </div>
                  ) : canUpgrade ? (
                    <button
                      onClick={() => handleUpgrade(plan.id)}
                      disabled={upgrading}
                      className={`w-full px-4 py-2 text-sm font-medium rounded-md text-white ${
                        plan.popular
                          ? 'bg-yellow-600 hover:bg-yellow-700'
                          : 'bg-blue-600 hover:bg-blue-700'
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      {upgrading ? 'Processing...' : `Upgrade to ${plan.name}`}
                    </button>
                  ) : (
                    <button
                      disabled
                      className="w-full px-4 py-2 bg-gray-100 text-gray-500 text-sm font-medium rounded-md cursor-not-allowed"
                    >
                      Downgrade Not Available
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Cancel Confirmation Modal */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Cancel Subscription</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your current billing period.
            </p>
            
            <div className="flex space-x-3">
              <button
                onClick={handleCancelSubscription}
                disabled={canceling}
                className="flex-1 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {canceling ? 'Canceling...' : 'Yes, Cancel'}
              </button>
              
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Keep Subscription
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default SubscriptionManagement;
