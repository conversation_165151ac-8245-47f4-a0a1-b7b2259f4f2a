import React from 'react';
import {
  ArrowsPointingOutIcon,
  ArrowDownTrayIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

function ChartContainer({ 
  title, 
  subtitle,
  children, 
  loading = false,
  error = null,
  actions = [],
  className = '',
  height = 'h-80'
}) {
  const handleFullscreen = () => {
    // TODO: Implement fullscreen functionality
  };

  const handleExport = () => {
    // TODO: Implement export functionality
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-6 bg-gray-200 rounded w-32"></div>
            <div className="flex space-x-2">
              <div className="h-8 w-8 bg-gray-200 rounded"></div>
              <div className="h-8 w-8 bg-gray-200 rounded"></div>
            </div>
          </div>
          <div className={`bg-gray-200 rounded ${height}`}></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className={`flex items-center justify-center ${height} bg-red-50 border border-red-200 rounded-lg`}>
          <div className="text-center">
            <InformationCircleIcon className="mx-auto h-12 w-12 text-red-400 mb-4" />
            <h4 className="text-lg font-medium text-red-900 mb-2">Failed to Load Chart</h4>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Custom Actions */}
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors duration-200"
              title={action.title}
            >
              <action.icon className="h-5 w-5" />
            </button>
          ))}
          
          {/* Default Actions */}
          <button
            onClick={handleExport}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors duration-200"
            title="Export chart"
          >
            <ArrowDownTrayIcon className="h-5 w-5" />
          </button>
          
          <button
            onClick={handleFullscreen}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors duration-200"
            title="View fullscreen"
          >
            <ArrowsPointingOutIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Chart Content */}
      <div className={`${height} relative`}>
        {children}
      </div>
    </div>
  );
}

export default ChartContainer;
