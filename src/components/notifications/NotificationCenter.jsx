import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import {
  BellIcon,
  XMarkIcon,
  CheckIcon,
  TrashIcon,
  EllipsisVerticalIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ShoppingBagIcon,
  ChatBubbleLeftRightIcon,
  CreditCardIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';
import { notificationAPI } from '../../services/apiServices';
import { useSocket } from '../../hooks/useSocket';
import logger from '../helpers/logger';

function NotificationCenter({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const socket = useSocket();
  
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'transactions', 'messages', 'system'
  const [selectedNotifications, setSelectedNotifications] = useState(new Set());
  const [showActions, setShowActions] = useState(null);
  
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user]);

  useEffect(() => {
    if (socket) {
      socket.on('notification', handleNewNotification);
      socket.on('notification_read', handleNotificationRead);
      socket.on('notification_deleted', handleNotificationDeleted);

      return () => {
        socket.off('notification', handleNewNotification);
        socket.off('notification_read', handleNotificationRead);
        socket.off('notification_deleted', handleNotificationDeleted);
      };
    }
  }, [socket]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setShowActions(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await notificationAPI.getNotifications();
      
      if (response.data.success) {
        setNotifications(response.data.notifications);
        setUnreadCount(response.data.unread_count);
      }
    } catch (error) {
      logger.error('Failed to fetch notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleNewNotification = (data) => {
    setNotifications(prev => [data.notification, ...prev]);
    setUnreadCount(prev => prev + 1);
    
    // Show toast notification
    const { type, title, message } = data.notification;
    const toastOptions = {
      onClick: () => handleNotificationClick(data.notification)
    };

    switch (type) {
      case 'success':
        toast.success(message || title, toastOptions);
        break;
      case 'error':
        toast.error(message || title, toastOptions);
        break;
      case 'warning':
        toast.warning(message || title, toastOptions);
        break;
      default:
        toast.info(message || title, toastOptions);
    }
  };

  const handleNotificationRead = (data) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === data.notification_id 
        ? { ...notif, read_at: data.read_at }
        : notif
    ));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleNotificationDeleted = (data) => {
    setNotifications(prev => prev.filter(notif => notif.id !== data.notification_id));
    if (!data.was_read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const handleNotificationClick = async (notification) => {
    // Mark as read if unread
    if (!notification.read_at) {
      await markAsRead(notification.id);
    }

    // Navigate to action URL if available
    if (notification.action_url) {
      window.location.href = notification.action_url;
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await notificationAPI.markAsRead(notificationId);
      setNotifications(prev => prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read_at: new Date().toISOString() }
          : notif
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead();
      setNotifications(prev => prev.map(notif => ({
        ...notif,
        read_at: notif.read_at || new Date().toISOString()
      })));
      setUnreadCount(0);
      toast.success('All notifications marked as read');
    } catch (error) {
      logger.error('Failed to mark all as read:', error);
      toast.error('Failed to mark all as read');
    }
  };

  const deleteNotification = async (notificationId) => {
    try {
      await notificationAPI.deleteNotification(notificationId);
      const notification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
      
      if (notification && !notification.read_at) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      logger.error('Failed to delete notification:', error);
      toast.error('Failed to delete notification');
    }
  };

  const deleteSelected = async () => {
    if (selectedNotifications.size === 0) return;

    try {
      await notificationAPI.deleteMultiple(Array.from(selectedNotifications));
      
      const deletedUnreadCount = notifications
        .filter(n => selectedNotifications.has(n.id) && !n.read_at)
        .length;
      
      setNotifications(prev => prev.filter(notif => !selectedNotifications.has(notif.id)));
      setUnreadCount(prev => Math.max(0, prev - deletedUnreadCount));
      setSelectedNotifications(new Set());
      
      toast.success(`${selectedNotifications.size} notifications deleted`);
    } catch (error) {
      logger.error('Failed to delete notifications:', error);
      toast.error('Failed to delete notifications');
    }
  };

  const getNotificationIcon = (type, category) => {
    const iconClass = "h-5 w-5";
    
    switch (category) {
      case 'transaction':
        return <CreditCardIcon className={`${iconClass} text-green-500`} />;
      case 'order':
        return <ShoppingBagIcon className={`${iconClass} text-blue-500`} />;
      case 'message':
        return <ChatBubbleLeftRightIcon className={`${iconClass} text-purple-500`} />;
      case 'user':
        return <UserIcon className={`${iconClass} text-gray-500`} />;
      default:
        switch (type) {
          case 'success':
            return <CheckCircleIcon className={`${iconClass} text-green-500`} />;
          case 'error':
            return <ExclamationTriangleIcon className={`${iconClass} text-red-500`} />;
          case 'warning':
            return <ExclamationTriangleIcon className={`${iconClass} text-yellow-500`} />;
          default:
            return <InformationCircleIcon className={`${iconClass} text-blue-500`} />;
        }
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = (now - date) / (1000 * 60);

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${Math.floor(diffInMinutes)}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const getFilteredNotifications = () => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.read_at);
      case 'transactions':
        return notifications.filter(n => n.category === 'transaction');
      case 'messages':
        return notifications.filter(n => n.category === 'message');
      case 'system':
        return notifications.filter(n => n.category === 'system');
      default:
        return notifications;
    }
  };

  const filteredNotifications = getFilteredNotifications();

  if (!user) {
    return null;
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
        title="Notifications"
      >
        {unreadCount > 0 ? (
          <BellIconSolid className="h-6 w-6 text-blue-600" />
        ) : (
          <BellIcon className="h-6 w-6" />
        )}
        
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-md p-1">
              {[
                { key: 'all', label: 'All' },
                { key: 'unread', label: 'Unread' },
                { key: 'transactions', label: 'Transactions' },
                { key: 'messages', label: 'Messages' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key)}
                  className={`flex-1 px-3 py-1 text-xs font-medium rounded transition-colors ${
                    filter === tab.key
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Actions */}
            {(unreadCount > 0 || selectedNotifications.size > 0) && (
              <div className="flex items-center justify-between mt-3">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Mark all as read
                  </button>
                )}
                
                {selectedNotifications.size > 0 && (
                  <button
                    onClick={deleteSelected}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Delete selected ({selectedNotifications.size})
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">Loading notifications...</p>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <BellIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-sm font-medium text-gray-900 mb-2">No notifications</h3>
                <p className="text-sm text-gray-500">
                  {filter === 'unread' 
                    ? "You're all caught up!"
                    : "You'll see notifications here when you have them."
                  }
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                      !notification.read_at ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.has(notification.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          const newSelected = new Set(selectedNotifications);
                          if (e.target.checked) {
                            newSelected.add(notification.id);
                          } else {
                            newSelected.delete(notification.id);
                          }
                          setSelectedNotifications(newSelected);
                        }}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type, notification.category)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={`text-sm ${
                              !notification.read_at ? 'font-semibold text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </p>
                            {notification.message && (
                              <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                                {notification.message}
                              </p>
                            )}
                            <p className="text-xs text-gray-400 mt-2">
                              {formatTime(notification.created_at)}
                            </p>
                          </div>
                          
                          <div className="flex items-center space-x-1 ml-2">
                            {!notification.read_at && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                            
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowActions(showActions === notification.id ? null : notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <EllipsisVerticalIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>

                        {/* Actions Dropdown */}
                        {showActions === notification.id && (
                          <div className="absolute right-4 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                            {!notification.read_at && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  markAsRead(notification.id);
                                  setShowActions(null);
                                }}
                                className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <CheckIcon className="h-4 w-4 mr-2" />
                                Mark as read
                              </button>
                            )}
                            
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteNotification(notification.id);
                                setShowActions(null);
                              }}
                              className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                              <TrashIcon className="h-4 w-4 mr-2" />
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default NotificationCenter;
