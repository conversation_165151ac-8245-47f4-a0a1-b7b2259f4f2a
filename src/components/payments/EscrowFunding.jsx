import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  ShieldCheckIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  BanknotesIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import { externalTransactionAPI, paymentAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';

function EscrowFunding() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('card');

  useEffect(() => {
    fetchTransaction();
  }, [id]);

  const fetchTransaction = async () => {
    try {
      setLoading(true);
      const response = await externalTransactionAPI.getTransaction(id);

      if (response.data.success) {
        const txn = response.data.transaction;

        // Verify this is the buyer and transaction is in correct state
        if (txn.buyer_id !== user?.id) {
          throw new Error('You are not authorized to fund this transaction');
        }

        if (txn.status !== 'accepted') {
          throw new Error('This transaction cannot be funded at this time');
        }

        setTransaction(txn);
        setError(null);
      } else {
        throw new Error(response.data.error || 'Transaction not found');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFundEscrow = async () => {
    try {
      setProcessing(true);

      const paymentData = {
        transaction_id: transaction.id,
        amount: transaction.total_amount,
        currency: transaction.currency || 'GHS',
        payment_method: paymentMethod,
        callback_url: `${window.location.origin}/payment/callback`,
        metadata: {
          transaction_type: 'external_escrow',
          transaction_id: transaction.id,
          buyer_id: user.id,
          seller_id: transaction.seller_id,
        },
      };

      const response = await paymentAPI.initializePayment(paymentData);

      if (response.data.success) {
        // Store payment reference for callback handling
        localStorage.setItem('pending_payment', JSON.stringify({
          transaction_id: transaction.id,
          payment_reference: response.data.payment.reference,
          return_url: `/transactions/${transaction.id}`,
        }));

        // Redirect to payment gateway
        window.location.href = response.data.payment.authorization_url;
      } else {
        throw new Error(response.data.error || 'Failed to initialize payment');
      }
    } catch (err) {
      toast.error(err.message || 'Failed to initialize payment');
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Unable to load transaction</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return null;
  }

  const paymentMethods = [
    {
      id: 'card',
      name: 'Debit/Credit Card',
      description: 'Pay with your Visa, Mastercard, or Verve card',
      icon: CreditCardIcon,
      available: true,
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      description: 'Direct bank transfer (may take 1-3 business days)',
      icon: BanknotesIcon,
      available: true,
    },
    {
      id: 'mobile_money',
      name: 'Mobile Money',
      description: 'Pay with MTN Mobile Money or AirtelTigo Money',
      icon: CurrencyDollarIcon,
      available: true,
    },
  ];

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    Fund Escrow Account
                  </h1>
                  <p className="text-sm text-gray-600">
                    Secure your purchase with escrow protection
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Transaction Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h2 className="text-lg font-medium text-gray-900 mb-3">Transaction Summary</h2>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Product:</span>
                <span className="font-medium text-gray-900">{transaction.product_name}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Seller:</span>
                <span className="font-medium text-gray-900">
                  {transaction.seller?.first_name}
                  {' '}
                  {transaction.seller?.last_name}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Product Amount:</span>
                <span className="font-medium text-gray-900">
                  GH₵
                  {transaction.base_amount?.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Escrow Fee (5%):</span>
                <span className="font-medium text-gray-900">
                  GH₵
                  {transaction.escrow_fee?.toFixed(2)}
                </span>
              </div>
              <div className="border-t border-gray-300 pt-2 flex justify-between">
                <span className="font-semibold text-gray-900">Total to Pay:</span>
                <span className="font-bold text-green-600 text-lg">
                  GH₵
                  {transaction.total_amount?.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Escrow Protection Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <ShieldCheckIcon className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-1">Escrow Protection</h3>
                <p className="text-sm text-blue-800">
                  Your payment will be held securely in escrow until you confirm delivery.
                  The seller will only receive payment after you're satisfied with your purchase.
                </p>
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Select Payment Method</h2>
            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <label
                  key={method.id}
                  className={`relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                    paymentMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300'
                  } ${!method.available ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method.id}
                    checked={paymentMethod === method.id}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    disabled={!method.available}
                    className="sr-only"
                  />
                  <div className="flex items-center space-x-3 flex-1">
                    <div className={`p-2 rounded-lg ${
                      paymentMethod === method.id ? 'bg-blue-100' : 'bg-gray-100'
                    }`}
                    >
                      <method.icon className={`h-5 w-5 ${
                        paymentMethod === method.id ? 'text-blue-600' : 'text-gray-600'
                      }`}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="font-medium text-gray-900">{method.name}</span>
                        {!method.available && (
                          <span className="ml-2 text-xs text-gray-500">(Coming Soon)</span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                  </div>
                  {paymentMethod === method.id && (
                    <CheckCircleIcon className="h-5 w-5 text-blue-600" />
                  )}
                </label>
              ))}
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-900 mb-1">Security Notice</h3>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Your payment is processed securely through Paystack</li>
                  <li>• Funds are held in escrow until delivery confirmation</li>
                  <li>• You can dispute the transaction if there are issues</li>
                  <li>• Never share your payment details outside this platform</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <button
              onClick={() => navigate(`/transactions/${transaction.id}`)}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
            <button
              onClick={handleFundEscrow}
              disabled={processing || !paymentMethod}
              className="flex-1 px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {processing ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <ShieldCheckIcon className="h-5 w-5 mr-2" />
                  Fund Escrow - GH₵
                  {' '}
                  {transaction.total_amount?.toFixed(2)}
                </>
              )}
            </button>
          </div>

          {/* Terms */}
          <div className="text-xs text-gray-500 text-center">
            By proceeding, you agree to our
            {' '}
            <a href="/terms" className="text-blue-600 hover:text-blue-800">Terms of Service</a>
            {' '}
            and
            {' '}
            <a href="/privacy" className="text-blue-600 hover:text-blue-800">Privacy Policy</a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EscrowFunding;
