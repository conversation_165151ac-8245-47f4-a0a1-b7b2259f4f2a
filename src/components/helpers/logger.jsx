// Simple logger that respects environment
const logger = {
  log: (message, ...args) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(message, ...args);
    }
  },
  error: (message, ...args) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.error(message, ...args);
    }
    // In production, you might want to send errors to a monitoring service
    // e.g., Sentry, LogRocket, etc.
  },
  warn: (message, ...args) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(message, ...args);
    }
  },
  info: (message, ...args) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.info(message, ...args);
    }
  },
  debug: (message, ...args) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.debug(message, ...args);
    }
  },
};

export default logger;
