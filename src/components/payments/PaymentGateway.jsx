import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { paymentAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';
import logger from '../helpers/logger';

function PaymentGateway() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [paymentStatus, setPaymentStatus] = useState('processing');
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Get payment reference from URL params
  const reference = searchParams.get('reference');
  const status = searchParams.get('status');

  useEffect(() => {
    if (reference) {
      verifyPayment();
    } else {
      setError('No payment reference found');
      setPaymentStatus('failed');
    }
  }, [reference, retryCount]);

  const verifyPayment = async () => {
    try {
      setPaymentStatus('processing');
      setError(null);

      const response = await paymentAPI.verifyPayment(reference);
      
      if (response.data.success) {
        const payment = response.data.payment;
        setPaymentData(payment);

        if (payment.status === 'success') {
          setPaymentStatus('success');
          
          // Show success message
          toast.success('Payment successful! Your escrow has been funded.');
          
          // Clear any pending payment data
          localStorage.removeItem('pending_payment');
          
          // Redirect to transaction details after a delay
          setTimeout(() => {
            const returnUrl = getReturnUrl(payment);
            navigate(returnUrl);
          }, 3000);
          
        } else if (payment.status === 'failed') {
          setPaymentStatus('failed');
          setError(payment.gateway_response || 'Payment failed');
          
        } else if (payment.status === 'pending') {
          setPaymentStatus('pending');
          
          // Retry verification after a delay for pending payments
          if (retryCount < 5) {
            setTimeout(() => {
              setRetryCount(prev => prev + 1);
            }, 3000);
          } else {
            setPaymentStatus('timeout');
            setError('Payment verification timed out. Please check your transaction status.');
          }
        }
      } else {
        throw new Error(response.data.error || 'Payment verification failed');
      }
    } catch (err) {
      setError(err.message);
      setPaymentStatus('failed');
    }
  };

  const getReturnUrl = (payment) => {
    // Try to get return URL from stored pending payment data
    const pendingPayment = localStorage.getItem('pending_payment');
    if (pendingPayment) {
      try {
        const data = JSON.parse(pendingPayment);
        if (data.return_url) {
          return data.return_url;
        }
      } catch (e) {
        logger.error('Error parsing pending payment data:', e);
      }
    }

    // Fallback based on payment metadata
    if (payment.metadata?.transaction_id) {
      return `/transactions/${payment.metadata.transaction_id}`;
    }
    
    if (payment.metadata?.order_id) {
      return `/orders/${payment.metadata.order_id}`;
    }

    // Default fallback
    return '/dashboard';
  };

  const handleRetry = () => {
    setRetryCount(0);
    verifyPayment();
  };

  const handleGoBack = () => {
    const pendingPayment = localStorage.getItem('pending_payment');
    if (pendingPayment) {
      try {
        const data = JSON.parse(pendingPayment);
        if (data.transaction_id) {
          navigate(`/transactions/${data.transaction_id}`);
          return;
        }
      } catch (e) {
        logger.error('Error parsing pending payment data:', e);
      }
    }
    navigate('/dashboard');
  };

  const renderStatusContent = () => {
    switch (paymentStatus) {
      case 'processing':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
              <LoadingSpinner size="large" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Verifying Payment
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Please wait while we confirm your payment...
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <ClockIcon className="h-4 w-4" />
              <span>This may take a few moments</span>
            </div>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <CheckCircleIcon className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Payment Successful!
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Your payment has been processed successfully and your escrow account has been funded.
            </p>
            
            {paymentData && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount Paid:</span>
                    <span className="font-medium text-gray-900">
                      {paymentData.currency} {paymentData.amount?.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reference:</span>
                    <span className="font-medium text-gray-900">{paymentData.reference}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium text-gray-900">
                      {new Date(paymentData.paid_at).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            <p className="text-xs text-gray-500 mb-6">
              Redirecting you to your transaction details...
            </p>
            
            <button
              onClick={() => navigate(getReturnUrl(paymentData || {}))}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Continue to Transaction
            </button>
          </div>
        );

      case 'failed':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
              <XCircleIcon className="h-10 w-10 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Payment Failed
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {error || 'Your payment could not be processed. Please try again.'}
            </p>
            
            {paymentData && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reference:</span>
                    <span className="font-medium text-gray-900">{paymentData.reference}</span>
                  </div>
                  {paymentData.gateway_response && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Reason:</span>
                      <span className="font-medium text-red-800">{paymentData.gateway_response}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={handleRetry}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Retry Verification
              </button>
              <button
                onClick={handleGoBack}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go Back
              </button>
            </div>
          </div>
        );

      case 'pending':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
              <ClockIcon className="h-10 w-10 text-yellow-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Payment Pending
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Your payment is being processed. This may take a few minutes.
            </p>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p>We're still waiting for confirmation from your bank or payment provider.</p>
                  <p className="mt-1">You'll receive a notification once the payment is confirmed.</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mb-6">
              <LoadingSpinner size="small" />
              <span>Checking status... (Attempt {retryCount + 1}/5)</span>
            </div>
            
            <button
              onClick={handleGoBack}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Continue Later
            </button>
          </div>
        );

      case 'timeout':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
              <ClockIcon className="h-10 w-10 text-gray-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Verification Timeout
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              We couldn't verify your payment status at this time. Your payment may still be processing.
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="text-sm text-blue-800">
                <p>If your payment was successful, it will be reflected in your account shortly.</p>
                <p className="mt-1">You can check your transaction status in your dashboard.</p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={handleRetry}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Check Again
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {renderStatusContent()}
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-xs text-gray-500">
          Payments processed securely by{' '}
          <a href="https://paystack.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
            Paystack
          </a>
        </p>
      </div>
    </div>
  );
}

export default PaymentGateway;
