import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slice/authSlice';
import userSlice from './slice/userSlice';
import productSlice from './slice/productSlice';
import orderSlice from './slice/orderSlice';
import cartSlice from './slice/cartSlice';
import chatSlice from './slice/chatSlice';
import uiSlice from './slices/uiSlice';

const store = configureStore({
  reducer: {
    auth: authSlice,
    user: userSlice,
    products: productSlice,
    orders: orderSlice,
    cart: cartSlice,
    chat: chatSlice,
    ui: uiSlice,
  },
});

export default store;
