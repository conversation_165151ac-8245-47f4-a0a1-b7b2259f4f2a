import React from 'react';
import { Link } from 'react-router-dom';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  UserIcon,
  LinkIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

function TransactionCard({ transaction, userRole = 'buyer' }) {
  // Status configurations
  const statusConfig = {
    pending: {
      icon: ClockIcon,
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200',
      label: 'Pending Acceptance'
    },
    accepted: {
      icon: CheckCircleIcon,
      color: 'blue',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200',
      label: 'Accepted - Awaiting Payment'
    },
    funded: {
      icon: CurrencyDollarIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Funded - In Progress'
    },
    shipped: {
      icon: CheckCircleIcon,
      color: 'purple',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200',
      label: 'Shipped - Awaiting Delivery'
    },
    delivered: {
      icon: CheckCircleIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Delivered - Awaiting Confirmation'
    },
    completed: {
      icon: CheckCircleIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Completed'
    },
    cancelled: {
      icon: XCircleIcon,
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-800',
      borderColor: 'border-red-200',
      label: 'Cancelled'
    },
    disputed: {
      icon: ExclamationTriangleIcon,
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-800',
      borderColor: 'border-red-200',
      label: 'Disputed'
    }
  };

  const status = statusConfig[transaction.status] || statusConfig.pending;
  const StatusIcon = status.icon;

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get platform icon/name
  const getPlatformDisplay = (storeName) => {
    const platforms = {
      'Facebook': { name: 'Facebook Marketplace', color: 'text-blue-600' },
      'TikTok': { name: 'TikTok Shop', color: 'text-black' },
      'Instagram': { name: 'Instagram Shop', color: 'text-pink-600' },
      'WhatsApp': { name: 'WhatsApp Business', color: 'text-green-600' },
      'Telegram': { name: 'Telegram', color: 'text-blue-500' },
      'Twitter': { name: 'Twitter/X', color: 'text-black' },
      'LinkedIn': { name: 'LinkedIn', color: 'text-blue-700' },
      'Snapchat': { name: 'Snapchat', color: 'text-yellow-500' },
      'YouTube': { name: 'YouTube', color: 'text-red-600' },
      'Discord': { name: 'Discord', color: 'text-indigo-600' },
      'Reddit': { name: 'Reddit', color: 'text-orange-600' },
      'Pinterest': { name: 'Pinterest', color: 'text-red-500' },
      'Offline': { name: 'Offline Deal', color: 'text-gray-700' },
      'Phone': { name: 'Phone Call', color: 'text-green-700' },
      'InPerson': { name: 'In-Person Meeting', color: 'text-purple-600' },
      'Other': { name: 'External Platform', color: 'text-gray-600' }
    };

    // If it's not a predefined platform, treat it as a custom platform name
    if (!platforms[storeName]) {
      return { name: storeName, color: 'text-gray-600' };
    }

    return platforms[storeName];
  };

  const platform = getPlatformDisplay(transaction.external_store_name);

  // Get action buttons based on status and user role
  const getActionButtons = () => {
    const buttons = [];

    if (userRole === 'seller' && transaction.status === 'pending') {
      buttons.push(
        <Link
          key="accept"
          to={`/transactions/${transaction.id}/accept`}
          className="inline-flex items-center px-3 py-1.5 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Accept Transaction
        </Link>
      );
    }

    if (userRole === 'buyer' && transaction.status === 'accepted') {
      buttons.push(
        <Link
          key="fund"
          to={`/transactions/${transaction.id}/fund`}
          className="inline-flex items-center px-3 py-1.5 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Fund Escrow
        </Link>
      );
    }

    if (userRole === 'seller' && transaction.status === 'funded') {
      buttons.push(
        <Link
          key="ship"
          to={`/transactions/${transaction.id}/order-form`}
          className="inline-flex items-center px-3 py-1.5 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        >
          Submit Shipping
        </Link>
      );
    }

    if (userRole === 'buyer' && transaction.status === 'delivered') {
      buttons.push(
        <Link
          key="confirm"
          to={`/transactions/${transaction.id}/confirm`}
          className="inline-flex items-center px-3 py-1.5 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Confirm Delivery
        </Link>
      );
    }

    // View details button (always available)
    buttons.push(
      <Link
        key="view"
        to={`/transactions/${transaction.id}`}
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      >
        View Details
      </Link>
    );

    return buttons;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className={`p-2 rounded-lg ${status.bgColor}`}>
                <StatusIcon className={`h-5 w-5 ${status.textColor}`} />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {transaction.product_name}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <LinkIcon className="h-4 w-4 text-gray-400" />
                <span className={`text-sm font-medium ${platform.color}`}>
                  {platform.name}
                </span>
              </div>
            </div>
          </div>
          
          {/* Status Badge */}
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bgColor} ${status.textColor} ${status.borderColor} border`}>
            {status.label}
          </div>
        </div>

        {/* Transaction Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <CurrencyDollarIcon className="h-4 w-4 mr-2" />
              <span className="font-medium">Amount:</span>
              <span className="ml-1 font-semibold text-gray-900">
                GH₵ {transaction.total_amount?.toFixed(2)}
              </span>
            </div>
            
            <div className="flex items-center text-sm text-gray-600">
              <UserIcon className="h-4 w-4 mr-2" />
              <span className="font-medium">
                {userRole === 'buyer' ? 'Seller:' : 'Buyer:'}
              </span>
              <span className="ml-1">
                {userRole === 'buyer' 
                  ? `${transaction.seller?.first_name} ${transaction.seller?.last_name}`
                  : `${transaction.buyer?.first_name} ${transaction.buyer?.last_name}`
                }
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <span className="font-medium">Created:</span>
              <span className="ml-1">
                {formatDate(transaction.created_at)}
              </span>
            </div>
            
            {transaction.updated_at !== transaction.created_at && (
              <div className="flex items-center text-sm text-gray-600">
                <ClockIcon className="h-4 w-4 mr-2" />
                <span className="font-medium">Updated:</span>
                <span className="ml-1">
                  {formatDate(transaction.updated_at)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Product URL */}
        {transaction.external_product_url && (
          <div className="mb-4">
            <a
              href={transaction.external_product_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
            >
              <LinkIcon className="h-4 w-4 mr-1" />
              View Original Product
            </a>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
            <span>Progress</span>
            <span>{getProgressPercentage(transaction.status)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(transaction.status)}`}
              style={{ width: `${getProgressPercentage(transaction.status)}%` }}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          {getActionButtons()}
        </div>

        {/* Additional Info */}
        {transaction.notes && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <p className="text-sm text-gray-700">
              <span className="font-medium">Notes:</span> {transaction.notes}
            </p>
          </div>
        )}

        {/* Dispute Warning */}
        {transaction.status === 'disputed' && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
              <p className="text-sm text-red-800">
                This transaction is under dispute. Please check your notifications for updates.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper functions
function getProgressPercentage(status) {
  const progressMap = {
    pending: 10,
    accepted: 25,
    funded: 50,
    shipped: 75,
    delivered: 90,
    completed: 100,
    cancelled: 0,
    disputed: 60
  };
  return progressMap[status] || 0;
}

function getProgressColor(status) {
  if (status === 'completed') return 'bg-green-500';
  if (status === 'cancelled') return 'bg-red-500';
  if (status === 'disputed') return 'bg-red-500';
  return 'bg-blue-500';
}

export default TransactionCard;
