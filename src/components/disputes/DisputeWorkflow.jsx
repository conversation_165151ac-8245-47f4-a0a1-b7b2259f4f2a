import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  UserIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { disputeAPI } from '../../services/apiServices';
import logger from '../helpers/logger';

function DisputeWorkflow({ disputeId, className = '' }) {
  const { user } = useSelector((state) => state.auth);
  
  const [dispute, setDispute] = useState(null);
  const [workflow, setWorkflow] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (disputeId) {
      fetchDisputeWorkflow();
    }
  }, [disputeId]);

  const fetchDisputeWorkflow = async () => {
    try {
      setLoading(true);
      const response = await disputeAPI.getDispute(disputeId);
      
      if (response.data.success) {
        setDispute(response.data.dispute);
        generateWorkflow(response.data.dispute);
      }
    } catch (error) {
      logger.error('Failed to fetch dispute workflow:', error);
      toast.error('Failed to load dispute workflow');
    } finally {
      setLoading(false);
    }
  };

  const generateWorkflow = (disputeData) => {
    const steps = [
      {
        id: 'created',
        title: 'Dispute Created',
        description: 'Dispute was submitted by the user',
        status: 'completed',
        timestamp: disputeData.created_at,
        actor: disputeData.created_by_user,
        icon: ExclamationTriangleIcon,
        color: 'red'
      },
      {
        id: 'notification_sent',
        title: 'Parties Notified',
        description: 'Both parties have been notified of the dispute',
        status: 'completed',
        timestamp: disputeData.created_at,
        actor: { name: 'System', role: 'system' },
        icon: DocumentTextIcon,
        color: 'blue'
      }
    ];

    // Add response steps
    if (disputeData.responses && disputeData.responses.length > 0) {
      disputeData.responses.forEach((response, index) => {
        steps.push({
          id: `response_${index}`,
          title: `Response from ${response.user?.name || 'User'}`,
          description: response.message.substring(0, 100) + (response.message.length > 100 ? '...' : ''),
          status: 'completed',
          timestamp: response.created_at,
          actor: response.user,
          icon: UserIcon,
          color: 'gray'
        });
      });
    }

    // Add review step
    if (disputeData.status === 'in_review' || disputeData.status === 'resolved' || disputeData.status === 'closed') {
      steps.push({
        id: 'under_review',
        title: 'Under Review',
        description: 'Dispute is being reviewed by moderators',
        status: disputeData.status === 'open' ? 'pending' : 'completed',
        timestamp: disputeData.review_started_at || disputeData.updated_at,
        actor: disputeData.assigned_moderator || { name: 'Moderation Team', role: 'moderator' },
        icon: ShieldCheckIcon,
        color: 'blue'
      });
    }

    // Add resolution step
    if (disputeData.status === 'resolved') {
      steps.push({
        id: 'resolved',
        title: 'Dispute Resolved',
        description: disputeData.resolution?.notes || 'Dispute has been resolved',
        status: 'completed',
        timestamp: disputeData.resolved_at || disputeData.updated_at,
        actor: disputeData.resolved_by_user || { name: 'Moderator', role: 'moderator' },
        icon: CheckCircleIcon,
        color: 'green'
      });

      // Add refund step if applicable
      if (disputeData.resolution?.refund_amount) {
        steps.push({
          id: 'refund_processed',
          title: 'Refund Processed',
          description: `Refund of GH₵${disputeData.resolution.refund_amount} has been processed`,
          status: disputeData.refund_status === 'completed' ? 'completed' : 'pending',
          timestamp: disputeData.refund_processed_at,
          actor: { name: 'Payment System', role: 'system' },
          icon: BanknotesIcon,
          color: 'green'
        });
      }
    } else if (disputeData.status === 'closed') {
      steps.push({
        id: 'closed',
        title: 'Dispute Closed',
        description: 'Dispute has been closed without resolution',
        status: 'completed',
        timestamp: disputeData.closed_at || disputeData.updated_at,
        actor: disputeData.closed_by_user || { name: 'Moderator', role: 'moderator' },
        icon: ExclamationTriangleIcon,
        color: 'gray'
      });
    } else {
      // Add pending steps
      if (disputeData.status === 'open') {
        steps.push({
          id: 'awaiting_review',
          title: 'Awaiting Review',
          description: 'Waiting for moderator to review the dispute',
          status: 'pending',
          timestamp: null,
          actor: null,
          icon: ClockIcon,
          color: 'yellow'
        });
      }

      steps.push({
        id: 'resolution_pending',
        title: 'Resolution Pending',
        description: 'Awaiting final resolution decision',
        status: 'pending',
        timestamp: null,
        actor: null,
        icon: ClockIcon,
        color: 'yellow'
      });
    }

    setWorkflow(steps);
  };

  const getStepIcon = (step) => {
    const Icon = step.icon;
    const baseClasses = "h-8 w-8 rounded-full flex items-center justify-center";
    
    switch (step.status) {
      case 'completed':
        return (
          <div className={`${baseClasses} bg-green-100`}>
            <CheckCircleIcon className="h-5 w-5 text-green-600" />
          </div>
        );
      case 'pending':
        return (
          <div className={`${baseClasses} bg-yellow-100`}>
            <ClockIcon className="h-5 w-5 text-yellow-600" />
          </div>
        );
      default:
        return (
          <div className={`${baseClasses} bg-gray-100`}>
            <Icon className="h-5 w-5 text-gray-600" />
          </div>
        );
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return null;
    
    return new Date(timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActorBadge = (actor) => {
    if (!actor) return null;

    const roleColors = {
      'system': 'bg-gray-100 text-gray-800',
      'moderator': 'bg-blue-100 text-blue-800',
      'admin': 'bg-red-100 text-red-800',
      'buyer': 'bg-green-100 text-green-800',
      'seller': 'bg-purple-100 text-purple-800'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        roleColors[actor.role] || 'bg-gray-100 text-gray-800'
      }`}>
        {actor.role === 'system' ? '🤖' : '👤'} {actor.name}
      </span>
    );
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-6"></div>
          <div className="space-y-4">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!dispute) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center ${className}`}>
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Dispute not found</h3>
        <p className="text-gray-500">Unable to load dispute workflow.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Dispute Workflow</h2>
        <p className="text-gray-600 mt-1">Track the progress of dispute #{dispute.id}</p>
      </div>

      {/* Workflow Steps */}
      <div className="p-6">
        <div className="flow-root">
          <ul className="-mb-8">
            {workflow.map((step, stepIdx) => (
              <li key={step.id}>
                <div className="relative pb-8">
                  {stepIdx !== workflow.length - 1 ? (
                    <span
                      className={`absolute top-4 left-4 -ml-px h-full w-0.5 ${
                        step.status === 'completed' ? 'bg-green-200' : 'bg-gray-200'
                      }`}
                      aria-hidden="true"
                    />
                  ) : null}
                  
                  <div className="relative flex space-x-3">
                    <div>{getStepIcon(step)}</div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{step.title}</p>
                          <p className="text-sm text-gray-500 mt-1">{step.description}</p>
                        </div>
                        
                        <div className="text-right">
                          {step.actor && getActorBadge(step.actor)}
                          {step.timestamp && (
                            <p className="text-xs text-gray-400 mt-1">
                              {formatTimestamp(step.timestamp)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Current Status */}
      <div className="p-6 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Current Status</h3>
            <p className="text-sm text-gray-600 mt-1 capitalize">
              {dispute.status.replace('_', ' ')}
            </p>
          </div>
          
          <div className="text-right">
            <p className="text-sm font-medium text-gray-900">
              Last Updated
            </p>
            <p className="text-sm text-gray-600">
              {formatTimestamp(dispute.updated_at)}
            </p>
          </div>
        </div>

        {/* Next Steps */}
        {dispute.status !== 'resolved' && dispute.status !== 'closed' && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-900">Next Steps</h4>
            <div className="text-sm text-blue-800 mt-1">
              {dispute.status === 'open' && (
                <p>Your dispute is waiting to be reviewed by our moderation team. You will be notified of any updates.</p>
              )}
              {dispute.status === 'in_review' && (
                <p>A moderator is currently reviewing your dispute. They may contact you for additional information.</p>
              )}
            </div>
          </div>
        )}

        {/* Resolution Summary */}
        {dispute.status === 'resolved' && dispute.resolution && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 className="text-sm font-medium text-green-900">Resolution Summary</h4>
            <div className="text-sm text-green-800 mt-1">
              <p><strong>Type:</strong> {dispute.resolution.type}</p>
              {dispute.resolution.refund_amount && (
                <p><strong>Refund Amount:</strong> GH₵{dispute.resolution.refund_amount}</p>
              )}
              <p><strong>Notes:</strong> {dispute.resolution.notes}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default DisputeWorkflow;
