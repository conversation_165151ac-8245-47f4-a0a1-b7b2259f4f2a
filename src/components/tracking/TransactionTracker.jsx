import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  UserIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import useAuth from '../../hooks/useAuth';
import { externalTransactionAPI } from '../../services/apiServices';
import webSocketService from '../../services/websocket';
import StatusTimeline from './StatusTimeline';
import LoadingSpinner from '../helpers/LoadingSpinner';

function TransactionTracker() {
  const { id } = useParams();
  const { user } = useAuth();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);

  const fetchTransaction = useCallback(async () => {
    try {
      setLoading(true);
      const response = await externalTransactionAPI.getTransaction(id);

      if (response.data.success) {
        setTransaction(response.data.transaction);
        setLastUpdate(new Date());
        setError(null);
      } else {
        throw new Error(response.data.error || 'Transaction not found');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [id]);

  const setupRealTimeUpdates = useCallback(() => {
    if (!webSocketService.isConnected()) {
      webSocketService.connect();
    }

    // Subscribe to transaction updates
    webSocketService.on('transaction_updated', (data) => {
      if (data.transaction_id === id) {
        setTransaction((prev) => ({
          ...prev,
          ...data.updates,
          updated_at: data.timestamp,
        }));
        setLastUpdate(new Date());

        // Show notification for status changes
        if (data.updates.status) {
          toast.info(`Transaction status updated to: ${data.updates.status}`);
        }
      }
    });

    // Subscribe to payment updates
    webSocketService.on('payment_updated', (data) => {
      if (data.transaction_id === id) {
        setTransaction((prev) => ({
          ...prev,
          payment_status: data.payment_status,
          payment_reference: data.payment_reference,
          updated_at: data.timestamp,
        }));
        setLastUpdate(new Date());

        toast.info(`Payment status updated: ${data.payment_status}`);
      }
    });

    // Join transaction-specific channel
    webSocketService.emit('join_transaction', { transaction_id: id });

    // Update connection status
    setRealTimeUpdates(webSocketService.isConnected());
  }, [id]); // id is the dependency

  useEffect(() => {
    fetchTransaction();
    setupRealTimeUpdates();

    return () => {
    // Cleanup WebSocket listeners
      webSocketService.off('transaction_updated');
      webSocketService.off('payment_updated');
    };
  }, [fetchTransaction, setupRealTimeUpdates]);

  const getStatusConfig = (status) => {
    const configs = {
      pending: {
        icon: ClockIcon,
        color: 'yellow',
        bgColor: 'bg-yellow-50',
        textColor: 'text-yellow-800',
        borderColor: 'border-yellow-200',
        label: 'Pending Acceptance',
        description: 'Waiting for seller to accept the transaction',
      },
      accepted: {
        icon: CheckCircleIcon,
        color: 'blue',
        bgColor: 'bg-blue-50',
        textColor: 'text-blue-800',
        borderColor: 'border-blue-200',
        label: 'Accepted',
        description: 'Seller has accepted. Waiting for escrow funding',
      },
      funded: {
        icon: ShieldCheckIcon,
        color: 'green',
        bgColor: 'bg-green-50',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        label: 'Escrow Funded',
        description: 'Payment secured in escrow. Seller can now ship',
      },
      shipped: {
        icon: TruckIcon,
        color: 'purple',
        bgColor: 'bg-purple-50',
        textColor: 'text-purple-800',
        borderColor: 'border-purple-200',
        label: 'Shipped',
        description: 'Item has been shipped. Tracking details available',
      },
      delivered: {
        icon: CheckCircleIcon,
        color: 'green',
        bgColor: 'bg-green-50',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        label: 'Delivered',
        description: 'Item delivered. Waiting for buyer confirmation',
      },
      completed: {
        icon: CheckCircleIcon,
        color: 'green',
        bgColor: 'bg-green-50',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        label: 'Completed',
        description: 'Transaction completed successfully',
      },
      cancelled: {
        icon: XCircleIcon,
        color: 'red',
        bgColor: 'bg-red-50',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        label: 'Cancelled',
        description: 'Transaction has been cancelled',
      },
      disputed: {
        icon: ExclamationTriangleIcon,
        color: 'red',
        bgColor: 'bg-red-50',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        label: 'Disputed',
        description: 'Transaction is under dispute resolution',
      },
    };
    return configs[status] || configs.pending;
  };

  const getProgressPercentage = (status) => {
    const progressMap = {
      pending: 10,
      accepted: 25,
      funded: 50,
      shipped: 75,
      delivered: 90,
      completed: 100,
      cancelled: 0,
      disputed: 60,
    };
    return progressMap[status] || 0;
  };

  const getProgressBarColor = (status) => {
    if (status === 'completed') {
      return 'bg-green-500';
    }
    if (status === 'cancelled' || status === 'disputed') {
      return 'bg-red-500';
    }
    return 'bg-blue-500';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const userRole = transaction?.buyer_id === user?.id ? 'buyer' : 'seller';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading transaction</h3>
        <p className="mt-1 text-sm text-gray-500">{error}</p>
        <button
          type="button"
          onClick={fetchTransaction}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!transaction) {
    return null;
  }

  const statusConfig = getStatusConfig(transaction.status);
  const StatusIcon = statusConfig.icon;
  const progress = getProgressPercentage(transaction.status);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Transaction Tracker
            </h1>
            <p className="text-sm text-gray-600">
              Transaction ID:
              {' '}
              {transaction.id}
            </p>
          </div>

          {/* Real-time indicator */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className={`h-2 w-2 rounded-full ${realTimeUpdates ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="text-xs text-gray-500">
                {realTimeUpdates ? 'Live Updates' : 'Offline'}
              </span>
            </div>
            {lastUpdate && (
              <span className="text-xs text-gray-500">
                Updated:
                {' '}
                {formatDate(lastUpdate)}
              </span>
            )}
          </div>
        </div>

        {/* Current Status */}
        <div className={`p-4 rounded-lg ${statusConfig.bgColor} ${statusConfig.borderColor} border`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <StatusIcon className={`h-8 w-8 ${statusConfig.textColor}`} />
              <div>
                <h2 className={`text-lg font-semibold ${statusConfig.textColor}`}>
                  {statusConfig.label}
                </h2>
                <p className={`text-sm ${statusConfig.textColor} opacity-75`}>
                  {statusConfig.description}
                </p>
              </div>
            </div>

            {/* Progress Circle */}
            <div className="relative w-16 h-16">
              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                  className="text-gray-200"
                />
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray={`${progress * 1.76} 176`}
                  className={statusConfig.textColor}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-sm font-semibold ${statusConfig.textColor}`}>
                  {progress}
                  %
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
            <span>Transaction Progress</span>
            <span>
              {progress}
              % Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${getProgressBarColor(transaction.status)}`}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Quick Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Product Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <UserIcon className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Product</h3>
          </div>
          <p className="text-sm font-semibold text-gray-900 mb-1">
            {transaction.product_name}
          </p>
          <p className="text-xs text-gray-500">
            From
            {' '}
            {transaction.external_store_name}
          </p>
        </div>

        {/* Amount Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
            </div>
            <h3 className="font-medium text-gray-900">Amount</h3>
          </div>
          <p className="text-sm font-semibold text-gray-900 mb-1">
            GH₵
            {' '}
            {transaction.total_amount?.toFixed(2)}
          </p>
          <p className="text-xs text-gray-500">
            Including 5% escrow fee
          </p>
        </div>

        {/* Participants */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <UserIcon className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="font-medium text-gray-900">
              {userRole === 'buyer' ? 'Seller' : 'Buyer'}
            </h3>
          </div>
          <p className="text-sm font-semibold text-gray-900 mb-1">
            {userRole === 'buyer'
              ? `${transaction.seller?.first_name} ${transaction.seller?.last_name}`
              : `${transaction.buyer?.first_name} ${transaction.buyer?.last_name}`}
          </p>
          <p className="text-xs text-gray-500">
            @
            {userRole === 'buyer' ? transaction.seller?.username : transaction.buyer?.username}
          </p>
        </div>
      </div>

      {/* Shipping Information */}
      {transaction.tracking_number && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-lg font-medium text-gray-900">
              Shipping Information
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Tracking Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tracking Number:</span>
                  <span className="font-medium text-gray-900">{transaction.tracking_number}</span>
                </div>
                {transaction.shipping_carrier && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Carrier:</span>
                    <span className="font-medium text-gray-900">{transaction.shipping_carrier}</span>
                  </div>
                )}
                {transaction.shipped_at && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Shipped:</span>
                    <span className="font-medium text-gray-900">{formatDate(transaction.shipped_at)}</span>
                  </div>
                )}
                {transaction.estimated_delivery && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Est. Delivery:</span>
                    <span className="font-medium text-gray-900">{formatDate(transaction.estimated_delivery)}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Delivery Address</h3>
              <div className="text-sm text-gray-900 space-y-1">
                <p>{transaction.shipping_address?.street}</p>
                <p>
                  {transaction.shipping_address?.city}
                  ,
                  {transaction.shipping_address?.region}
                </p>
                <p>{transaction.shipping_address?.country}</p>
              </div>
            </div>
          </div>

          {/* External tracking link */}
          {transaction.tracking_number && transaction.shipping_carrier && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <a
                href={`https://track.${transaction.shipping_carrier.toLowerCase()}.com/${transaction.tracking_number}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <TruckIcon className="h-4 w-4 mr-1" />
                Track with
                {' '}
                {transaction.shipping_carrier}
              </a>
            </div>
          )}
        </div>
      )}

      {/* Timeline */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Transaction Timeline
        </h2>
        <StatusTimeline transaction={transaction} />
      </div>

      {/* Notifications */}
      {transaction.notifications && transaction.notifications.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <BellIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <h2 className="text-lg font-medium text-gray-900">
              Recent Notifications
            </h2>
          </div>

          <div className="space-y-3">
            {transaction.notifications.slice(0, 5).map((notification) => (
              <div key={notification.id || notification.created_at} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                <div className="flex-shrink-0 h-2 w-2 bg-blue-500 rounded-full mt-2" />
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{notification.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(notification.created_at)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default TransactionTracker;
