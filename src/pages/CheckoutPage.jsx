import { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  ArrowLeftIcon,
  CheckIcon,
  MapPinIcon,
  CreditCardIcon,
  ShoppingBagIcon,
} from '@heroicons/react/24/outline';
import { orderAPI, paymentAPI } from '../services/apiServices';
import { clearCart } from '../redux/slice/cartSlice';
import useAuth from '../hooks/useAuth';
import ShippingAddress from '../components/checkout/ShippingAddress';
import PaymentMethod from '../components/checkout/PaymentMethod';
import OrderSummary from '../components/checkout/OrderSummary';
import LoadingSpinner from '../components/helpers/LoadingSpinner';
import logger from '../components/helpers/logger';

function CheckoutPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    items, total, loading: cartLoading,
  } = useSelector((state) => state.cart);

  const [currentStep, setCurrentStep] = useState(1);
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [appliedCoupon] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [loading, setLoading] = useState(false);

  const fetchAddresses = useCallback(async () => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch user addresses
      // const response = await addressAPI.getUserAddresses();
      // setAddresses(response.data.addresses);

      // Placeholder addresses for demo
      const demoAddresses = [
        {
          id: 1,
          type: 'home',
          full_name: `${user?.first_name} ${user?.last_name}` || 'John Doe',
          phone: user?.phone_number || '+233 24 123 4567',
          street_address: '123 Main Street, East Legon',
          city: 'Accra',
          region: 'Greater Accra',
          postal_code: 'GA-123-4567',
          is_default: true,
        },
      ];

      setAddresses(demoAddresses);
      setSelectedAddress(demoAddresses.find((addr) => addr.is_default) || demoAddresses[0]);
    } catch (error) {
      logger.error('Failed to fetch addresses:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    // Redirect if not authenticated
    if (!user) {
      toast.error('Please login to proceed with checkout');
      navigate('/login', { state: { from: '/checkout' } });
      return;
    }

    // Redirect if cart is empty
    if (items.length === 0) {
      toast.error('Your cart is empty');
      navigate('/cart');
      return;
    }

    // Fetch user addresses
    fetchAddresses();
  }, [user, items.length, navigate, fetchAddresses]);

  // Calculate totals
  const subtotal = total;
  const escrowFee = subtotal * 0.05; // 5% escrow fee
  const totalShipping = items.reduce((sum, item) => sum + (item.product?.shipping_cost || 0), 0);
  const couponDiscount = appliedCoupon ? ((subtotal * appliedCoupon.discount_percentage) / 100) : 0;
  const finalTotal = subtotal - couponDiscount + totalShipping + escrowFee;

  const handleAddressAdd = async (addressData) => {
    // TODO: Implement API call to add address
    // const response = await addressAPI.createAddress(addressData);
    // const newAddress = response.data.address;

    // Placeholder for demo
    const newAddress = {
      id: Date.now(),
      ...addressData,
    };

    setAddresses((prev) => [...prev, newAddress]);

    if (addressData.is_default || addresses.length === 0) {
      setSelectedAddress(newAddress);
    }
  };

  const handleAddressUpdate = async (addressId, addressData) => {
    // TODO: Implement API call to update address
    // await addressAPI.updateAddress(addressId, addressData);

    setAddresses((prev) => prev.map((addr) => (addr.id === addressId
      ? { ...addr, ...addressData } : addr)));

    if (selectedAddress?.id === addressId) {
      setSelectedAddress((prev) => ({ ...prev, ...addressData }));
    }
  };

  const handleAddressDelete = async (addressId) => {
    // TODO: Implement API call to delete address
    // await addressAPI.deleteAddress(addressId);

    setAddresses((prev) => prev.filter((addr) => addr.id !== addressId));

    if (selectedAddress?.id === addressId) {
      const remainingAddresses = addresses.filter((addr) => addr.id !== addressId);
      setSelectedAddress(remainingAddresses[0] || null);
    }
  };

  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      toast.error('Please select a shipping address');
      return;
    }

    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method');
      return;
    }

    try {
      setIsProcessing(true);

      // Prepare order data
      const orderData = {
        items: items.map((item) => ({
          product_id: item.product.id,
          quantity: item.quantity,
          price: item.product.price,
        })),
        shipping_address_id: selectedAddress.id,
        payment_method: selectedPaymentMethod.id,
        subtotal,
        shipping_cost: totalShipping,
        escrow_fee: escrowFee,
        coupon_discount: couponDiscount,
        total: finalTotal,
        coupon_code: appliedCoupon?.code,
      };

      // Create order
      const orderResponse = await orderAPI.createOrder(orderData);

      if (!orderResponse.data.success) {
        throw new Error(orderResponse.data.error || 'Failed to create order');
      }

      const { order } = orderResponse.data;

      // Process payment
      const paymentData = {
        order_id: order.id,
        amount: finalTotal,
        payment_method: selectedPaymentMethod.id,
        customer: {
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: selectedAddress.phone,
        },
      };

      const paymentResponse = await paymentAPI.processPayment(paymentData);

      if (!paymentResponse.data.success) {
        throw new Error(paymentResponse.data.error || 'Payment failed');
      }

      // Clear cart and redirect to success page
      dispatch(clearCart());
      toast.success('Order placed successfully!');
      navigate(`/orders/${order.id}/success`);
    } catch (error) {
      logger.error('Checkout error:', error);
      toast.error(error.message || 'Failed to process order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const steps = [
    { id: 1, name: 'Shipping', icon: MapPinIcon },
    { id: 2, name: 'Payment', icon: CreditCardIcon },
    { id: 3, name: 'Review', icon: ShoppingBagIcon },
  ];

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return selectedAddress !== null;
      case 2:
        return selectedPaymentMethod !== null;
      case 3:
        return selectedAddress && selectedPaymentMethod;
      default:
        return false;
    }
  };

  const getStepIconClasses = (isCompleted, isActive) => {
    if (isCompleted) {
      return 'bg-green-500 border-green-500 text-white';
    }
    if (isActive) {
      return 'bg-blue-500 border-blue-500 text-white';
    }
    return 'bg-white border-gray-300 text-gray-400';
  };

  const getStepTextClasses = (isCompleted, isActive) => {
    if (isActive) {
      return 'text-blue-600';
    }
    if (isCompleted) {
      return 'text-green-600';
    }
    return 'text-gray-500';
  };

  if (loading || cartLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              type="button"
              onClick={() => navigate('/cart')}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
              <p className="text-sm text-gray-600 mt-1">
                Complete your order securely
              </p>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${getStepIconClasses(isCompleted, isActive)}`}>
                    {isCompleted ? (
                      <CheckIcon className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>

                  <div className="ml-3 mr-8">
                    <p className={`text-sm font-medium ${getStepTextClasses(isCompleted, isActive)}`}>
                      {step.name}
                    </p>
                  </div>

                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
                    }`}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <div className="lg:grid lg:grid-cols-12 lg:gap-x-8">
          {/* Main Content */}
          <div className="lg:col-span-8">
            <div className="space-y-8">
              {/* Step 1: Shipping Address */}
              {currentStep === 1 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <ShippingAddress
                    selectedAddress={selectedAddress}
                    onAddressSelect={setSelectedAddress}
                    onAddressAdd={handleAddressAdd}
                    onAddressUpdate={handleAddressUpdate}
                    onAddressDelete={handleAddressDelete}
                    addresses={addresses}
                    isLoading={loading}
                  />
                </div>
              )}

              {/* Step 2: Payment Method */}
              {currentStep === 2 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <PaymentMethod
                    selectedMethod={selectedPaymentMethod}
                    onMethodSelect={setSelectedPaymentMethod}
                    orderTotal={subtotal - couponDiscount + totalShipping}
                    escrowFee={escrowFee}
                    isLoading={loading}
                  />
                </div>
              )}

              {/* Step 3: Review Order */}
              {currentStep === 3 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-6">Review Your Order</h2>

                  {/* Order Items */}
                  <div className="space-y-4 mb-6">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 py-4 border-b border-gray-200 last:border-b-0">
                        <img
                          src={item.product.primary_image?.url || '/placeholder-product.jpg'}
                          alt={item.product.name}
                          className="h-16 w-16 object-cover rounded-md border border-gray-200"
                        />
                        <div className="flex-1">
                          <h3 className="text-sm font-medium text-gray-900">{item.product.name}</h3>
                          <p className="text-sm text-gray-500">
                            Quantity:
                            {' '}
                            {item.quantity}
                          </p>
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          GH₵
                          {' '}
                          {(item.product.price * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Selected Address */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-md">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Shipping Address</h4>
                    <div className="text-sm text-gray-600">
                      <p className="font-medium text-gray-900">{selectedAddress?.full_name}</p>
                      <p>{selectedAddress?.street_address}</p>
                      <p>
                        {selectedAddress?.city}
                        ,
                        {' '}
                        {selectedAddress?.region}
                      </p>
                      <p>
                        Phone:
                        {' '}
                        {selectedAddress?.phone}
                      </p>
                    </div>
                  </div>

                  {/* Selected Payment Method */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-md">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Method</h4>
                    <p className="text-sm text-gray-600">
                      {selectedPaymentMethod?.name || 'Credit/Debit Card'}
                    </p>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                  disabled={currentStep === 1}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={() => setCurrentStep(currentStep + 1)}
                    disabled={!canProceedToNextStep()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Continue
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handlePlaceOrder}
                    disabled={isProcessing || !canProceedToNextStep()}
                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Processing...' : 'Place Order'}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-4 mt-8 lg:mt-0">
            <OrderSummary
              items={items}
              shippingAddress={selectedAddress}
              paymentMethod={selectedPaymentMethod}
              appliedCoupon={appliedCoupon}
              onPlaceOrder={handlePlaceOrder}
              isProcessing={isProcessing}
              showItemDetails={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CheckoutPage;
