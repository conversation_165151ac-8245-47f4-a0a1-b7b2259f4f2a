import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  CheckCircleIcon,
  ClockIcon,
  TruckIcon,
  ExclamationTriangleIcon,
  UserIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ArrowLeftIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import { externalTransactionAPI } from '../../services/apiServices';
import TrackingNumberInput from './TrackingNumberInput';
import LoadingSpinner from '../helpers/LoadingSpinner';

function OrderFulfillment() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [fulfillmentStep, setFulfillmentStep] = useState(1);

  useEffect(() => {
    fetchTransaction();
  }, [id]);

  const fetchTransaction = async () => {
    try {
      setLoading(true);
      const response = await externalTransactionAPI.getTransaction(id);

      if (response.data.success) {
        const txn = response.data.transaction;

        // Verify this is the seller
        if (txn.seller_id !== user?.id) {
          throw new Error('You are not authorized to fulfill this order');
        }

        // Check transaction status
        if (!['funded', 'shipped'].includes(txn.status)) {
          throw new Error('This order is not ready for fulfillment');
        }

        setTransaction(txn);

        // Determine fulfillment step based on transaction status
        if (txn.status === 'funded' && !txn.tracking_number) {
          setFulfillmentStep(1); // Need to add tracking
        } else if (txn.status === 'funded' && txn.tracking_number) {
          setFulfillmentStep(2); // Ready to ship
        } else if (txn.status === 'shipped') {
          setFulfillmentStep(3); // Already shipped
        }

        setError(null);
      } else {
        throw new Error(response.data.error || 'Transaction not found');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTrackingUpdate = async (trackingData) => {
    try {
      const response = await externalTransactionAPI.submitShippingEvidence(transaction.id, trackingData);

      if (response.data.success) {
        setTransaction((prev) => ({
          ...prev,
          ...trackingData,
          status: 'shipped',
          shipped_at: new Date().toISOString(),
        }));
        setFulfillmentStep(3);
        toast.success('Order marked as shipped successfully!');
      } else {
        throw new Error(response.data.error || 'Failed to update tracking information');
      }
    } catch (error) {
      throw error;
    }
  };

  const markAsShipped = async () => {
    try {
      const response = await externalTransactionAPI.submitShippingEvidence(transaction.id, {
        status: 'shipped',
        shipped_at: new Date().toISOString(),
      });

      if (response.data.success) {
        setTransaction((prev) => ({
          ...prev,
          status: 'shipped',
          shipped_at: new Date().toISOString(),
        }));
        setFulfillmentStep(3);
        toast.success('Order marked as shipped!');
      } else {
        throw new Error(response.data.error || 'Failed to mark order as shipped');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to mark order as shipped');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Unable to load order</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return null;
  }

  const fulfillmentSteps = [
    {
      id: 1,
      title: 'Add Tracking Information',
      description: 'Provide tracking number and shipping details',
      icon: DocumentTextIcon,
      status: fulfillmentStep > 1 ? 'completed' : fulfillmentStep === 1 ? 'current' : 'upcoming',
    },
    {
      id: 2,
      title: 'Ship the Item',
      description: 'Package and ship the item to the buyer',
      icon: TruckIcon,
      status: fulfillmentStep > 2 ? 'completed' : fulfillmentStep === 2 ? 'current' : 'upcoming',
    },
    {
      id: 3,
      title: 'Order Shipped',
      description: 'Buyer will be notified and can track the package',
      icon: CheckCircleIcon,
      status: fulfillmentStep >= 3 ? 'completed' : 'upcoming',
    },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate(-1)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Order Fulfillment
              </h1>
              <p className="text-sm text-gray-600">
                Transaction ID:
                {' '}
                {transaction.id}
              </p>
            </div>
          </div>

          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
            transaction.status === 'funded' ? 'bg-blue-50 text-blue-800 border-blue-200'
              : transaction.status === 'shipped' ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-gray-50 text-gray-800 border-gray-200'
          } border`}
          >
            {transaction.status === 'funded' ? 'Ready to Ship'
              : transaction.status === 'shipped' ? 'Shipped'
                : transaction.status}
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-6">Fulfillment Progress</h2>

        <div className="space-y-4">
          {fulfillmentSteps.map((step, stepIdx) => {
            const StepIcon = step.icon;
            const isLast = stepIdx === fulfillmentSteps.length - 1;

            return (
              <div key={step.id} className="relative">
                {!isLast && (
                  <div className={`absolute left-4 top-8 -ml-px h-6 w-0.5 ${
                    step.status === 'completed' ? 'bg-green-400' : 'bg-gray-300'
                  }`}
                  />
                )}

                <div className="relative flex items-start space-x-3">
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full ${
                    step.status === 'completed' ? 'bg-green-100'
                      : step.status === 'current' ? 'bg-blue-100'
                        : 'bg-gray-100'
                  }`}
                  >
                    <StepIcon className={`h-5 w-5 ${
                      step.status === 'completed' ? 'text-green-600'
                        : step.status === 'current' ? 'text-blue-600'
                          : 'text-gray-400'
                    }`}
                    />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className={`text-sm font-medium ${
                        step.status === 'completed' ? 'text-green-900'
                          : step.status === 'current' ? 'text-blue-900'
                            : 'text-gray-500'
                      }`}
                      >
                        {step.title}
                      </h3>

                      {step.status === 'completed' && (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      )}

                      {step.status === 'current' && (
                        <ClockIcon className="h-5 w-5 text-blue-500" />
                      )}
                    </div>

                    <p className={`mt-1 text-sm ${
                      step.status === 'completed' ? 'text-green-700'
                        : step.status === 'current' ? 'text-blue-700'
                          : 'text-gray-500'
                    }`}
                    >
                      {step.description}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Order Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Product & Buyer Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Order Details</h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Product</h3>
              <p className="text-sm font-semibold text-gray-900">{transaction.product_name}</p>
              <p className="text-xs text-gray-500">
                From
                {transaction.external_store_name}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Buyer Information</h3>
              <div className="flex items-center space-x-3">
                <img
                  src={transaction.buyer?.avatar || '/default-avatar.jpg'}
                  alt={transaction.buyer?.first_name}
                  className="h-8 w-8 rounded-full"
                />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {transaction.buyer?.first_name}
                    {' '}
                    {transaction.buyer?.last_name}
                  </p>
                  <p className="text-xs text-gray-500">
                    @
                    {transaction.buyer?.username}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Order Value</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Product Amount:</span>
                  <span className="font-medium">
                    GH₵
                    {transaction.base_amount?.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Escrow Fee:</span>
                  <span className="font-medium">
                    GH₵
                    {transaction.escrow_fee?.toFixed(2)}
                  </span>
                </div>
                <div className="border-t border-gray-200 pt-1 flex justify-between font-semibold">
                  <span>Total:</span>
                  <span>
                    GH₵
                    {transaction.total_amount?.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Shipping Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Shipping Information</h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Delivery Address</h3>
              <div className="text-sm text-gray-900 space-y-1">
                <p>{transaction.shipping_address?.street}</p>
                <p>
                  {transaction.shipping_address?.city}
                  ,
                  {' '}
                  {transaction.shipping_address?.region}
                </p>
                <p>{transaction.shipping_address?.country}</p>
              </div>
            </div>

            {transaction.tracking_number && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Tracking Information</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tracking Number:</span>
                    <span className="font-medium">{transaction.tracking_number}</span>
                  </div>
                  {transaction.shipping_carrier && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Carrier:</span>
                      <span className="font-medium capitalize">{transaction.shipping_carrier.replace('_', ' ')}</span>
                    </div>
                  )}
                  {transaction.estimated_delivery && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Est. Delivery:</span>
                      <span className="font-medium">{formatDate(transaction.estimated_delivery)}</span>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => setShowTrackingModal(true)}
                  className="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Update Tracking Info
                </button>
              </div>
            )}

            {transaction.shipped_at && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Shipping Status</h3>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-green-700">
                    Shipped on
                    {' '}
                    {formatDate(transaction.shipped_at)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Next Steps</h3>
            <p className="text-sm text-gray-600 mt-1">
              {fulfillmentStep === 1 && 'Add tracking information to proceed with shipping'}
              {fulfillmentStep === 2 && 'Mark the order as shipped once you\'ve sent the package'}
              {fulfillmentStep === 3 && 'Order has been shipped. The buyer will be notified.'}
            </p>
          </div>

          <div className="flex space-x-3">
            {fulfillmentStep === 1 && (
              <button
                onClick={() => setShowTrackingModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                Add Tracking Info
              </button>
            )}

            {fulfillmentStep === 2 && (
              <button
                onClick={markAsShipped}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <TruckIcon className="h-4 w-4 mr-2" />
                Mark as Shipped
              </button>
            )}

            {fulfillmentStep === 3 && (
              <button
                onClick={() => navigate(`/transactions/${transaction.id}`)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                View Transaction Details
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tracking Modal */}
      {showTrackingModal && (
        <TrackingNumberInput
          transaction={transaction}
          initialData={transaction.tracking_number ? {
            tracking_number: transaction.tracking_number,
            shipping_carrier: transaction.shipping_carrier,
            shipping_method: transaction.shipping_method,
            estimated_delivery: transaction.estimated_delivery,
          } : null}
          onUpdate={handleTrackingUpdate}
          onClose={() => setShowTrackingModal(false)}
        />
      )}
    </div>
  );
}

export default OrderFulfillment;
