import { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { orderAPI, paymentAPI } from '../../services/apiServices';
import { clearCart } from '../../redux/slice/cartSlice';
import Button from '../helpers/Button';
import logger from '../helpers/logger';

function CheckoutForm() {
  const { items, total } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      street: user?.address?.street || '',
      city: user?.address?.city || '',
      region: user?.address?.region || '',
      country: user?.address?.country || 'Ghana',
    },
  });

  const onSubmit = async (formData) => {
    try {
      setIsProcessing(true);

      // Create order
      const orderData = {
        items: items.map((item) => ({
          product_id: item.product.id,
          quantity: item.quantity,
          unit_price: item.price,
        })),
        shipping_address: {
          street: formData.street,
          city: formData.city,
          region: formData.region,
          country: formData.country,
          postal_code: formData.postal_code,
        },
        notes: formData.notes || '',
      };

      const orderResponse = await orderAPI.createOrder(orderData);
      if (!orderResponse.data.success) {
        logger.error('Failed to create order');
      }

      const { order } = orderResponse.data;

      // Initialize payment
      const paymentData = {
        order_id: order.id,
        payment_method: 'card',
        callback_url: `${window.location.origin}/payment/callback`,
      };

      const paymentResponse = await paymentAPI.initializePayment(paymentData);
      if (!paymentResponse.data.success) {
        throw new Error('Failed to initialize payment');
      }

      // Clear cart and redirect to payment
      dispatch(clearCart());
      window.location.href = paymentResponse.data.redirect_url;
    } catch (error) {
      toast.error(error.message || 'Checkout failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (items.length === 0) {
    return (
      <div>
        <p>Your cart is empty</p>
        <Button
          onClick={() => navigate('/products')}
        >
          Continue Shopping
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div>
        <h1>Checkout</h1>

        <div>
          {/* Order Summary */}
          <div>
            <h2>Order Summary</h2>
            <div>
              {items.map((item) => (
                <div key={item.id}>
                  <div>
                    <img
                      src={item.product.images[0]?.url || '/placeholder.jpg'}
                      alt={item.product.name}
                    />
                    <div>
                      <p>
                        {item.product.name}
                      </p>
                      <p>
                        Qty:
                        {' '}
                        {item.quantity}
                      </p>
                    </div>
                  </div>
                  <p>
                    GHS
                    {' '}
                    {(item.price * item.quantity).toFixed(2)}
                  </p>
                </div>
              ))}

              <div>
                <div>
                  <p>Total:</p>
                  <p>
                    GHS
                    {' '}
                    {total.toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Shipping Form */}
          <div>
            <h2>Shipping Information</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label htmlFor="street">
                  Street Address *
                  <input
                    name={register('street', { required: 'Street address is required' }).name}
                    onChange={register('street', { required: 'Street address is required' }).onChange}
                    onBlur={register('street', { required: 'Street address is required' }).onBlur}
                    ref={register('street', { required: 'Street address is required' }).ref}
                    type="text"
                  />
                </label>
                {errors.street && (
                <p>{errors.street.message}</p>
                )}
              </div>

              <div>
                <div>
                  <label htmlFor="city">
                    City *
                    <input
                      name={register('city', { required: 'City is required' }).name}
                      onChange={register('city', { required: 'City is required' }).onChange}
                      onBlur={register('city', { required: 'City is required' }).onBlur}
                      ref={register('city', { required: 'City is required' }).ref}
                      type="text"
                    />
                  </label>
                  {errors.city && (
                    <p>{errors.city.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="region">
                    Region *

                    <select
                      name={register('region', { required: 'Region is required' }).name}
                      onChange={register('region', { required: 'Region is required' }).onChange}
                      onBlur={register('region', { required: 'Region is required' }).onBlur}
                      ref={register('region', { required: 'Region is required' }).ref}
                    >
                      <option value="">Select Region</option>
                      <option value="Greater Accra">Greater Accra</option>
                      <option value="Ashanti">Ashanti</option>
                      <option value="Western">Western</option>
                      <option value="Central">Central</option>
                      <option value="Eastern">Eastern</option>
                      <option value="Volta">Volta</option>
                      <option value="Northern">Northern</option>
                      <option value="Upper East">Upper East</option>
                      <option value="Upper West">Upper West</option>
                      <option value="Brong Ahafo">Brong Ahafo</option>
                    </select>
                  </label>
                  {errors.region && (
                    <p>{errors.region.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="postal_code">
                  Postal Code

                  <input
                    name={register('postal_code').name}
                    onChange={register('postal_code').onChange}
                    onBlur={register('postal_code').onBlur}
                    placeholder="Enter postal code"
                    type="text"
                  />
                </label>
              </div>

              <div>
                <label htmlFor="notes">
                  Additional Notes
                  <textarea
                    name={register('notes').name}
                    onChange={register('notes').onChange}
                    onBlur={register('notes').onBlur}
                    ref={register('notes').ref}
                    rows={3}
                    placeholder="Any special instructions..."
                  />
                </label>
              </div>

              <div>
                <Button
                  type="submit"
                  size="large"
                  isLoading={isProcessing}
                  className="w-full"
                >
                  Proceed to Payment - GHS
                  {' '}
                  {total.toFixed(2)}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CheckoutForm;
