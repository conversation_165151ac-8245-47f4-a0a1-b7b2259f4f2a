{"name": "payhold-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "prop-types": "^15.8.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "use-local-storage-state": "^19.5.0"}, "devDependencies": {"@babel/core": "^7.28.4", "@babel/eslint-parser": "^7.28.4", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/preset-react": "^7.27.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "stylelint": "^16.24.0", "stylelint-config-standard": "^39.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-scss": "^6.12.1", "vite": "^7.1.2"}}