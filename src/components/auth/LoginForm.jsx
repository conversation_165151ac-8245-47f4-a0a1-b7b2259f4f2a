import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import useAuth from '../../hooks/useAuth';
import Button from '../helpers/Button';

function LoginForm() {
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();
  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = async (data) => {
    try {
      const result = await login(data);
      if (result.type === 'auth/login/fulfilled') {
        navigate('/dashboard');
      }
    } catch (error) {
      // toast.error expects a string; include error message if available
      toast.error(`Login failed: ${error?.message ?? error}`);
    }
  };

  // create register props for email/password and destructure to avoid spreading
  const emailReg = register('email', {
    required: 'Email is required',
    pattern: {
      value: /^\S+@\S+$/i,
      message: 'Invalid email address',
    },
  });

  const passwordReg = register('password', {
    required: 'Password is required',
  });

  return (
    <div>
      <div>
        <div>
          <h2>Sign in to PayHold</h2>
          <p>Secure escrow payments for Ghana</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div>
            <div>
              <label htmlFor="email">
                Email Address
                <input
                  id="email"
                  name={emailReg.name}
                  onChange={emailReg.onChange}
                  onBlur={emailReg.onBlur}
                  ref={emailReg.ref}
                  type="email"
                  placeholder="Enter your email"
                  aria-invalid={errors.email ? 'true' : 'false'}
                />
              </label>
              {errors.email && <p role="alert">{errors.email.message}</p>}
            </div>

            <div>
              <label htmlFor="password">
                Password
                <input
                  id="password"
                  name={passwordReg.name}
                  onChange={passwordReg.onChange}
                  onBlur={passwordReg.onBlur}
                  ref={passwordReg.ref}
                  type="password"
                  placeholder="Enter your password"
                  aria-invalid={errors.password ? 'true' : 'false'}
                />
              </label>
              {errors.password && <p role="alert">{errors.password.message}</p>}
            </div>
          </div>

          <div>
            <div>
              <input id="remember-me" name="remember-me" type="checkbox" />
              <span htmlFor="remember-me">Remember me</span>
            </div>

            <div>
              <Link to="/forgot-password">Forgot your password?</Link>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              size="large"
              isLoading={isLoading}
            >
              Sign in
            </Button>
          </div>

          <div>
            <span>
              Don&apos;t have an account?
              {' '}
              <Link to="/register">
                Sign up here
              </Link>
            </span>
          </div>
        </form>
      </div>
    </div>
  );
}

export default LoginForm;
