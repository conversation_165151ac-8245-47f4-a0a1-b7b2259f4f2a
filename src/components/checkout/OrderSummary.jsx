import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  ShoppingBagIcon,
  TruckIcon,
  ShieldCheckIcon,
  TagIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  InformationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

function OrderSummary({ 
  items = [], 
  shippingAddress,
  paymentMethod,
  appliedCoupon,
  onPlaceOrder,
  isProcessing = false,
  showItemDetails = true 
}) {
  const [showItems, setShowItems] = useState(showItemDetails);

  // Calculate totals
  const subtotal = items.reduce((sum, item) => {
    return sum + (item.product.price * item.quantity);
  }, 0);

  const totalShipping = items.reduce((sum, item) => {
    return sum + (item.product.shipping_cost || 0);
  }, 0);

  const escrowFee = subtotal * 0.05; // 5% escrow fee
  
  const couponDiscount = appliedCoupon ? (subtotal * appliedCoupon.discount_percentage / 100) : 0;
  const discountedSubtotal = subtotal - couponDiscount;
  
  const finalTotal = discountedSubtotal + totalShipping + escrowFee;

  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  const canPlaceOrder = shippingAddress && paymentMethod && items.length > 0;

  const formatAddress = (address) => {
    if (!address) return 'No address selected';
    
    const parts = [
      address.street_address,
      address.city,
      address.region
    ].filter(Boolean);
    
    return parts.join(', ');
  };

  const getPaymentMethodName = (method) => {
    if (!method) return 'No payment method selected';
    
    switch (method.id) {
      case 'card':
        return 'Credit/Debit Card';
      case 'mobile_money':
        return 'Mobile Money';
      case 'bank_transfer':
        return 'Bank Transfer';
      default:
        return method.name || 'Unknown';
    }
  };

  const getEstimatedDelivery = () => {
    const today = new Date();
    const deliveryDate = new Date(today);
    deliveryDate.setDate(today.getDate() + 5); // 5 days from now
    
    return deliveryDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-6">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-6">
        <ShoppingBagIcon className="h-5 w-5 text-gray-500" />
        <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
      </div>

      {/* Items Toggle */}
      <div className="mb-4">
        <button
          onClick={() => setShowItems(!showItems)}
          className="flex items-center justify-between w-full text-sm font-medium text-gray-900 hover:text-gray-700"
        >
          <span>Items ({itemCount})</span>
          {showItems ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Items List */}
      {showItems && (
        <div className="mb-6 space-y-3 max-h-64 overflow-y-auto">
          {items.map((item) => (
            <div key={item.id} className="flex items-center space-x-3 py-2">
              <div className="flex-shrink-0">
                <img
                  src={item.product.primary_image?.url || '/placeholder-product.jpg'}
                  alt={item.product.name}
                  className="h-12 w-12 object-cover rounded-md border border-gray-200"
                />
              </div>
              
              <div className="flex-1 min-w-0">
                <Link
                  to={`/products/${item.product.id}`}
                  className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-1"
                >
                  {item.product.name}
                </Link>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-gray-500">
                    Qty: {item.quantity}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    GH₵ {(item.product.price * item.quantity).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Cost Breakdown */}
      <div className="space-y-3 py-4 border-t border-gray-200">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal ({itemCount} items)</span>
          <span className="text-gray-900">GH₵ {subtotal.toFixed(2)}</span>
        </div>

        {appliedCoupon && couponDiscount > 0 && (
          <div className="flex justify-between text-sm">
            <div className="flex items-center space-x-1">
              <TagIcon className="h-4 w-4 text-green-500" />
              <span className="text-green-600">Discount ({appliedCoupon.code})</span>
            </div>
            <span className="text-green-600">-GH₵ {couponDiscount.toFixed(2)}</span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <div className="flex items-center space-x-1">
            <TruckIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Shipping</span>
          </div>
          <span className="text-gray-900">
            {totalShipping > 0 ? `GH₵ ${totalShipping.toFixed(2)}` : 'Free'}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <div className="flex items-center space-x-1">
            <ShieldCheckIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Escrow Fee (5%)</span>
          </div>
          <span className="text-gray-900">GH₵ {escrowFee.toFixed(2)}</span>
        </div>

        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between">
            <span className="text-base font-medium text-gray-900">Total</span>
            <span className="text-lg font-bold text-blue-600">GH₵ {finalTotal.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Delivery Information */}
      <div className="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div className="flex items-start space-x-2">
          <ClockIcon className="h-4 w-4 text-blue-500 mt-0.5" />
          <div className="text-xs text-blue-800">
            <p className="font-medium mb-1">Estimated Delivery</p>
            <p>{getEstimatedDelivery()}</p>
            <p className="mt-1">Standard delivery (3-7 business days)</p>
          </div>
        </div>
      </div>

      {/* Shipping Address Summary */}
      <div className="mb-4 p-3 bg-gray-50 rounded-md">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Shipping Address</h4>
        {shippingAddress ? (
          <div className="text-sm text-gray-600">
            <p className="font-medium text-gray-900">{shippingAddress.full_name}</p>
            <p>{formatAddress(shippingAddress)}</p>
            {shippingAddress.phone && <p>Phone: {shippingAddress.phone}</p>}
          </div>
        ) : (
          <p className="text-sm text-red-600">Please select a shipping address</p>
        )}
      </div>

      {/* Payment Method Summary */}
      <div className="mb-6 p-3 bg-gray-50 rounded-md">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Method</h4>
        {paymentMethod ? (
          <div className="text-sm text-gray-600">
            <p>{getPaymentMethodName(paymentMethod)}</p>
            {paymentMethod.id === 'card' && (
              <p className="text-xs text-gray-500 mt-1">
                Your card will be charged after you confirm the order
              </p>
            )}
          </div>
        ) : (
          <p className="text-sm text-red-600">Please select a payment method</p>
        )}
      </div>

      {/* Escrow Protection Info */}
      <div className="mb-6 p-3 bg-green-50 border border-green-200 rounded-md">
        <div className="flex items-start space-x-2">
          <ShieldCheckIcon className="h-4 w-4 text-green-500 mt-0.5" />
          <div className="text-xs text-green-800">
            <p className="font-medium mb-1">Escrow Protection</p>
            <p>Your payment is held securely until you confirm delivery. Sellers only get paid after you're satisfied with your order.</p>
          </div>
        </div>
      </div>

      {/* Place Order Button */}
      <button
        onClick={onPlaceOrder}
        disabled={!canPlaceOrder || isProcessing}
        className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isProcessing ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Processing Order...
          </>
        ) : (
          <>
            <ShieldCheckIcon className="h-5 w-5 mr-2" />
            Place Order - GH₵ {finalTotal.toFixed(2)}
          </>
        )}
      </button>

      {/* Order Validation Messages */}
      {!canPlaceOrder && !isProcessing && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="h-4 w-4 text-yellow-500 mt-0.5" />
            <div className="text-xs text-yellow-800">
              <p className="font-medium mb-1">Complete your order</p>
              <ul className="list-disc list-inside space-y-1">
                {!shippingAddress && <li>Select a shipping address</li>}
                {!paymentMethod && <li>Choose a payment method</li>}
                {items.length === 0 && <li>Add items to your cart</li>}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Terms and Conditions */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>
          By placing this order, you agree to our{' '}
          <Link to="/terms" className="text-blue-600 hover:text-blue-800">
            Terms of Service
          </Link>{' '}
          and{' '}
          <Link to="/privacy" className="text-blue-600 hover:text-blue-800">
            Privacy Policy
          </Link>
        </p>
      </div>

      {/* Security Notice */}
      <div className="mt-4 flex items-center justify-center space-x-2 text-xs text-gray-500">
        <ShieldCheckIcon className="h-3 w-3" />
        <span>Secure checkout powered by PayHold</span>
      </div>
    </div>
  );
}

export default OrderSummary;
