import React from 'react';
import {
  MagnifyingGlassIcon,
  ClockIcon,
  TrendingUpIcon,
  SparklesIcon,
  TagIcon,
  UserIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline';

function SearchSuggestions({ suggestions, onSelect, className = '' }) {
  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'product':
        return <ShoppingCartIcon className="h-4 w-4 text-blue-500" />;
      case 'category':
        return <TagIcon className="h-4 w-4 text-green-500" />;
      case 'seller':
        return <UserIcon className="h-4 w-4 text-purple-500" />;
      case 'trending':
        return <TrendingUpIcon className="h-4 w-4 text-red-500" />;
      case 'recent':
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
      case 'autocomplete':
        return <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />;
      default:
        return <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSuggestionLabel = (type) => {
    switch (type) {
      case 'product':
        return 'Product';
      case 'category':
        return 'Category';
      case 'seller':
        return 'Seller';
      case 'trending':
        return 'Trending';
      case 'recent':
        return 'Recent';
      case 'autocomplete':
        return 'Search';
      default:
        return '';
    }
  };

  const groupSuggestionsByType = (suggestions) => {
    const grouped = {};
    
    suggestions.forEach(suggestion => {
      const type = suggestion.type || 'autocomplete';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(suggestion);
    });
    
    return grouped;
  };

  const highlightMatch = (text, query) => {
    if (!query || !text) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="font-medium text-blue-600">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  const groupedSuggestions = groupSuggestionsByType(suggestions);
  const typeOrder = ['trending', 'product', 'category', 'seller', 'autocomplete', 'recent'];

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 max-h-80 overflow-y-auto ${className}`}>
      <div className="p-2">
        {typeOrder.map(type => {
          if (!groupedSuggestions[type] || groupedSuggestions[type].length === 0) {
            return null;
          }

          return (
            <div key={type} className="mb-4 last:mb-0">
              {/* Section Header */}
              {groupedSuggestions[type].length > 0 && getSuggestionLabel(type) && (
                <div className="flex items-center space-x-2 px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                  {getSuggestionIcon(type)}
                  <span>{getSuggestionLabel(type)}</span>
                </div>
              )}

              {/* Suggestions */}
              <div className="space-y-1">
                {groupedSuggestions[type].map((suggestion, index) => (
                  <button
                    key={`${type}-${index}`}
                    onClick={() => onSelect(suggestion)}
                    className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 rounded-md transition-colors duration-150"
                  >
                    <div className="flex-shrink-0">
                      {getSuggestionIcon(suggestion.type || 'autocomplete')}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-900 truncate">
                          {highlightMatch(suggestion.text, suggestion.query)}
                        </p>
                        
                        {suggestion.count && (
                          <span className="text-xs text-gray-500 ml-2">
                            {suggestion.count} results
                          </span>
                        )}
                      </div>
                      
                      {suggestion.description && (
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {suggestion.description}
                        </p>
                      )}
                      
                      {suggestion.metadata && (
                        <div className="flex items-center space-x-2 mt-1">
                          {suggestion.metadata.price && (
                            <span className="text-xs text-green-600 font-medium">
                              GH₵{suggestion.metadata.price}
                            </span>
                          )}
                          
                          {suggestion.metadata.rating && (
                            <div className="flex items-center space-x-1">
                              <span className="text-xs text-yellow-500">★</span>
                              <span className="text-xs text-gray-500">
                                {suggestion.metadata.rating}
                              </span>
                            </div>
                          )}
                          
                          {suggestion.metadata.location && (
                            <span className="text-xs text-gray-500">
                              📍 {suggestion.metadata.location}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {suggestion.trending && (
                      <div className="flex-shrink-0">
                        <SparklesIcon className="h-4 w-4 text-yellow-500" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 px-3 py-2 bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Press Enter to search</span>
          <div className="flex items-center space-x-2">
            <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">↑</kbd>
            <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">↓</kbd>
            <span>to navigate</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SearchSuggestions;
