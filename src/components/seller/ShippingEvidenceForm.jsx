import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  TruckIcon,
  CameraIcon,
  XCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../../hooks/useAuth';
import { externalTransactionAPI } from '../../services/apiServices';
import LoadingSpinner from '../helpers/LoadingSpinner';

function ShippingEvidenceForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [uploadedImages, setUploadedImages] = useState([]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      tracking_number: '',
      shipping_carrier: '',
      shipping_method: '',
      estimated_delivery: '',
      shipping_cost: '',
      notes: '',
      package_weight: '',
      package_dimensions: ''
    }
  });

  useEffect(() => {
    fetchTransaction();
  }, [id]);

  const fetchTransaction = async () => {
    try {
      setLoading(true);
      const response = await externalTransactionAPI.getTransaction(id);
      
      if (response.data.success) {
        const txn = response.data.transaction;
        
        // Verify this is the seller and transaction is in correct state
        if (txn.seller_id !== user?.id) {
          throw new Error('You are not authorized to submit shipping details for this transaction');
        }
        
        if (txn.status !== 'funded') {
          throw new Error('This transaction is not ready for shipping');
        }
        
        setTransaction(txn);
        setError(null);
      } else {
        throw new Error(response.data.error || 'Transaction not found');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const shippingCarriers = [
    { value: 'dhl', label: 'DHL Express' },
    { value: 'fedex', label: 'FedEx' },
    { value: 'ups', label: 'UPS' },
    { value: 'ghana_post', label: 'Ghana Post' },
    { value: 'speedaf', label: 'SpeedAF' },
    { value: 'aramex', label: 'Aramex' },
    { value: 'tnt', label: 'TNT' },
    { value: 'other', label: 'Other' }
  ];

  const shippingMethods = [
    { value: 'standard', label: 'Standard Shipping (5-7 days)' },
    { value: 'express', label: 'Express Shipping (2-3 days)' },
    { value: 'overnight', label: 'Overnight Shipping (1 day)' },
    { value: 'economy', label: 'Economy Shipping (7-14 days)' },
    { value: 'pickup', label: 'Local Pickup' }
  ];

  const handleImageUpload = (event) => {
    const files = Array.from(event.target.files);
    const maxFiles = 10;
    
    if (uploadedImages.length + files.length > maxFiles) {
      toast.error(`You can only upload up to ${maxFiles} images`);
      return;
    }

    files.forEach(file => {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImages(prev => [...prev, {
          file,
          preview: e.target.result,
          name: file.name
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (formData) => {
    try {
      setSubmitting(true);

      // Validate required fields
      if (!formData.tracking_number) {
        toast.error('Tracking number is required');
        return;
      }

      if (uploadedImages.length === 0) {
        toast.error('Please upload at least one shipping evidence photo');
        return;
      }

      // Prepare form data for submission
      const submitData = new FormData();
      
      // Add form fields
      Object.keys(formData).forEach(key => {
        if (formData[key]) {
          submitData.append(key, formData[key]);
        }
      });

      // Add images
      uploadedImages.forEach((image, index) => {
        submitData.append(`shipping_images[${index}]`, image.file);
      });

      const response = await externalTransactionAPI.submitShippingEvidence(transaction.id, submitData);

      if (response.data.success) {
        toast.success('Shipping details submitted successfully!');
        navigate(`/transactions/${transaction.id}`);
      } else {
        throw new Error(response.data.error || 'Failed to submit shipping details');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to submit shipping details');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Unable to load transaction</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return null;
  }

  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <TruckIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    Submit Shipping Details
                  </h1>
                  <p className="text-sm text-gray-600">
                    Provide shipping information and evidence for the buyer
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Transaction Summary */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-3">Transaction Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Product:</span>
                <span className="ml-2 font-medium text-gray-900">{transaction.product_name}</span>
              </div>
              <div>
                <span className="text-gray-600">Buyer:</span>
                <span className="ml-2 font-medium text-gray-900">
                  {transaction.buyer?.first_name} {transaction.buyer?.last_name}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Amount:</span>
                <span className="ml-2 font-medium text-gray-900">GH₵ {transaction.total_amount?.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>
                <span className="ml-2 font-medium text-green-600">Escrow Funded</span>
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-blue-900 mb-2">Shipping Address</h3>
            <div className="text-sm text-blue-800">
              <p>{transaction.shipping_address?.street}</p>
              <p>{transaction.shipping_address?.city}, {transaction.shipping_address?.region}</p>
              <p>{transaction.shipping_address?.country}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Tracking Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tracking Number *
                </label>
                <input
                  type="text"
                  {...register('tracking_number', { required: 'Tracking number is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter tracking number"
                />
                {errors.tracking_number && (
                  <p className="mt-1 text-sm text-red-600">{errors.tracking_number.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Shipping Carrier *
                </label>
                <select
                  {...register('shipping_carrier', { required: 'Please select a carrier' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select carrier...</option>
                  {shippingCarriers.map((carrier) => (
                    <option key={carrier.value} value={carrier.value}>
                      {carrier.label}
                    </option>
                  ))}
                </select>
                {errors.shipping_carrier && (
                  <p className="mt-1 text-sm text-red-600">{errors.shipping_carrier.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Shipping Method
                </label>
                <select
                  {...register('shipping_method')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select method...</option>
                  {shippingMethods.map((method) => (
                    <option key={method.value} value={method.value}>
                      {method.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Delivery Date
                </label>
                <input
                  type="date"
                  {...register('estimated_delivery')}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Package Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Shipping Cost (GH₵)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('shipping_cost')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Package Weight (kg)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  {...register('package_weight')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dimensions (L×W×H cm)
                </label>
                <input
                  type="text"
                  {...register('package_dimensions')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., 30×20×10"
                />
              </div>
            </div>

            {/* Shipping Evidence Photos */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shipping Evidence Photos *
              </label>
              <p className="text-xs text-gray-500 mb-3">
                Upload photos of the packaged item, shipping label, and receipt. Maximum 10 images, 5MB each.
              </p>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="shipping-images"
                />
                <label
                  htmlFor="shipping-images"
                  className="cursor-pointer flex flex-col items-center justify-center"
                >
                  <CameraIcon className="h-12 w-12 text-gray-400 mb-4" />
                  <span className="text-sm font-medium text-gray-900">Upload shipping photos</span>
                  <span className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 5MB each</span>
                </label>
              </div>

              {/* Image Previews */}
              {uploadedImages.length > 0 && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  {uploadedImages.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.preview}
                        alt={`Shipping evidence ${index + 1}`}
                        className="w-full h-24 object-cover rounded-md border border-gray-200"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <XCircleIcon className="h-4 w-4" />
                      </button>
                      <p className="text-xs text-gray-500 mt-1 truncate">{image.name}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Additional Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <textarea
                {...register('notes')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Any additional information about the shipment, special handling instructions, or notes for the buyer..."
              />
            </div>

            {/* Important Notice */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3 mt-0.5" />
                <div className="text-sm">
                  <h4 className="font-medium text-yellow-800 mb-1">Important</h4>
                  <ul className="text-yellow-700 space-y-1">
                    <li>• Ensure all shipping information is accurate</li>
                    <li>• Upload clear photos of the package and shipping label</li>
                    <li>• The buyer will be notified once you submit this form</li>
                    <li>• You cannot edit shipping details after submission</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => navigate(`/transactions/${transaction.id}`)}
                disabled={submitting}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {submitting ? (
                  <>
                    <LoadingSpinner size="small" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                    Submit Shipping Details
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default ShippingEvidenceForm;
