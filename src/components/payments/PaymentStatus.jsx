import React from 'react';
import { Link } from 'react-router-dom';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

function PaymentStatus({ payment, showDetails = true, className = '' }) {
  if (!payment) {
    return null;
  }

  // Status configurations
  const statusConfig = {
    success: {
      icon: CheckCircleIcon,
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
      borderColor: 'border-green-200',
      label: 'Payment Successful'
    },
    pending: {
      icon: ClockIcon,
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200',
      label: 'Payment Pending'
    },
    failed: {
      icon: XCircleIcon,
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-800',
      borderColor: 'border-red-200',
      label: 'Payment Failed'
    },
    cancelled: {
      icon: XCircleIcon,
      color: 'gray',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-800',
      borderColor: 'border-gray-200',
      label: 'Payment Cancelled'
    },
    refunded: {
      icon: ExclamationTriangleIcon,
      color: 'blue',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200',
      label: 'Payment Refunded'
    }
  };

  const status = statusConfig[payment.status] || statusConfig.pending;
  const StatusIcon = status.icon;

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency
  const formatCurrency = (amount, currency = 'GHS') => {
    if (typeof amount !== 'number') return 'N/A';
    const symbol = currency === 'GHS' ? 'GH₵' : currency === 'USD' ? '$' : currency;
    return `${symbol} ${amount.toFixed(2)}`;
  };

  // Get payment method display
  const getPaymentMethodDisplay = (method) => {
    const methods = {
      'card': 'Debit/Credit Card',
      'bank_transfer': 'Bank Transfer',
      'mobile_money': 'Mobile Money',
      'wallet': 'Wallet Balance'
    };
    return methods[method] || method;
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className={`px-4 py-3 border-b border-gray-200 ${status.bgColor}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <StatusIcon className={`h-5 w-5 ${status.textColor}`} />
            <div>
              <h3 className={`font-medium ${status.textColor}`}>
                {status.label}
              </h3>
              <p className={`text-sm ${status.textColor} opacity-75`}>
                Reference: {payment.reference}
              </p>
            </div>
          </div>
          
          {/* Amount */}
          <div className="text-right">
            <p className={`font-semibold ${status.textColor}`}>
              {formatCurrency(payment.amount, payment.currency)}
            </p>
            <p className={`text-sm ${status.textColor} opacity-75`}>
              {payment.currency}
            </p>
          </div>
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <div className="p-4 space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <CurrencyDollarIcon className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-600">Payment Method:</span>
                <span className="ml-2 font-medium text-gray-900">
                  {getPaymentMethodDisplay(payment.payment_method)}
                </span>
              </div>
              
              <div className="flex items-center text-sm">
                <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-600">Created:</span>
                <span className="ml-2 font-medium text-gray-900">
                  {formatDate(payment.created_at)}
                </span>
              </div>
              
              {payment.paid_at && (
                <div className="flex items-center text-sm">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-gray-600">Paid:</span>
                  <span className="ml-2 font-medium text-gray-900">
                    {formatDate(payment.paid_at)}
                  </span>
                </div>
              )}
            </div>

            <div className="space-y-3">
              {payment.gateway_response && (
                <div className="flex items-start text-sm">
                  <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-gray-600">Gateway Response:</span>
                    <p className="font-medium text-gray-900 mt-1">
                      {payment.gateway_response}
                    </p>
                  </div>
                </div>
              )}
              
              {payment.channel && (
                <div className="flex items-center text-sm">
                  <span className="text-gray-600">Channel:</span>
                  <span className="ml-2 font-medium text-gray-900 capitalize">
                    {payment.channel}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Transaction Details */}
          {payment.metadata && (
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Transaction Details</h4>
              <div className="space-y-2">
                {payment.metadata.transaction_id && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Transaction ID:</span>
                    <Link
                      to={`/transactions/${payment.metadata.transaction_id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      {payment.metadata.transaction_id}
                    </Link>
                  </div>
                )}
                
                {payment.metadata.order_id && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Order ID:</span>
                    <Link
                      to={`/orders/${payment.metadata.order_id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      {payment.metadata.order_id}
                    </Link>
                  </div>
                )}
                
                {payment.metadata.transaction_type && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium text-gray-900 capitalize">
                      {payment.metadata.transaction_type.replace('_', ' ')}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Fee Breakdown */}
          {(payment.fees || payment.gateway_fee) && (
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Fee Breakdown</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium text-gray-900">
                    {formatCurrency(payment.amount - (payment.fees || 0), payment.currency)}
                  </span>
                </div>
                
                {payment.gateway_fee && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Gateway Fee:</span>
                    <span className="font-medium text-gray-900">
                      {formatCurrency(payment.gateway_fee, payment.currency)}
                    </span>
                  </div>
                )}
                
                {payment.fees && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Platform Fee:</span>
                    <span className="font-medium text-gray-900">
                      {formatCurrency(payment.fees, payment.currency)}
                    </span>
                  </div>
                )}
                
                <div className="border-t border-gray-200 pt-1 flex justify-between font-medium">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-gray-900">
                    {formatCurrency(payment.amount, payment.currency)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Status-specific Messages */}
          {payment.status === 'pending' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="flex items-start">
                <ClockIcon className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Payment Processing</p>
                  <p className="text-yellow-700 mt-1">
                    Your payment is being processed. This may take a few minutes depending on your payment method.
                  </p>
                </div>
              </div>
            </div>
          )}

          {payment.status === 'failed' && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-start">
                <XCircleIcon className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-red-800">Payment Failed</p>
                  <p className="text-red-700 mt-1">
                    {payment.gateway_response || 'Your payment could not be processed. Please try again or contact support.'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {payment.status === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <div className="flex items-start">
                <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-green-800">Payment Successful</p>
                  <p className="text-green-700 mt-1">
                    Your payment has been processed successfully and your account has been updated.
                  </p>
                </div>
              </div>
            </div>
          )}

          {payment.status === 'refunded' && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800">Payment Refunded</p>
                  <p className="text-blue-700 mt-1">
                    This payment has been refunded. The amount will be credited back to your original payment method within 3-5 business days.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default PaymentStatus;
