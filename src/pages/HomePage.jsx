import { Link } from 'react-router-dom';
import {
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ChatBubbleLeftRightIcon,
  StarIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';
import useAuth from '../hooks/useAuth';

function HomePage() {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: ShieldCheckIcon,
      title: 'Secure Escrow Protection',
      description: 'Your money is safely held in escrow until you confirm delivery. Buy with confidence from any platform.',
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Fair 5% Fee',
      description: 'Simple, transparent pricing. Only 5% fee on transactions, waived for premium subscribers.',
    },
    {
      icon: ChatBubbleLeftRightIcon,
      title: 'Built-in Communication',
      description: 'Chat directly with buyers and sellers. Keep all transaction communication in one place.',
    },
    {
      icon: StarIcon,
      title: 'Rating & Review System',
      description: 'Build trust with our comprehensive rating system. See seller reputation before you buy.',
    },
  ];

  const howItWorks = [
    {
      step: '1',
      title: 'Create Transaction',
      description: 'Start a transaction for any product from Facebook, TikTok, or our internal store.',
    },
    {
      step: '2',
      title: 'Secure Payment',
      description: 'Your payment is held safely in escrow until you receive and approve your order.',
    },
    {
      step: '3',
      title: 'Receive & Confirm',
      description: 'Once you receive your item and are satisfied, confirm delivery to release payment.',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Shop Safely with
              <span className="text-yellow-400"> PayHold</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              The secure escrow platform for safe online transactions.
              Buy from Facebook, TikTok, or our marketplace with complete protection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {!isAuthenticated ? (
                <>
                  <Link
                    to="/register"
                    className="bg-yellow-400 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
                  >
                    Get Started Free
                  </Link>
                  <Link
                    to="/login"
                    className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors"
                  >
                    Sign In
                  </Link>
                </>
              ) : (
                <Link
                  to="/dashboard"
                  className="bg-yellow-400 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors inline-flex items-center"
                >
                  Go to Dashboard
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose PayHold?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We provide the security and tools you need for safe online transactions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature) => (
              <div key={feature.title} className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How PayHold Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Simple, secure, and transparent process for all your transactions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {howItWorks.map((step) => (
              <div key={step.title} className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Shop Safely?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of users who trust PayHold for their online transactions
          </p>
          {!isAuthenticated && (
            <Link
              to="/register"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              Start Your First Transaction
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          )}
        </div>
      </section>
    </div>
  );
}

export default HomePage;
