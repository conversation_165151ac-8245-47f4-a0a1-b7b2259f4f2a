import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  ExclamationTriangleIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { disputeAPI } from '../../services/apiServices';
import { useRoleAccess } from '../auth/RoleBasedAccess';
import DisputeList from './DisputeList';
import DisputeDetails from './DisputeDetails';
import DisputeForm from './DisputeForm';
import DisputeResolution from './DisputeResolution';
import logger from '../helpers/logger';

function DisputeDashboard({ className = '' }) {
  const { user } = useSelector((state) => state.auth);
  const { hasPermission } = useRoleAccess();
  
  const [activeView, setActiveView] = useState('list'); // 'list', 'details', 'create', 'resolve'
  const [selectedDispute, setSelectedDispute] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (hasPermission('dispute.moderate')) {
      fetchDisputeStats();
    }
  }, [hasPermission]);

  const fetchDisputeStats = async () => {
    try {
      setLoading(true);
      const response = await disputeAPI.getDisputeStats();
      
      if (response.data.success) {
        setStats(response.data.stats);
      }
    } catch (error) {
      logger.error('Failed to fetch dispute stats:', error);
      toast.error('Failed to load dispute statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectDispute = (dispute) => {
    setSelectedDispute(dispute);
    setActiveView('details');
  };

  const handleCreateDispute = () => {
    setActiveView('create');
  };

  const handleDisputeCreated = (dispute) => {
    setSelectedDispute(dispute);
    setActiveView('details');
    toast.success('Dispute created successfully');
  };

  const handleDisputeUpdated = (dispute) => {
    setSelectedDispute(dispute);
    if (hasPermission('dispute.moderate')) {
      fetchDisputeStats();
    }
  };

  const handleResolveDispute = () => {
    setActiveView('resolve');
  };

  const handleBackToList = () => {
    setActiveView('list');
    setSelectedDispute(null);
  };

  const getStatCard = (title, value, icon, color = 'blue') => {
    const Icon = icon;
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      green: 'bg-green-50 text-green-600',
      red: 'bg-red-50 text-red-600'
    };

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center">
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {hasPermission('dispute.moderate') ? 'Dispute Management' : 'My Disputes'}
            </h1>
            <p className="text-gray-600 mt-1">
              {hasPermission('dispute.moderate') 
                ? 'Manage and resolve user disputes'
                : 'View and manage your disputes'
              }
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-3">
          {activeView !== 'list' && (
            <button
              onClick={handleBackToList}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Back to List
            </button>
          )}
          
          {activeView === 'details' && hasPermission('dispute.resolve') && 
           selectedDispute?.status !== 'resolved' && selectedDispute?.status !== 'closed' && (
            <button
              onClick={handleResolveDispute}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              Resolve Dispute
            </button>
          )}
          
          {activeView === 'list' && !hasPermission('dispute.moderate') && (
            <button
              onClick={handleCreateDispute}
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Dispute
            </button>
          )}
        </div>
      </div>

      {/* Statistics (for moderators) */}
      {hasPermission('dispute.moderate') && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getStatCard(
            'Total Disputes',
            stats.total_disputes || 0,
            ChartBarIcon,
            'blue'
          )}
          {getStatCard(
            'Open Disputes',
            stats.open_disputes || 0,
            ClockIcon,
            'yellow'
          )}
          {getStatCard(
            'Resolved This Month',
            stats.resolved_this_month || 0,
            CheckCircleIcon,
            'green'
          )}
          {getStatCard(
            'Average Resolution Time',
            stats.avg_resolution_time ? `${stats.avg_resolution_time} days` : 'N/A',
            XCircleIcon,
            'red'
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {activeView === 'list' && (
          <DisputeList
            onSelectDispute={handleSelectDispute}
          />
        )}

        {activeView === 'details' && selectedDispute && (
          <DisputeDetails
            disputeId={selectedDispute.id}
            onUpdate={handleDisputeUpdated}
          />
        )}

        {activeView === 'create' && (
          <DisputeForm
            onSuccess={handleDisputeCreated}
            onCancel={handleBackToList}
          />
        )}

        {activeView === 'resolve' && selectedDispute && (
          <DisputeResolution
            dispute={selectedDispute}
            onResolution={handleDisputeUpdated}
          />
        )}
      </div>

      {/* Recent Activity (for moderators) */}
      {hasPermission('dispute.moderate') && activeView === 'list' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          
          <div className="space-y-4">
            {stats?.recent_activity?.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {activity.type === 'created' && (
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                  )}
                  {activity.type === 'resolved' && (
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  )}
                  {activity.type === 'updated' && (
                    <ClockIcon className="h-5 w-5 text-blue-500" />
                  )}
                </div>
                
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(activity.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            )) || (
              <p className="text-gray-500 text-center py-4">No recent activity</p>
            )}
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="h-6 w-6 text-blue-600 mt-0.5" />
          <div>
            <h3 className="text-lg font-medium text-blue-900">Dispute Guidelines</h3>
            <div className="text-sm text-blue-800 mt-2 space-y-2">
              {hasPermission('dispute.moderate') ? (
                <ul className="list-disc list-inside space-y-1">
                  <li>Review all evidence carefully before making a decision</li>
                  <li>Communicate clearly with both parties</li>
                  <li>Document your reasoning in resolution notes</li>
                  <li>Escalate complex cases to senior moderators</li>
                  <li>Follow up on resolutions that require action</li>
                </ul>
              ) : (
                <ul className="list-disc list-inside space-y-1">
                  <li>Provide detailed descriptions and evidence</li>
                  <li>Be honest and accurate in your claims</li>
                  <li>Respond promptly to moderator requests</li>
                  <li>Try to resolve issues directly with the other party first</li>
                  <li>False disputes may result in account penalties</li>
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DisputeDashboard;
