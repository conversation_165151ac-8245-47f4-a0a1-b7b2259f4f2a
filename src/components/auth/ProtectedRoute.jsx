import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { useRoleAccess } from './RoleBasedAccess';
import { ShieldExclamationIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../helpers/LoadingSpinner';

function ProtectedRoute({
  children,
  allowedRoles = [], // Legacy support
  roles = [],
  permissions = [],
  requireAll = false,
  fallbackPath = '/login',
  showAccessDenied = true
}) {
  const { isAuthenticated, loading } = useSelector((state) => state.auth);
  const { hasRole, hasPermission, hasAnyPermission, hasAllPermissions } = useRoleAccess();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Merge legacy allowedRoles with new roles prop for backward compatibility
  const allRoles = [...allowedRoles, ...roles];

  // Check role-based access
  let hasAccess = true;

  // Check roles if specified
  if (allRoles.length > 0) {
    hasAccess = allRoles.some(role => hasRole(role));
  }

  // Check permissions if specified
  if (permissions.length > 0 && hasAccess) {
    hasAccess = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }

  // Show access denied page if user doesn't have required access
  if (!hasAccess) {
    if (showAccessDenied) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <ShieldExclamationIcon className="mx-auto h-16 w-16 text-red-500 mb-6" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-6">
              You don't have the required permissions to access this page.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.history.back()}
                className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
              >
                Go Back
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="w-full px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        </div>
      );
    } else {
      // Redirect to fallback path
      return <Navigate to={fallbackPath} replace />;
    }
  }

  return children;
}

// Convenience components for common access patterns
export const AdminRoute = ({ children, ...props }) => (
  <ProtectedRoute roles={['admin', 'super_admin']} {...props}>
    {children}
  </ProtectedRoute>
);

export const ModeratorRoute = ({ children, ...props }) => (
  <ProtectedRoute roles={['moderator', 'admin', 'super_admin']} {...props}>
    {children}
  </ProtectedRoute>
);

export const SellerRoute = ({ children, ...props }) => (
  <ProtectedRoute roles={['seller', 'premium_seller', 'moderator', 'admin', 'super_admin']} {...props}>
    {children}
  </ProtectedRoute>
);

export const PremiumRoute = ({ children, ...props }) => (
  <ProtectedRoute roles={['premium_seller', 'moderator', 'admin', 'super_admin']} {...props}>
    {children}
  </ProtectedRoute>
);

ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  allowedRoles: PropTypes.arrayOf(PropTypes.string),
  roles: PropTypes.arrayOf(PropTypes.string),
  permissions: PropTypes.arrayOf(PropTypes.string),
  requireAll: PropTypes.bool,
  fallbackPath: PropTypes.string,
  showAccessDenied: PropTypes.bool,
};

export default ProtectedRoute;
