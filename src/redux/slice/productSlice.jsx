import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { productAPI } from '../../services/apiServices';

// Async thunks
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters = {}, { rejectWithValue }) => {
    try {
      const response = await productAPI.getProducts(filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch products');
    }
  }
);

export const fetchProduct = createAsyncThunk(
  'products/fetchProduct',
  async (productId, { rejectWithValue }) => {
    try {
      const response = await productAPI.getProduct(productId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch product');
    }
  }
);

export const createProduct = createAsyncThunk(
  'products/createProduct',
  async (productData, { rejectWithValue }) => {
    try {
      const response = await productAPI.createProduct(productData);
      toast.success('Product created successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create product';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateProduct = createAsyncThunk(
  'products/updateProduct',
  async ({ id, productData }, { rejectWithValue }) => {
    try {
      const response = await productAPI.updateProduct(id, productData);
      toast.success('Product updated successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update product';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const deleteProduct = createAsyncThunk(
  'products/deleteProduct',
  async (productId, { rejectWithValue }) => {
    try {
      await productAPI.deleteProduct(productId);
      toast.success('Product deleted successfully!');
      return productId;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete product';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await productAPI.getCategories();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch categories');
    }
  }
);

export const fetchFeaturedProducts = createAsyncThunk(
  'products/fetchFeaturedProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await productAPI.getFeatured();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch featured products');
    }
  }
);

export const uploadProductImages = createAsyncThunk(
  'products/uploadImages',
  async ({ productId, formData }, { rejectWithValue }) => {
    try {
      const response = await productAPI.uploadImages(productId, formData);
      toast.success('Images uploaded successfully!');
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to upload images';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

const initialState = {
  // Product lists
  products: [],
  featuredProducts: [],
  categories: [],
  
  // Single product
  currentProduct: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    per_page: 20
  },
  
  // Filters
  filters: {
    search: '',
    category: '',
    min_price: '',
    max_price: '',
    sort: 'newest'
  },
  
  // Loading states
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isUploadingImages: false,
  
  // Error states
  error: null,
  createError: null,
  updateError: null,
  deleteError: null,
  uploadError: null
};

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.createError = null;
      state.updateError = null;
      state.deleteError = null;
      state.uploadError = null;
    },
    
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
    },
    
    updateProductInList: (state, action) => {
      const updatedProduct = action.payload;
      const index = state.products.findIndex(p => p.id === updatedProduct.id);
      if (index !== -1) {
        state.products[index] = updatedProduct;
      }
    }
  },
  
  extraReducers: (builder) => {
    // Fetch Products
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload.products || [];
        state.pagination = action.payload.pagination || initialState.pagination;
        state.error = null;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Fetch Single Product
    builder
      .addCase(fetchProduct.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload.product;
        state.error = null;
      })
      .addCase(fetchProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Create Product
    builder
      .addCase(createProduct.pending, (state) => {
        state.isCreating = true;
        state.createError = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.isCreating = false;
        state.products.unshift(action.payload.product);
        state.createError = null;
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.isCreating = false;
        state.createError = action.payload;
      });

    // Update Product
    builder
      .addCase(updateProduct.pending, (state) => {
        state.isUpdating = true;
        state.updateError = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.isUpdating = false;
        const updatedProduct = action.payload.product;
        
        // Update in products list
        const index = state.products.findIndex(p => p.id === updatedProduct.id);
        if (index !== -1) {
          state.products[index] = updatedProduct;
        }
        
        // Update current product if it's the same
        if (state.currentProduct?.id === updatedProduct.id) {
          state.currentProduct = updatedProduct;
        }
        
        state.updateError = null;
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.isUpdating = false;
        state.updateError = action.payload;
      });

    // Delete Product
    builder
      .addCase(deleteProduct.pending, (state) => {
        state.isDeleting = true;
        state.deleteError = null;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.isDeleting = false;
        const productId = action.payload;
        state.products = state.products.filter(p => p.id !== productId);
        
        // Clear current product if it's the deleted one
        if (state.currentProduct?.id === productId) {
          state.currentProduct = null;
        }
        
        state.deleteError = null;
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.isDeleting = false;
        state.deleteError = action.payload;
      });

    // Fetch Categories
    builder
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload.categories || [];
      });

    // Fetch Featured Products
    builder
      .addCase(fetchFeaturedProducts.fulfilled, (state, action) => {
        state.featuredProducts = action.payload.products || [];
      });

    // Upload Images
    builder
      .addCase(uploadProductImages.pending, (state) => {
        state.isUploadingImages = true;
        state.uploadError = null;
      })
      .addCase(uploadProductImages.fulfilled, (state, action) => {
        state.isUploadingImages = false;
        const { product_id, images } = action.payload;
        
        // Update current product images
        if (state.currentProduct?.id === product_id) {
          state.currentProduct.images = images;
        }
        
        // Update product in list
        const index = state.products.findIndex(p => p.id === product_id);
        if (index !== -1) {
          state.products[index].images = images;
        }
        
        state.uploadError = null;
      })
      .addCase(uploadProductImages.rejected, (state, action) => {
        state.isUploadingImages = false;
        state.uploadError = action.payload;
      });
  }
});

export const {
  clearError,
  setFilters,
  clearFilters,
  clearCurrentProduct,
  updateProductInList
} = productSlice.actions;

export default productSlice.reducer;
