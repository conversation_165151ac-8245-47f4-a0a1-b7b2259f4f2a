import PropTypes from 'prop-types';
import LoadingSpinner from './LoadingSpinner';

function Button({ children, isLoading = false, disabled = false }) {
  return (
    <button
      type="button"
      disabled={disabled || isLoading}
    >
      {isLoading ? (
        <>
          <LoadingSpinner />
          <span>Loading...</span>
        </>
      ) : (
        children
      )}
    </button>
  );
}

Button.propTypes = {
  children: PropTypes.node.isRequired,
  isLoading: PropTypes.bool.isRequired,
  disabled: PropTypes.bool.isRequired,
};

export default Button;
