import React, { useState, useRef } from 'react';
import { toast } from 'react-toastify';
import {
  PhotoIcon,
  XMarkIcon,
  ArrowUpTrayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

function ImageUpload({ 
  images = [], 
  onImagesChange, 
  maxImages = 8, 
  maxFileSize = 5 * 1024 * 1024, // 5MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  showPreview = true,
  allowReorder = true,
  className = ''
}) {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const fileInputRef = useRef(null);

  const validateFile = (file) => {
    if (!acceptedTypes.includes(file.type)) {
      throw new Error(`File type ${file.type} is not supported. Please use: ${acceptedTypes.join(', ')}`);
    }
    
    if (file.size > maxFileSize) {
      throw new Error(`File size must be less than ${Math.round(maxFileSize / (1024 * 1024))}MB`);
    }
    
    return true;
  };

  const processFiles = async (files) => {
    const fileArray = Array.from(files);
    
    if (images.length + fileArray.length > maxImages) {
      toast.error(`You can only upload up to ${maxImages} images`);
      return;
    }

    setUploading(true);
    const newImages = [];

    try {
      for (const file of fileArray) {
        validateFile(file);
        
        // Create preview URL
        const preview = URL.createObjectURL(file);
        
        // Create image object
        const imageObj = {
          id: Date.now() + Math.random(), // Temporary ID
          file,
          preview,
          name: file.name,
          size: file.size,
          type: file.type,
          is_primary: images.length === 0 && newImages.length === 0, // First image is primary
          uploaded: false
        };
        
        newImages.push(imageObj);
      }
      
      // Update images array
      onImagesChange([...images, ...newImages]);
      
      toast.success(`${newImages.length} image${newImages.length > 1 ? 's' : ''} added successfully`);
    } catch (error) {
      toast.error(error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setDragOver(false);
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setDragOver(false);
  };

  const removeImage = (imageId) => {
    const updatedImages = images.filter(img => img.id !== imageId);
    
    // If we removed the primary image, make the first remaining image primary
    if (updatedImages.length > 0) {
      const hadPrimary = images.find(img => img.id === imageId)?.is_primary;
      if (hadPrimary && !updatedImages.some(img => img.is_primary)) {
        updatedImages[0].is_primary = true;
      }
    }
    
    onImagesChange(updatedImages);
    
    // Clean up preview URL if it exists
    const removedImage = images.find(img => img.id === imageId);
    if (removedImage?.preview && removedImage.file) {
      URL.revokeObjectURL(removedImage.preview);
    }
  };

  const setPrimaryImage = (imageId) => {
    const updatedImages = images.map(img => ({
      ...img,
      is_primary: img.id === imageId
    }));
    onImagesChange(updatedImages);
  };

  const moveImage = (fromIndex, toIndex) => {
    if (!allowReorder) return;
    
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    onImagesChange(updatedImages);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          dragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="space-y-2">
          {uploading ? (
            <>
              <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-blue-500 animate-pulse" />
              <p className="text-sm text-blue-600">Uploading images...</p>
            </>
          ) : (
            <>
              <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')} up to {Math.round(maxFileSize / (1024 * 1024))}MB each
                </p>
                <p className="text-xs text-gray-500">
                  Maximum {maxImages} images ({maxImages - images.length} remaining)
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Image Previews */}
      {images.length > 0 && showPreview && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">
              Uploaded Images ({images.length}/{maxImages})
            </h3>
            {images.length > 1 && allowReorder && (
              <p className="text-xs text-gray-500">
                Drag to reorder
              </p>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div
                key={image.id}
                className="relative group bg-white border border-gray-200 rounded-lg overflow-hidden"
                draggable={allowReorder}
                onDragStart={(e) => e.dataTransfer.setData('text/plain', index.toString())}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => {
                  e.preventDefault();
                  const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
                  moveImage(fromIndex, index);
                }}
              >
                {/* Image */}
                <div className="aspect-square relative">
                  <img
                    src={image.preview || image.url}
                    alt={image.name}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Primary Badge */}
                  {image.is_primary && (
                    <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded flex items-center">
                      <StarIconSolid className="h-3 w-3 mr-1" />
                      Primary
                    </div>
                  )}
                  
                  {/* Upload Status */}
                  {image.uploaded === false && (
                    <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                      Pending
                    </div>
                  )}
                  
                  {/* Overlay Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setPreviewImage(image);
                        }}
                        className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100"
                        title="Preview"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      
                      {!image.is_primary && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setPrimaryImage(image.id);
                          }}
                          className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100"
                          title="Set as primary"
                        >
                          <StarIcon className="h-4 w-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(image.id);
                        }}
                        className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                        title="Remove"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* Image Info */}
                <div className="p-2">
                  <p className="text-xs text-gray-900 truncate" title={image.name}>
                    {image.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(image.size)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <XMarkIcon className="h-8 w-8" />
            </button>
            
            <img
              src={previewImage.preview || previewImage.url}
              alt={previewImage.name}
              className="max-w-full max-h-full object-contain"
            />
            
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded">
              <p className="text-sm font-medium">{previewImage.name}</p>
              <p className="text-xs">{formatFileSize(previewImage.size)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
        <div className="flex items-start">
          <ExclamationTriangleIcon className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-1">Image Guidelines</h4>
            <ul className="space-y-1 text-xs">
              <li>• Use high-quality images for better sales</li>
              <li>• First image will be used as the primary product image</li>
              <li>• Show different angles and details of your product</li>
              <li>• Avoid watermarks or text overlays</li>
              <li>• Ensure good lighting and clear focus</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ImageUpload;
