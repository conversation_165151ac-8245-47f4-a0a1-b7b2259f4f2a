import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  HeartIcon,
  ShoppingCartIcon,
  EyeIcon,
  StarIcon,
  MapPinIcon,
  ShieldCheckIcon,
  TruckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid, StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';

function ProductGrid({ 
  products = [], 
  loading = false, 
  onAddToCart, 
  onToggleWishlist,
  viewMode = 'grid', // 'grid' or 'list'
  className = '' 
}) {
  const [wishlistedItems, setWishlistedItems] = useState(new Set());
  const [addingToCart, setAddingToCart] = useState(new Set());

  const handleAddToCart = async (product) => {
    if (addingToCart.has(product.id)) return;

    setAddingToCart(prev => new Set(prev).add(product.id));
    
    try {
      if (onAddToCart) {
        await onAddToCart(product);
      }
      toast.success('Added to cart');
    } catch (error) {
      toast.error('Failed to add to cart');
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev);
        newSet.delete(product.id);
        return newSet;
      });
    }
  };

  const handleToggleWishlist = async (product) => {
    const isWishlisted = wishlistedItems.has(product.id);
    
    try {
      if (onToggleWishlist) {
        await onToggleWishlist(product.id, !isWishlisted);
      }
      
      setWishlistedItems(prev => {
        const newSet = new Set(prev);
        if (isWishlisted) {
          newSet.delete(product.id);
        } else {
          newSet.add(product.id);
        }
        return newSet;
      });
      
      toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
    } catch (error) {
      toast.error('Failed to update wishlist');
    }
  };

  const formatPrice = (price) => {
    return `GH₵ ${price.toLocaleString()}`;
  };

  const formatRating = (rating, reviewCount) => {
    if (!rating) return null;
    
    return (
      <div className="flex items-center space-x-1">
        <div className="flex items-center">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIconSolid
              key={star}
              className={`h-3 w-3 ${
                star <= Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        <span className="text-xs text-gray-600">
          {rating.toFixed(1)} ({reviewCount || 0})
        </span>
      </div>
    );
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'new':
        return 'bg-green-100 text-green-800';
      case 'like_new':
        return 'bg-blue-100 text-blue-800';
      case 'good':
        return 'bg-yellow-100 text-yellow-800';
      case 'fair':
        return 'bg-orange-100 text-orange-800';
      case 'poor':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const ProductCard = ({ product }) => {
    const isWishlisted = wishlistedItems.has(product.id);
    const isAddingToCart = addingToCart.has(product.id);
    const isOutOfStock = product.stock_quantity === 0;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow group">
        {/* Product Image */}
        <div className="relative aspect-square">
          <Link to={`/products/${product.id}`}>
            <img
              src={product.primary_image?.url || '/placeholder-product.jpg'}
              alt={product.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </Link>
          
          {/* Overlay Actions */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex items-center space-x-2">
              <Link
                to={`/products/${product.id}`}
                className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 shadow-md"
                title="View details"
              >
                <EyeIcon className="h-4 w-4" />
              </Link>
              
              <button
                onClick={() => handleToggleWishlist(product)}
                className={`p-2 rounded-full shadow-md ${
                  isWishlisted 
                    ? 'bg-red-500 text-white hover:bg-red-600' 
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
                title={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
              >
                {isWishlisted ? (
                  <HeartIconSolid className="h-4 w-4" />
                ) : (
                  <HeartIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col space-y-1">
            {product.condition && product.condition !== 'new' && (
              <span className={`text-xs px-2 py-1 rounded-full font-medium ${getConditionColor(product.condition)}`}>
                {product.condition.replace('_', ' ').toUpperCase()}
              </span>
            )}
            
            {product.is_featured && (
              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Featured
              </span>
            )}
            
            {isOutOfStock && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Out of Stock
              </span>
            )}
          </div>

          {/* Discount Badge */}
          {product.original_price && product.original_price > product.price && (
            <div className="absolute top-2 right-2">
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                -{Math.round(((product.original_price - product.price) / product.original_price) * 100)}%
              </span>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          {/* Title */}
          <Link
            to={`/products/${product.id}`}
            className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2 mb-2"
          >
            {product.name}
          </Link>

          {/* Rating */}
          {product.rating && (
            <div className="mb-2">
              {formatRating(product.rating, product.review_count)}
            </div>
          )}

          {/* Price */}
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg font-bold text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.original_price && product.original_price > product.price && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.original_price)}
              </span>
            )}
          </div>

          {/* Seller Info */}
          <div className="flex items-center space-x-2 mb-3">
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <span>By {product.seller?.store_name || product.seller?.username}</span>
              {product.seller?.is_verified && (
                <ShieldCheckIcon className="h-3 w-3 text-green-500" />
              )}
            </div>
          </div>

          {/* Features */}
          <div className="flex items-center space-x-3 mb-3 text-xs text-gray-500">
            {product.shipping_cost === 0 && (
              <div className="flex items-center space-x-1">
                <TruckIcon className="h-3 w-3" />
                <span>Free Shipping</span>
              </div>
            )}
            
            {product.location && (
              <div className="flex items-center space-x-1">
                <MapPinIcon className="h-3 w-3" />
                <span>{product.location}</span>
              </div>
            )}
          </div>

          {/* Add to Cart Button */}
          <button
            onClick={() => handleAddToCart(product)}
            disabled={isOutOfStock || isAddingToCart}
            className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAddingToCart ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Adding...
              </>
            ) : isOutOfStock ? (
              'Out of Stock'
            ) : (
              <>
                <ShoppingCartIcon className="h-4 w-4 mr-2" />
                Add to Cart
              </>
            )}
          </button>
        </div>
      </div>
    );
  };

  const ProductListItem = ({ product }) => {
    const isWishlisted = wishlistedItems.has(product.id);
    const isAddingToCart = addingToCart.has(product.id);
    const isOutOfStock = product.stock_quantity === 0;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
        <div className="flex items-start space-x-4">
          {/* Product Image */}
          <div className="flex-shrink-0">
            <Link to={`/products/${product.id}`}>
              <img
                src={product.primary_image?.url || '/placeholder-product.jpg'}
                alt={product.name}
                className="h-24 w-24 object-cover rounded-md"
              />
            </Link>
          </div>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link
                  to={`/products/${product.id}`}
                  className="text-lg font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
                >
                  {product.name}
                </Link>
                
                {product.rating && (
                  <div className="mt-1">
                    {formatRating(product.rating, product.review_count)}
                  </div>
                )}

                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span>By {product.seller?.store_name || product.seller?.username}</span>
                  {product.seller?.is_verified && (
                    <div className="flex items-center space-x-1">
                      <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                      <span>Verified</span>
                    </div>
                  )}
                  {product.location && (
                    <div className="flex items-center space-x-1">
                      <MapPinIcon className="h-4 w-4" />
                      <span>{product.location}</span>
                    </div>
                  )}
                </div>

                <div className="mt-2 flex items-center space-x-3 text-xs text-gray-500">
                  {product.shipping_cost === 0 && (
                    <div className="flex items-center space-x-1">
                      <TruckIcon className="h-3 w-3" />
                      <span>Free Shipping</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="h-3 w-3" />
                    <span>3-7 days delivery</span>
                  </div>
                </div>
              </div>

              {/* Price and Actions */}
              <div className="text-right ml-4">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </span>
                  {product.original_price && product.original_price > product.price && (
                    <span className="text-sm text-gray-500 line-through">
                      {formatPrice(product.original_price)}
                    </span>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleWishlist(product)}
                    className={`p-2 rounded-md ${
                      isWishlisted 
                        ? 'text-red-500 bg-red-50 hover:bg-red-100' 
                        : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
                    }`}
                    title={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
                  >
                    {isWishlisted ? (
                      <HeartIconSolid className="h-5 w-5" />
                    ) : (
                      <HeartIcon className="h-5 w-5" />
                    )}
                  </button>

                  <button
                    onClick={() => handleAddToCart(product)}
                    disabled={isOutOfStock || isAddingToCart}
                    className="flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isAddingToCart ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Adding...
                      </>
                    ) : isOutOfStock ? (
                      'Out of Stock'
                    ) : (
                      <>
                        <ShoppingCartIcon className="h-4 w-4 mr-2" />
                        Add to Cart
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
          : 'space-y-4'
        }>
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse">
              <div className="aspect-square bg-gray-200"></div>
              <div className="p-4 space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p className="text-gray-500">Try adjusting your search or filters to find what you're looking for.</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
        : 'space-y-4'
      }>
        {products.map((product) => (
          viewMode === 'grid' ? (
            <ProductCard key={product.id} product={product} />
          ) : (
            <ProductListItem key={product.id} product={product} />
          )
        ))}
      </div>
    </div>
  );
}

export default ProductGrid;
